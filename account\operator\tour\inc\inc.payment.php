<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);
include '../../../../connection/dbconnect.php';
include 'inc.function.php';
if ($_SESSION['loginStatus'] != "isLogin") {
    header("Location: ../logout.php");
    exit;
} else {
    // Handle payment method selection
    if ($_SERVER["REQUEST_METHOD"] === "GET" && isset($_GET['id']) && isset($_GET['method'])) {
        try {
            // Check if user is logged in
            if (!isset($_SESSION['id'])) {
                throw new Exception("You must be logged in to perform this action.");
            }

            // Retrieve and validate booking id and payment method
            $getBookingId = $_GET['id'] ?? null;
            $paymentMethod = $_GET['method'] ?? null;

            if (!$getBookingId || !is_numeric($getBookingId)) {
                throw new Exception("Invalid booking ID.");
            }

            if (!in_array($paymentMethod, ['Over_the_Counter', 'Bank_Transfer'])) {
                throw new Exception("Invalid payment method selected.");
            }

            $bookingDetails = getBookingDetails($pdo, $getBookingId);
            if (!$bookingDetails) {
                throw new Exception("Booking does not exist or is invalid.");
            }

            // Begin database transaction
            $pdo->beginTransaction();

            // Update payment method in the database
            $stmt = $pdo->prepare("UPDATE cb_payments SET payment_method = :payment_method WHERE booking_id = :booking_id");
            $stmt->execute([
                ':payment_method' => $paymentMethod,
                ':booking_id' => $getBookingId
            ]);

            // Commit the transaction
            $pdo->commit();

            // Redirect back to the payment page with the selected method
            header("Location: ../payment-tdf.php?id=" . urlencode($getBookingId) . "&method=" . urlencode($paymentMethod));
            exit();
        } catch (Exception $e) {
            // Roll back transaction if active
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../payment-tdf.php?id=" . urlencode($getBookingId));
            exit();
        }
    }

    // Handle receipt upload
    if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['uploadReceiptBtn'])) {
        try {
            // Check if user is logged in
            if (!isset($_SESSION['id'])) {
                throw new Exception("You must be logged in to perform this action.");
            }

            // Validate CSRF token
            if (
                !isset($_POST['csrf_token']) ||
                !isset($_SESSION['csrf_token']) ||
                $_POST['csrf_token'] !== $_SESSION['csrf_token']
            ) {
                throw new Exception("Invalid CSRF token.");
            }
            // Optionally, unset or regenerate the CSRF token after it's used.
            unset($_SESSION['csrf_token']);

            // Retrieve and validate booking id and reference number
            $getBookingId    = $_POST['booking_id'] ?? null;
            $referenceNumber = $_POST['referenceNumber'] ?? null;
            $paymentMethod   = $_POST['paymentMethod'] ?? 'Over_the_Counter'; // Default to Over the Counter if not specified

            if (!$getBookingId || !is_numeric($getBookingId)) {
                throw new Exception("Invalid booking ID.");
            }

            $pattern = '/^CB-\d{4}-[A-Z0-9]{6}-[A-Z0-9]{4}$/';
            if (!preg_match($pattern, $referenceNumber)) {
                throw new Exception("Invalid reference number format.");
            }

            if (!in_array($paymentMethod, ['Over_the_Counter', 'Bank_Transfer'])) {
                throw new Exception("Invalid payment method selected.");
            }

            $bookingDetails = getBookingDetails($pdo, $getBookingId);
            if (!$bookingDetails) {
                throw new Exception("Booking does not exist or is invalid.");
            }

            $getReferenceNumber = $bookingDetails['referenceNum'];
            if ($getReferenceNumber !== $referenceNumber) {
                throw new Exception("Invalid reference number. Please try again.");
            }

            // Validate file upload
            if (!isset($_FILES['uploadReceipt']) || $_FILES['uploadReceipt']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('File upload error.');
            }

            // Process the file upload
            $fileTmpPath      = $_FILES['uploadReceipt']['tmp_name'];
            $originalFileName = $_FILES['uploadReceipt']['name'];

            // Sanitize file name to prevent security issues
            $fileName = preg_replace("/[^A-Za-z0-9\.\-_]/", '', basename($originalFileName));

            // Validate allowed file extensions
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'pdf']; // Adjust allowed types as needed
            $fileExtension     = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            if (!in_array($fileExtension, $allowedExtensions)) {
                throw new Exception("File type not allowed.");
            }

            // Define the upload directory relative to this script
            $uploadDir = __DIR__ . '/../../receipt/';
            if (!is_dir($uploadDir)) {
                if (!mkdir($uploadDir, 0755, true)) {
                    throw new Exception("Failed to create folders...");
                }
            }

            // Set destination path. Optionally, you can append a timestamp or unique id to avoid collisions.
            $destPath = $uploadDir . $fileName;
            if (!move_uploaded_file($fileTmpPath, $destPath)) {
                throw new Exception("Error moving the uploaded file.");
            }

            // Begin database transaction
            $pdo->beginTransaction();

            $setApprovalStatus = "Pending";

            // Update payment record with the uploaded receipt image name and payment method
            $stmt = $pdo->prepare("UPDATE cb_payments SET receipt_image = :receiptImage, payment_method = :payment_method WHERE booking_id = :booking_id");
            $stmt->execute([
                ':receiptImage' => $fileName,
                ':payment_method' => $paymentMethod,
                ':booking_id'   => $getBookingId
            ]);

            // Update Treasurer Approval State
            $stmt = $pdo->prepare("UPDATE cb_booking_approvals SET mtho = :mtho WHERE booking_id = :booking_id");
            $stmt->execute([
                ':mtho'     => $setApprovalStatus,
                ':booking_id' => $getBookingId
            ]);

            // Commit the transaction
            $pdo->commit();

            $_SESSION['success'] = "Payment Receipt has been uploaded";
            header("Location: ../transaction-pending.php");
            exit();
        } catch (Exception $e) {
            // Roll back transaction if active
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../payment-tdf.php?id=" . urlencode($getBookingId));
            exit();
        }
    }
}
