<?php
ob_start();
require '_header.php';

$getBookingId = isset($_GET['id']) ? (int) $_GET['id'] : 0;


if (empty($getBookingId)) {
    header("Location: transaction-draft.php");
    exit;
}

$bookingDetails = getBookingDetails2($pdo, $getBookingId);

if (!empty($bookingDetails)) {
    $getId = $_SESSION['id'] ?? null; // Define $id properly

    if ($bookingDetails['boat_operator_id'] != $getId) {
        header("Location: transaction-pending.php");
        exit;
    } elseif ($bookingDetails['boat'] === "Approved") {
        header("Location: transaction-pending.php");
        exit;
    } else {
        $extname = $bookingDetails['extname'] ?? '';
        $middlename = $bookingDetails['middlename'] ?? '';
        $operatorName = trim($bookingDetails['firstname'] . ' ' . $middlename . ' ' . $bookingDetails['lastname'] . ' ' . $extname);
        $designationTour = $bookingDetails['tour_operator_designation'];
        $resortName = $bookingDetails['resort_operator_designation'];
        $referenceNumber = $bookingDetails['referenceNum'];
        $boatName = $bookingDetails['boatName'];
        $portName = $bookingDetails['portName'];
        $checkIn = $bookingDetails['check_in_date'];
        $checkout = $bookingDetails['check_out_date'];
        $adultCount = $bookingDetails['total_adults'];
        $childrenCount = $bookingDetails['total_children'];
    }
}

$disContactPassenger = getDistinctTourist($pdo, "contact_number", $getBookingId);
$disAddressPassenger = getDistinctTourist($pdo, "address", $getBookingId);

// Get Count from inserted tourist info
$counts = getCrewCount($pdo, $getBookingId);
$actual_crewCount = $counts['crew'];

if ($actual_crewCount === 0) {
    $paymentBtn = 'onclick="return false;"';
} else {
    $paymentBtn = "";
}

ob_end_flush();
?>

<div class="p-4 sm:ml-64">
    <div class="border p-4 rounded-lg shadow-lg bg-white mt-14">

        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Pending</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Passenger Info</span>
                    </div>
                </li>
            </ol>
        </nav>


        <!-- Status Messages -->
         <div class="mt-4">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="flex p-4 mb-4 text-green-800 rounded-lg bg-green-50 border border-green-200" role="alert">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-600"></i>
                    </div>
                    <div class="ml-3 text-sm font-medium text-green-800">
                        <?= htmlspecialchars($_SESSION['success']); ?>
                    </div>
                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-green-50 text-green-500 rounded-lg focus:ring-2 focus:ring-green-400 p-1.5 hover:bg-green-200 inline-flex items-center justify-center h-8 w-8" data-dismiss-target="[role='alert']" aria-label="Close">
                        <span class="sr-only">Close</span>
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                    </button>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="flex p-4 mb-4 text-red-800 rounded-lg bg-red-50 border border-red-200" role="alert">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-600"></i>
                    </div>
                    <div class="ml-3 text-sm font-medium text-red-800">
                        <?= htmlspecialchars($_SESSION['error']); ?>
                    </div>
                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-200 inline-flex items-center justify-center h-8 w-8" data-dismiss-target="[role='alert']" aria-label="Close">
                        <span class="sr-only">Close</span>
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                    </button>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
        </div>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-blue-100 p-2 rounded-lg mr-3">
                <i class="fas fa-users text-blue-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-xl font-bold text-gray-900">Crew Information</h2>
                <p class="text-sm text-gray-600">Add Boat Crew Information Here</p>
            </div>
        </div>
                <!-- Booking Summary Card -->
        <div class="mt-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 p-4 shadow-sm">
            <div class="flex items-center mb-3">
                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                <h3 class="text-md font-semibold text-blue-800">Booking Summary</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-white rounded-lg p-3 shadow-sm border border-blue-100">
                    <p class="text-xs font-medium text-gray-500 uppercase">Tour Operator</p>
                    <p class="text-sm font-semibold text-gray-800"><?= htmlspecialchars($operatorName, ENT_QUOTES, 'UTF-8'); ?></p>
                </div>
                <div class="bg-white rounded-lg p-3 shadow-sm border border-blue-100">
                    <p class="text-xs font-medium text-gray-500 uppercase">Destination</p>
                    <p class="text-sm font-semibold text-gray-800"><?= htmlspecialchars($resortName, ENT_QUOTES, 'UTF-8'); ?></p>
                </div>
                 <div class="bg-white rounded-lg p-3 shadow-sm border border-blue-100">
                    <p class="text-xs font-medium text-gray-500 uppercase">Passenger</p>
                    <p class="text-sm font-semibold text-gray-800"><?= htmlspecialchars($adultCount, ENT_QUOTES, 'UTF-8'); ?> Adults, <?= htmlspecialchars($childrenCount, ENT_QUOTES, 'UTF-8'); ?> Children</p>
                </div>
                <div class="bg-white rounded-lg p-3 shadow-sm border border-blue-100">
                    <p class="text-xs font-medium text-gray-500 uppercase">Travel Dates</p>
                    <p class="text-sm font-semibold text-gray-800"><?= (new DateTime(htmlspecialchars($checkIn, ENT_QUOTES, 'UTF-8')))->format('M d') ?> - <?= (new DateTime(htmlspecialchars($checkout, ENT_QUOTES, 'UTF-8')))->format('M d, Y') ?></p>
                </div>
            </div>
        </div>

        <!-- Passenger Count Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <!-- Crew Section -->
            <div class="bg-white border border-amber-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="flex items-center">
                            <h3 class="text-sm font-semibold text-amber-700">Crew</h3>
                            <span class="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded-full">Required</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Boat operators (min: 1)</p>
                    </div>
                    <div class="bg-amber-100 p-2 rounded-full">
                        <i class="fas fa-ship text-amber-600"></i>
                    </div>
                </div>
                <div class="mt-3 flex items-end">
                    <span class="text-2xl font-bold text-amber-700"><?= $actual_crewCount; ?></span>
                    <span class="text-sm text-gray-500 ml-1 mb-1">/ 4</span>
                    <div class="ml-auto">
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-amber-600 h-2.5 rounded-full" style="width: <?= ($actual_crewCount / 4) * 100 ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Empty columns to maintain grid layout -->
            <div class="hidden md:block"></div>
            <div class="hidden md:block"></div>
        </div>


        <!-- Add Passenger Button -->
        <div class="mt-6 flex justify-between items-center">
            <h3 class="text-md font-semibold text-gray-700">Crew List</h3>
            <button
                data-modal-target="addPassenger-modal"
                data-modal-toggle="addPassenger-modal"
                type="button"
                class="inline-flex items-center justify-center space-x-2 px-5 py-2.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 shadow-sm hover:shadow-md transition-all duration-300">
                <i class="fas fa-user-plus mr-2"></i>
                <span>Add Crew Member</span>
            </button>

            <!-- Main modal -->
            <div id="addPassenger-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-7xl max-h-full">
                    <!-- Modal content -->
                    <div class="relative bg-white rounded-lg shadow-lg">
                        <!-- Modal header -->
                        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t border-gray-200 bg-gradient-to-r from-blue-600 to-blue-700">
                            <div class="flex items-center">
                                <i class="fas fa-user-plus text-white mr-2"></i>
                                <h3 class="text-xl font-semibold text-white">
                                    Add New Crew Member
                                </h3>
                            </div>
                            <button type="button" class="text-white bg-blue-800 hover:bg-blue-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="addPassenger-modal">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>
                        <form action="inc/inc.passenger.php" method="POST">
                            <div class="p-4 md:p-5 space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Passenger Information Section -->
                                    <fieldset class="bg-gray-50 p-5 rounded-lg shadow-sm border border-gray-200">
                                        <legend class="px-2 text-lg font-bold text-blue-700 flex items-center">
                                            <i class="fas fa-id-card text-blue-500 mr-2"></i>
                                            Crew Information
                                        </legend>

                                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                        <input type="hidden" value="<?= $_GET['id']; ?>" name="bookingId">
                                        <input type="hidden" name="passengerType" value="crewMbca">
                                        <input type="hidden" name="region-country" value="Camarines Norte">
                                        <input type="hidden" name="citizenship" value="filipino">

                                        <!-- Full Name -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Full Name <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-user text-blue-500"></i>
                                                </div>
                                                <input type="text" name="fullName" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Enter full name" required>
                                            </div>
                                        </div>

                                        <!-- Common Address Fields -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Address <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-home text-blue-500"></i>
                                                </div>
                                                <input type="text" name="address" list="addressPassenger" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Enter complete address" required>
                                                <datalist id="addressPassenger">
                                                    <?php
                                                    foreach ($disAddressPassenger as $showAddressPassenger) {
                                                    ?>
                                                        <option value="<?php echo $showAddressPassenger['address']; ?>"><?php echo $showAddressPassenger['address']; ?></option>
                                                    <?php } ?>
                                                </datalist>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">Previously used addresses will appear as suggestions</p>
                                        </div>
                                    </fieldset>

                                    <!-- Additional Details Section -->
                                    <fieldset class="bg-gray-50 p-5 rounded-lg shadow-sm border border-gray-200">
                                        <legend class="px-2 text-lg font-bold text-blue-700 flex items-center">
                                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                            Additional Details
                                        </legend>

                                        <!-- Gender -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Gender <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-venus-mars text-blue-500"></i>
                                                </div>
                                                <select name="gender" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                                                    <option value="" disabled selected>Select gender...</option>
                                                    <option value="Male">Male</option>
                                                    <option value="Female">Female</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Age -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Age <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-birthday-cake text-blue-500"></i>
                                                </div>
                                                <input type="number" name="age" min="1" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Enter age" required>
                                            </div>
                                        </div>

                                        <!-- Contact Number -->
                                        <div class="mb-4">
                                            <label class="block mb-2 text-sm font-medium text-gray-700">
                                                Contact Number <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                    <i class="fas fa-phone text-blue-500"></i>
                                                </div>
                                                <input type="text" name="contactNumber" list="contactNumberPassenger" maxlength="11" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Enter contact number" required>
                                                <datalist id="contactNumberPassenger">
                                                    <?php
                                                    foreach ($disContactPassenger as $showContactPassenger) {
                                                    ?>
                                                        <option value="<?php echo $showContactPassenger['contact_number']; ?>"><?php echo $showContactPassenger['contact_number']; ?></option>
                                                    <?php } ?>
                                                </datalist>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">Previously used contact numbers will appear as suggestions</p>
                                        </div>

                                        <!-- Information Notice -->
                                        <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                            <div class="flex">
                                                <div class="flex-shrink-0">
                                                    <i class="fas fa-info-circle text-blue-500"></i>
                                                </div>
                                                <div class="ml-3">
                                                    <h3 class="text-sm font-medium text-blue-800">Important Information</h3>
                                                    <div class="mt-2 text-sm text-blue-700">
                                                        <ul class="list-disc pl-5 space-y-1">
                                                            <li>All fields marked with <span class="text-red-600">*</span> are required</li>
                                                            <li>Ensure contact information is accurate for emergency purposes</li>
                                                            <li>At least one crew member is required for the booking</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>

                            <!-- Form Buttons -->
                            <div class="flex justify-end items-center p-4 md:p-5 border-t border-gray-200 rounded-b bg-gray-50">
                                <button type="button" data-modal-hide="addPassenger-modal" class="py-2.5 px-5 mr-3 text-sm font-medium text-gray-900 bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-600 focus:outline-none focus:ring-4 focus:ring-gray-200 shadow-sm">
                                    <i class="fas fa-times mr-1"></i> Cancel
                                </button>
                                <button type="submit" name="registerPassenger" class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center shadow-sm hover:shadow-md transition-all duration-300">
                                    <i class="fas fa-save mr-1"></i> Register Crew Member
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <table id="search-table" class="w-full max-w-2xl mx-auto bg-white border border-gray-200 rounded-lg shadow-sm">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-3/4">Name</th>
                        <th class="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">Action</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php
                    try {
                        // SQL query to fetch booking details
                        $sql = " SELECT * FROM cb_tourists
                                WHERE booking_id = :booking_id
                                AND info_type = 'crewMbca'
                                ORDER BY info_type = 'crewMbca' DESC, full_name ASC";
                        // Prepare and execute the statement
                        $stmt = $pdo->prepare($sql);
                        $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_STR);
                        $stmt->execute();

                        // Fetch the results
                        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        if ($rows) {
                            foreach ($rows as $row) {
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-2 text-sm text-gray-700"><?= htmlspecialchars($row['full_name']); ?></td>
                                    <td class="px-4 py-2 text-center">
                                        <button data-modal-target="delete-passenger-modal-<?= $row['tourist_id']; ?>" data-modal-toggle="delete-passenger-modal-<?= $row['tourist_id']; ?>" type="button" class="inline-flex items-center justify-center bg-red-600 hover:bg-red-700 text-white font-medium rounded-md text-xs px-2 py-1">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3.5 h-3.5">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                                            </svg>
                                        </button>
                                    </td>
                                </tr>

                                <!-- Delete Confirmation Modal -->
                                <div id="delete-passenger-modal-<?= $row['tourist_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-md max-h-full">
                                        <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                            <!-- Modal header -->
                                            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t border-gray-200 bg-gradient-to-r from-red-600 to-red-700">
                                                <div class="flex items-center">
                                                    <i class="fas fa-trash-alt text-white mr-2"></i>
                                                    <h3 class="text-xl font-semibold text-white">
                                                        Delete Crew Member
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white bg-red-800 hover:bg-red-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="delete-passenger-modal-<?= $row['tourist_id']; ?>">
                                                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <form action="inc/inc.passenger.php" method="POST">
                                                <div class="p-4 md:p-5">
                                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                    <input type="hidden" name="passengerId" value="<?= $row['tourist_id']; ?>">
                                                    <input type="hidden" name="bookingId" value="<?= $row['booking_id']; ?>">

                                                    <!-- Warning Message -->
                                                    <div class="flex p-4 mb-4 text-red-800 rounded-lg bg-red-50 border border-red-200" role="alert">
                                                        <div class="flex-shrink-0">
                                                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                                                        </div>
                                                        <div class="ml-3 text-sm font-medium text-red-800">
                                                            Are you sure you want to delete this crew member? This action cannot be undone.
                                                        </div>
                                                    </div>

                                                    <!-- Passenger Info -->
                                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                                                        <div class="flex justify-between items-center mb-2">
                                                            <span class="text-sm font-medium text-gray-600">Name:</span>
                                                            <span class="text-sm font-bold text-gray-800"><?= htmlspecialchars($row['full_name']); ?></span>
                                                        </div>
                                                        <div class="flex justify-between items-center">
                                                            <span class="text-sm font-medium text-gray-600">Type:</span>
                                                            <span class="text-sm font-bold text-gray-800">Crew Member</span>
                                                        </div>
                                                    </div>

                                                    <!-- Action Buttons -->
                                                    <div class="flex justify-end space-x-3 mt-6">
                                                        <button data-modal-hide="delete-passenger-modal-<?= $row['tourist_id']; ?>" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 shadow-sm">
                                                            <i class="fas fa-times mr-1"></i> Cancel
                                                        </button>
                                                        <button type="submit" name="deletePassenger" class="text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center shadow-sm hover:shadow-md transition-all duration-300">
                                                            <i class="fas fa-trash-alt mr-1"></i> Delete Crew Member
                                                        </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="2" class="px-4 py-3 text-center text-sm text-red-600">
                                Error: <?= htmlspecialchars($e->getMessage()); ?>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
        <form action="inc/inc.passenger.php" method="POST">
            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
            <input type="hidden" value="<?= $_GET['id']; ?>" name="bookingId">
            <div class="flex flex-col md:flex-row justify-center md:justify-end mt-6 space-y-4 md:space-y-0 md:space-x-4">
                <?php
                // Check if there's at least one crew member
                if ($actual_crewCount === 0) {
                    $btnClass = "inline-flex items-center justify-center bg-gray-400 cursor-not-allowed text-white font-medium rounded-md text-sm p-3";
                    $tooltipAttr = 'title="Please add at least 1 crew member before proceeding"';
                } else {
                    $btnClass = "inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-sm p-3 shadow-sm hover:shadow-md transition-all duration-300";
                    $tooltipAttr = '';
                }
                ?>
                <button
                    name="crewPassengerBtn"
                    class="<?= $btnClass; ?>"
                    type="submit"
                    <?= $paymentBtn; ?>
                    <?= $tooltipAttr; ?>
                >
                    <i class="fas fa-check-circle mr-2"></i> Proceed
                </button>
            </div>
        </form>
    </div>
</div>
<?php
require '_footer.php';
?>