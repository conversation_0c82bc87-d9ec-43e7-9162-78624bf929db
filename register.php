<?php
session_start();
include 'connection/dbconnect.php';

if (empty($_SESSION['csrf_token']) || time() > $_SESSION['csrf_token_expiration']) {
  $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
  $_SESSION['csrf_token_expiration'] = time() + 3600; // 1-hour validity
}

$sql = 'SELECT status_maintenance FROM system';
$stmt = $pdo->prepare($sql);
$stmt->execute();
$row = $stmt->fetch(PDO::FETCH_ASSOC);
if (!$row) {
  $_SESSION['error'] = "Something went wrong, Try Again!";
  header("Location: login.php");
  exit;
}

if ($row['status_maintenance'] === 1) {
  $_SESSION['error'] = "System under maintenance, Try Again later";
  header("Location: login.php");
  exit;
}

?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
  <title>Registration Form</title>
</head>

<body class="bg-gray-50">
  <header class="bg-blue-600 text-white p-6 shadow-md">
    <div class="container mx-auto flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold">OPERATOR REGISTRATION</h1>
        <p class="text-sm">Municipal Tourism and Heritage Operations - Vinzons</p>
      </div>
      <a href="login.php" class="bg-white text-blue-600 px-4 py-2 rounded-md font-semibold hover:bg-blue-100 transition">
        Back to Login
      </a>
    </div>
  </header>

  <div class="container mx-auto p-6 bg-white rounded-lg shadow-md my-6 max-w-6xl">
    <main>
      <!-- Progress Steps -->
      <div class="mb-8">
        <ol class="flex items-center w-full">
          <li class="flex w-full items-center text-blue-600 after:content-[''] after:w-full after:h-1 after:border-b after:border-blue-100 after:border-4 after:inline-block">
            <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full lg:h-12 lg:w-12 shrink-0">
              <svg class="w-4 h-4 text-blue-600 lg:w-6 lg:h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
              </svg>
            </div>
            <span class="ml-2 text-sm font-medium text-blue-600">Personal Information</span>
          </li>
          <li class="flex items-center">
            <div class="flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full lg:h-12 lg:w-12 shrink-0">
              <svg class="w-4 h-4 text-gray-500 lg:w-6 lg:h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M18 7.5h-.423l-.452-1.09.5-.444a1.5 1.5 0 0 0 0-2.121L16.56 2.78a1.5 1.5 0 0 0-2.12 0l-.445.5L12.904.827A1.5 1.5 0 0 0 11.5.5h-3a1.5 1.5 0 0 0-1.404.827L6.644 2.78l-.444-.5a1.5 1.5 0 0 0-2.121 0L3.015 3.345a1.5 1.5 0 0 0 0 2.121l.5.444-.452 1.09H2.5A1.5 1.5 0 0 0 1 8.5v3A1.5 1.5 0 0 0 2.5 13h.423l.452 1.09-.5.444a1.5 1.5 0 0 0 0 2.121l1.065 1.065a1.5 1.5 0 0 0 2.12 0l.445-.5 1.09.452V18A1.5 1.5 0 0 0 8.5 19h3a1.5 1.5 0 0 0 1.404-.827l.452-1.09.444.5a1.5 1.5 0 0 0 2.121 0l1.065-1.065a1.5 1.5 0 0 0 0-2.121l-.5-.444.452-1.09H18a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 18 7.5ZM10 13a3 3 0 1 1 0-6 3 3 0 0 1 0 6Z"/>
              </svg>
            </div>
            <span class="ml-2 text-sm font-medium text-gray-500">Operator Details</span>
          </li>
        </ol>
      </div>

      <h2 class="text-3xl mb-6 text-gray-900 font-bold">Create Your Account</h2>
      <p class="text-gray-600 mb-8">Please fill in the information below to register as an operator.</p>

      <form action="inc.register.php" method="POST" class="grid grid-cols-1 gap-6" onsubmit="validateForm(event)">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Personal Details Section -->
          <fieldset class="border p-4 rounded-lg shadow-sm bg-white">
            <legend class="text-lg font-bold text-blue-600 px-2">Personal Details</legend>

            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="block">
                <label for="firstname" class="block mb-2 text-sm font-medium text-gray-700">
                  First Name <span class="text-red-600">*</span>
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                    <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                    </svg>
                  </div>
                  <input type="text" id="firstname" name="firstname" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" maxlength="20" minlength="5" placeholder="Enter your first name" required>
                </div>
              </div>

              <div class="block">
                <label for="middlename" class="block mb-2 text-sm font-medium text-gray-700">
                  Middle Name <span class="text-gray-400">(Optional)</span>
                </label>
                <input type="text" id="middlename" name="middlename" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" maxlength="20" minlength="5" placeholder="Enter your middle name">
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div class="block">
                <label for="lastname" class="block mb-2 text-sm font-medium text-gray-700">
                  Last Name <span class="text-red-600">*</span>
                </label>
                <input type="text" id="lastname" name="lastname" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" maxlength="20" minlength="3" placeholder="Enter your last name" required>
              </div>

              <div class="block">
                <label for="extname" class="block mb-2 text-sm font-medium text-gray-700">
                  Extension Name <span class="text-gray-400">(Optional)</span>
                </label>
                <input type="text" id="extname" name="extname" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" maxlength="5" minlength="1" placeholder="e.g., Jr, II, III">
              </div>
            </div>

            <div class="mt-4">
              <label for="address" class="block mb-2 text-sm font-medium text-gray-700">
                Address <span class="text-red-600">*</span>
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z" />
                  </svg>
                </div>
                <input type="text" id="address" name="address" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" maxlength="50" minlength="8" placeholder="Enter your complete address" required>
              </div>
            </div>

            <div class="mt-4">
              <label for="phone" class="block mb-2 text-sm font-medium text-gray-700">
                Contact No. <span class="text-red-600">*</span>
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z" />
                  </svg>
                </div>
                <input type="text" id="phone" name="contact" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" maxlength="11" placeholder="Enter your contact number" oninput="enforceNumericInput('phone')" required>
              </div>
              <p class="mt-1 text-xs text-gray-500">Format: 09XXXXXXXXX</p>
            </div>
          </fieldset>

          <!-- Account Details Section -->
          <fieldset class="border p-4 rounded-lg shadow-sm bg-white">
            <legend class="text-lg font-bold text-blue-600 px-2">Account Details</legend>

            <div class="mb-4">
              <label for="operatorType" class="block mb-2 text-sm font-medium text-gray-700">
                Select Operator Type <span class="text-red-600">*</span>
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                  </svg>
                </div>
                <select id="operatorType" name="operatorType" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" required>
                  <option value="" disabled selected>Select operator type...</option>
                  <option value="Tour operator">Tour operator</option>
                  <option value="Resort operator">Resort operator</option>
                  <option value="Boat operator">Boat operator</option>
                </select>
              </div>
            </div>

            <div class="mb-4">
              <label for="username" class="block mb-2 text-sm font-medium text-gray-700">
                Username <span class="text-red-600">*</span>
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                  </svg>
                </div>
                <input type="text" id="username" name="username" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" maxlength="15" minlength="5" placeholder="Choose a username" required>
              </div>
              <p class="mt-1 text-xs text-gray-500">Username must be 5-15 characters long</p>
            </div>

            <div class="mb-4">
              <label for="password" class="block mb-2 text-sm font-medium text-gray-700">
                Password <span class="text-red-600">*</span>
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                  </svg>
                </div>
                <input type="password" id="password" name="password" class="password-field bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" maxlength="20" minlength="8" placeholder="Choose a password" required onkeyup="checkPasswordStrength()">
              </div>
              <div id="password-strength" class="mt-2 h-2 rounded-full overflow-hidden">
                <div id="password-strength-bar" class="h-full w-0 transition-all duration-300 ease-in-out bg-red-500"></div>
              </div>
              <p id="password-strength-text" class="mt-1 text-xs text-gray-500">Password must be at least 8 characters long</p>
            </div>

            <div class="mb-4">
              <label for="confirm_password" class="block mb-2 text-sm font-medium text-gray-700">
                Verify Password <span class="text-red-600">*</span>
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                  </svg>
                </div>
                <input type="password" id="confirm_password" name="confirm_password" class="password-field bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" maxlength="20" minlength="8" placeholder="Re-enter your password" required>
              </div>
            </div>

            <div class="flex items-center mb-4">
              <input type="checkbox" id="show-password" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" onclick="togglePasswordVisibility()">
              <label for="show-password" class="ms-2 text-sm font-medium text-gray-700">Show Password</label>
            </div>

            <!-- Password Alert -->
            <div id="password-alert" class="hidden text-red-600 bg-red-100 border border-red-400 p-3 rounded-lg mb-4"></div>

            <div class="mb-4">
              <label for="email" class="block mb-2 text-sm font-medium text-gray-700">
                Email Address <span class="text-red-600">*</span>
              </label>
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                  </svg>
                </div>
                <input type="email" id="email" name="email" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" maxlength="50" minlength="5" placeholder="Enter your email address" required>
              </div>
            </div>

            <div class="mb-4">
              <label for="captcha" class="block mb-2 text-sm font-medium text-gray-700">
                CAPTCHA <span class="text-red-600">*</span>
              </label>
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-3">
                <p class="text-xl font-semibold text-gray-900 text-center py-2" id="captcha-question"></p>
              </div>
              <div class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 15.75l-2.489-2.489m0 0a3.375 3.375 0 10-4.773-4.773 3.375 3.375 0 004.774 4.774zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <input type="text" id="captcha" name="captcha" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" placeholder="Solve the CAPTCHA" required>
                <input type="hidden" name="captcha_answer" id="captcha-answer">
              </div>
              <!-- CAPTCHA Alert -->
              <div id="captcha-alert" class="hidden text-red-600 bg-red-100 border border-red-400 p-3 rounded-lg mt-2"></div>
            </div>
          </fieldset>
        </div>

        <div class="mt-8">
          <h3 class="text-lg font-semibold mb-3 text-blue-600">Terms and Conditions</h3>
          <div class="bg-blue-50 p-5 border border-blue-200 rounded-lg shadow-sm">
            <p class="text-sm text-gray-700 mb-4">
              By registering on our website, you agree to the following terms and conditions:
            </p>
            <ul class="text-sm text-gray-700 space-y-3 mb-4">
              <li class="flex items-start">
                <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span><strong>Accurate Information:</strong> You certify that all information provided during the registration process is true and correct to the best of your knowledge.</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span><strong>No False Accounts:</strong> You shall not create false or misleading accounts on our website.</span>
              </li>
              <li class="flex items-start">
                <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span><strong>Credential Protection:</strong> You are responsible for keeping your registration credentials (including username and password) confidential and agree not to share them with anyone else.</span>
              </li>
            </ul>
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-yellow-700">
                    <strong>DISCLAIMER:</strong> In accordance with R.A. 10173 or the Data Privacy Act, all collected information will be treated with utmost confidentiality and will not be subjected to public disclosure.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="flex items-center mt-4">
            <input type="checkbox" id="accept-terms" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" required>
            <label for="accept-terms" class="ms-2 text-sm font-medium text-gray-700">I Accept the Terms and Conditions</label>
          </div>
        </div>

        <div class="flex flex-col md:flex-row justify-between mt-8 space-y-4 md:space-y-0">
          <a href="login.php" class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-blue-600 bg-white border border-blue-600 rounded-lg hover:bg-blue-50 focus:ring-4 focus:ring-blue-300">
            <svg class="w-5 h-5 mr-2 -ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
            </svg>
            Back to Login
          </a>
          <button type="submit" name="registerBtn" class="inline-flex items-center justify-center px-5 py-3 text-base font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300">
            <svg class="w-5 h-5 mr-2 -ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M19 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM4 19.235v-.11a6.375 6.375 0 0112.75 0v.109A12.318 12.318 0 0110.374 21c-2.331 0-4.512-.645-6.374-1.766z" />
            </svg>
            Register Account
          </button>
        </div>
      </form>
    </main>
  </div>

  <footer class="bg-blue-600 text-white p-6 mt-8 shadow-inner">
    <div class="container mx-auto text-center">
      <p class="text-sm">Copyright © 2025 Municipality of Vinzons, Camarines Norte | All Rights Reserved.</p>
      <p class="text-xs mt-2">Municipal Tourism and Heritage Operations</p>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
  <script src="components/js/register.js"></script>

  <?php if (isset($_SESSION['error'])): ?>
    <script>
      Swal.fire({
        icon: 'error',
        title: 'Registration Error',
        text: '<?php echo htmlspecialchars($_SESSION['error'], ENT_QUOTES, 'UTF-8'); ?>',
        confirmButtonColor: '#3b82f6',
      });
    </script>
    <?php unset($_SESSION['error']); ?>
  <?php endif; ?>

  <script>
    // Password strength checker
    function checkPasswordStrength() {
      const password = document.getElementById('password').value;
      const strengthBar = document.getElementById('password-strength-bar');
      const strengthText = document.getElementById('password-strength-text');

      // Default - no password
      if (password.length === 0) {
        strengthBar.style.width = '0%';
        strengthBar.className = 'h-full w-0 transition-all duration-300 ease-in-out bg-red-500';
        strengthText.textContent = 'Password must be at least 8 characters long';
        strengthText.className = 'mt-1 text-xs text-gray-500';
        return;
      }

      // Calculate strength
      let strength = 0;

      // Length check
      if (password.length >= 8) strength += 25;

      // Character type checks
      if (password.match(/[a-z]+/)) strength += 15;
      if (password.match(/[A-Z]+/)) strength += 20;
      if (password.match(/[0-9]+/)) strength += 20;
      if (password.match(/[^a-zA-Z0-9]+/)) strength += 20;

      // Update the strength bar
      strengthBar.style.width = strength + '%';

      // Update colors and text based on strength
      if (strength < 40) {
        strengthBar.className = 'h-full transition-all duration-300 ease-in-out bg-red-500';
        strengthText.textContent = 'Weak password';
        strengthText.className = 'mt-1 text-xs text-red-500';
      } else if (strength < 70) {
        strengthBar.className = 'h-full transition-all duration-300 ease-in-out bg-yellow-500';
        strengthText.textContent = 'Moderate password';
        strengthText.className = 'mt-1 text-xs text-yellow-600';
      } else {
        strengthBar.className = 'h-full transition-all duration-300 ease-in-out bg-green-500';
        strengthText.textContent = 'Strong password';
        strengthText.className = 'mt-1 text-xs text-green-600';
      }
    }
  </script>
</body>

</html>