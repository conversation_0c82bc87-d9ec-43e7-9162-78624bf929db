<?php
require '_header.php';
?>

<div class="p-4 sm:ml-64">
    <div class="border p-4 rounded-lg shadow-lg bg-white mt-14">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Transactions</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Approved</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-green-100 p-3 rounded-lg mr-4">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Approved Bookings</h2>
                <p class="text-sm text-gray-600 mt-1">Bookings that you have approved</p>
            </div>
        </div>
        <!-- Table -->
        <div class="mt-4 overflow-hidden">
            <div class="overflow-x-auto rounded-lg">
                <table id="search-table" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-responsive">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Tour Operator</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Destination</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                        </tr>
                    </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php
                    try {
                        $getBoatStatus = "Approved";

                        $bookingDetails = getBookingDetails($pdo, $getBoatStatus, $id);

                        if ($bookingDetails) {
                            foreach ($bookingDetails as $row) {
                                $operatorName = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                                $extname = $row['extname'] ?? ''; // Use null coalescing operator for defaults
                                $middlename = $row['middlename'] ?? '';
                                $designationTour = $row['tour_operator_designation'];
                                $resortName = $row['resort_operator_designation'];
                                $referenceNumber = $row['referenceNum'];
                                $boatName = $row['boatName'];
                                $portName = $row['portName'];
                                $checkIn = $row['check_in_date'];
                                $checkout = $row['check_out_date'];
                                $adultCount = $row['total_adults'];
                                $childrenCount = $row['total_children'];
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= $operatorName; ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500 text-center"><?= $row['resort_operator_designation']; ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-center">
                                        <button data-modal-target="view-booking-modal-<?= $row['booking_id']; ?>" data-modal-toggle="view-booking-modal-<?= $row['booking_id']; ?>" type="button" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md text-xs px-2 sm:px-3 py-1.5 w-full sm:w-auto">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                            </svg>
                                            <span class="whitespace-nowrap">View Details</span>
                                        </button>
                                    </td>
                                </tr>
                                <!-- View Booking Modal -->
                                <div id="view-booking-modal-<?= $row['booking_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative w-full max-w-2xl max-h-full">
                                        <!-- Modal Container -->
                                        <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                            <!-- Modal Header -->
                                            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-600 to-green-800 rounded-t-lg">
                                                <div class="flex items-center">
                                                    <i class="fas fa-ship text-white mr-2"></i>
                                                    <h3 class="text-lg font-semibold text-white">
                                                        Approved Boat Booking
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white hover:text-gray-200 rounded-full w-7 h-7 flex justify-center items-center transition-colors duration-200 bg-green-800 hover:bg-green-900" data-modal-hide="view-booking-modal-<?= $row['booking_id']; ?>">
                                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <!-- Reference Number Banner -->
                                            <div class="bg-green-50 p-3 border-b border-green-100">
                                                <div class="flex justify-between items-center">
                                                    <div class="flex items-center">
                                                        <i class="fas fa-hashtag text-green-600 mr-2"></i>
                                                        <span class="text-sm font-medium text-green-800">Reference Number:</span>
                                                    </div>
                                                    <span class="text-sm font-bold text-green-600"><?= $referenceNumber; ?></span>
                                                </div>
                                            </div>

                                            <!-- Modal Body -->
                                            <div class="p-4 bg-white">
                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                    <!-- Operator Information Section -->
                                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                                        <div class="flex items-center mb-3">
                                                            <i class="fas fa-user-tie text-green-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Operator Information</h4>
                                                        </div>
                                                        <div class="space-y-3">
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-user text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Tour Operator:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $operatorName; ?></p>
                                                            </div>
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-building text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Designation:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $designationTour; ?></p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Destination Information Section -->
                                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                                        <div class="flex items-center mb-3">
                                                            <i class="fas fa-map-marked-alt text-green-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Destination Information</h4>
                                                        </div>
                                                        <div class="space-y-3">
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-hotel text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Resort Destination:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $resortName; ?></p>
                                                            </div>
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-map-marker-alt text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Place of Departure:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $portName; ?></p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Boat Information Section -->
                                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                                        <div class="flex items-center mb-3">
                                                            <i class="fas fa-ship text-green-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Boat Information</h4>
                                                        </div>
                                                        <div class="space-y-3">
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-ship text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Boat to be Used:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $boatName; ?></p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Schedule Information Section -->
                                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                                        <div class="flex items-center mb-3">
                                                            <i class="fas fa-calendar-alt text-green-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Schedule Information</h4>
                                                        </div>
                                                        <div class="space-y-3">
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-calendar-check text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Check-in Date:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= (new DateTime($checkIn))->format('F d, Y'); ?></p>
                                                            </div>
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-calendar-minus text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Check-out Date:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= (new DateTime($checkout))->format('F d, Y'); ?></p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Passenger Information Section -->
                                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 md:col-span-2">
                                                        <div class="flex items-center mb-3">
                                                            <i class="fas fa-users text-green-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-800">Passenger Information</h4>
                                                        </div>
                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-user text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Number of Adults:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $adultCount; ?></p>
                                                            </div>
                                                            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-child text-gray-500 mr-2 text-xs"></i>
                                                                    <p class="text-xs font-medium text-gray-700">Number of Children:</p>
                                                                </div>
                                                                <p class="text-xs text-gray-600 mt-1 pl-5"><?= $childrenCount; ?></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Modal Footer -->
                                            <div class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                                <button data-modal-hide="view-booking-modal-<?= $row['booking_id']; ?>" type="button" class="flex items-center py-2 px-4 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-200">
                                                    <i class="fas fa-times-circle mr-1.5"></i> Close
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="3" class="px-4 sm:px-6 py-4 text-center">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-3 inline-block mx-auto">
                                    <p class="text-sm text-red-600 font-medium">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </p>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }

                    if (empty($bookingDetails)) {
                    ?>
                        <tr>
                            <td colspan="3" class="px-4 sm:px-6 py-6 sm:py-8 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <div class="bg-green-100 p-3 rounded-full mb-3">
                                        <i class="fas fa-check-circle text-green-500 text-xl"></i>
                                    </div>
                                    <p class="text-gray-500 text-sm font-medium">No approved bookings found</p>
                                    <p class="text-gray-400 text-xs mt-1 max-w-xs mx-auto">When bookings are approved, they will appear here</p>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
</div>


<?php
require '_footer.php';
?>