<?php
session_start();
include '../../connection/dbconnect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['registerAgainBtn'])) {

        try {
            $pdo->beginTransaction();

            $status = "Incomplete";
            $id = $_SESSION['id'];
            $operator = $_SESSION['operator'];

            $stmt = $pdo->prepare("
                UPDATE operator_account SET accountStatus = :accStatus WHERE id = :id
            ");
            $stmt->execute([
                'accStatus' => $status,
                'id' => $id,
            ]);

            $pdo->commit();

            header("Location: ../register-other-details.php");
            exit;
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            error_log($e->getMessage(), 3, 'errors.log');
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['activateBtn'])) {

        try {
            $pdo->beginTransaction();

            $status = "Activated";
            $id = $_SESSION['id'];
            $operator = $_SESSION['operator'];
            $boatStatus = 1;

            $stmt = $pdo->prepare("
                UPDATE operator_account SET accountStatus = :accStatus WHERE id = :id
            ");
            $stmt->execute([
                'accStatus' => $status,
                'id' => $id,
            ]);

            $pdo->commit();


            if ($operator == "Tour operator") {

                $stmt = $pdo->prepare("INSERT INTO cb_vouchers (operator_id) VALUES (:operator_id)");
                $stmt->execute(['operator_id' => $id]);

                header("Location: ../../account/operator/tour/home.php");
                exit;
            } elseif ($operator == "Resort operator") {
                header("Location: ../../account/operator/resort/home.php");
                exit;
            } else {

                $stmt = $pdo->prepare("UPDATE boat_operator_boatlist SET boatStatus = :boatStatus WHERE user_id = :id");
                $stmt->execute(['boatStatus' => $boatStatus, 'id' => $id]);

                if ($stmt->rowCount() === 0) {
                    throw new Exception('Failed to update.');
                }

                header("Location: ../../account/operator/boat/home.php");
                exit;
            }
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            error_log($e->getMessage(), 3, 'errors.log');
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}
