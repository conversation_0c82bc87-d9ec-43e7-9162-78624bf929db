<?php
session_start();
include '../../../../connection/dbconnect.php';
include 'inc.function.php';


if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['registerPassenger'])) {
    try {
        // CSRF Protection
        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
            throw new Exception("Invalid CSRF token.");
        }

        // Sanitize and Validate Input
        $bookingId = filter_input(INPUT_POST, 'bookingId', FILTER_SANITIZE_NUMBER_INT);
        $passengerType = htmlspecialchars(trim($_POST['passengerType']), ENT_QUOTES, 'UTF-8');
        $fullName = strtoupper(htmlspecialchars(trim($_POST['fullName']), ENT_QUOTES, 'UTF-8'));
        $regCount = htmlspecialchars(trim($_POST['region-country']), ENT_QUOTES, 'UTF-8');
        $address = strtoupper(htmlspecialchars(trim($_POST['address']), ENT_QUOTES, 'UTF-8'));
        $gender = htmlspecialchars(trim($_POST['gender']), ENT_QUOTES, 'UTF-8');
        $age = filter_var($_POST['age'], FILTER_VALIDATE_INT);
        $contactNumber = htmlspecialchars(trim($_POST['contactNumber']), ENT_QUOTES, 'UTF-8');
        $tourOperatorId = filter_input(INPUT_POST, 'tourOperatorId', FILTER_SANITIZE_NUMBER_INT);
        $voucher = filter_input(INPUT_POST, 'voucher', FILTER_SANITIZE_NUMBER_INT);
        $portName = strtoupper(htmlspecialchars(trim($_POST['portName']), ENT_QUOTES, 'UTF-8'));
        $voucherUse = filter_input(INPUT_POST, 'voucherUse', FILTER_SANITIZE_NUMBER_INT);


        $bookingDetails = getBookingDetails2($pdo, $bookingId);
        $counts = getTouristAndCrewCounts($pdo, $bookingId);

        // Expected Final Count of Passengers
        $adultCount = $bookingDetails['total_adults'];
        $childrenCount = $bookingDetails['total_children'];
        // Ongoing Count of Passengers
        $actual_adultCount = $counts['adults'];
        $actual_childrenCount = $counts['children'];

        $total_expected = $adultCount + $childrenCount;
        $total_ongoing = $actual_adultCount + $actual_childrenCount;

        // Validate Required Fields
        if (empty($bookingId) || empty($passengerType) || empty($fullName) || empty($address) || empty($gender) || empty($age) || empty($contactNumber)) {
            throw new Exception("All fields are required.");
        }

        if (!$age || $age < 0) {
            throw new Exception("Invalid age provided.");
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Check if operator has a voucher record
        $checkVoucherStmt = $pdo->prepare("SELECT * FROM cb_vouchers WHERE operator_id = :operator_id");
        $checkVoucherStmt->execute([':operator_id' => $tourOperatorId]);
        $voucherRecord = $checkVoucherStmt->fetch(PDO::FETCH_ASSOC);

        // If no voucher record exists, create one with default values
        if (!$voucherRecord) {
            $createVoucherStmt = $pdo->prepare("INSERT INTO cb_vouchers (operator_id, voucher_vinzons, voucher_others) VALUES (:operator_id, :voucher_vinzons, :voucher_others)");
            $createVoucherStmt->execute([
                ':operator_id' => $tourOperatorId,
                ':voucher_vinzons' => 0,
                ':voucher_others' => 0
            ]);
            // Set voucher to 0 since we just created a new record
            $voucher = 0;
        }

        // Check if the voucher is available based on port name
        if ($age >= 8) {
            if (preg_match('/Vinzons/i', $portName)) {
                // If voucher record exists, get the current voucher_vinzons value
                if ($voucherRecord) {
                    $voucher = (int)$voucherRecord['voucher_vinzons'];
                }

                // Check if voucher is available
                if ($voucher <= 0) {
                    throw new Exception("No Vinzons vouchers available for this operator.");
                }
            } else {
                // If voucher record exists, get the current voucher_others value
                if ($voucherRecord) {
                    $voucher = (int)$voucherRecord['voucher_others'];
                }

                // Check if voucher is available
                if ($voucher <= 0) {
                    throw new Exception("No vouchers available for this operator.");
                }
            }
        }

        // Check if the tourist already exists
        $stmt = $pdo->prepare("
        SELECT 1
        FROM cb_tourists
        WHERE booking_id = :booking_id
        AND LOWER(full_name) = LOWER(:full_name)
        LIMIT 1
        ");
        $stmt->execute([
            ':booking_id' => $bookingId,
            ':full_name' => $fullName
        ]);

        if ($stmt->fetch()) {
            throw new Exception("Tourist already exists.");
        }

        if ($age >= 8) {
            // Cast voucher to integer before subtraction to avoid type errors
            $setVoucher = (int)$voucher - 1;

            if (preg_match('/Vinzons/i', $portName)) {
                $stmtVoucherVinzons = $pdo->prepare("UPDATE cb_vouchers SET voucher_vinzons = :voucher WHERE operator_id = :operator_id");
                $stmtVoucherVinzons->execute([
                    ':voucher' => $setVoucher,
                    ':operator_id' => $tourOperatorId
                ]);
            } else {
                $stmtVoucherOthers = $pdo->prepare("UPDATE cb_vouchers SET voucher_others = :voucher WHERE operator_id = :operator_id");
                $stmtVoucherOthers->execute([
                    ':voucher' => $setVoucher,
                    ':operator_id' => $tourOperatorId
                ]);
            }

            if ($total_expected === $total_ongoing) {
                // Cast voucherUse to integer before addition to avoid type errors
                $voucher_use = (int)$voucherUse + 1;
                $stmtVoucherUse = $pdo->prepare("UPDATE cb_payments SET voucher_use = :voucher_use WHERE booking_id = :booking_id");
                $stmtVoucherUse->execute([
                    ':voucher_use' => $voucher_use,
                    ':booking_id' => $bookingId
                ]);
            }
        }

        // Insert into cb_tourist
        $stmt = $pdo->prepare("INSERT INTO cb_tourists (booking_id, full_name, demographic, address, gender, age, contact_number, info_type)
        VALUES (:bookingId, :fullName, :regCount, :address, :gender, :age, :contactNumber, :passengerType)");
        $stmt->execute([
            ':bookingId' => $bookingId,
            ':fullName' => $fullName,
            ':regCount' => $regCount,
            ':address' => $address,
            ':gender' => $gender,
            ':age' => $age,
            ':contactNumber' => $contactNumber,
            ':passengerType' => $passengerType
        ]);

        // Commit Transaction
        $pdo->commit();

        $_SESSION['success'] = "Passenger has been added successfully.";
        header("Location: ../view-approved-transaction.php?id=" . htmlspecialchars($bookingId));
        exit();

    } catch (Exception $e) {
        // Rollback Transaction if Active
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        $_SESSION['error'] = $e->getMessage();
        header("Location: ../view-approved-transaction.php?id=" . htmlspecialchars($bookingId));
        exit();
    }
}


if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['deletePassenger'])) {

        try {
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                throw new Exception("Invalid CSRF token.");
            }

            $passengerId = $_POST['passengerId'];
            $bookingId = $_POST['bookingId'];
            $age = $_POST['age'];
            $tourOperatorId = $_POST['tourOperatorId'];
            $portName = strtoupper(htmlspecialchars(trim($_POST['portName']), ENT_QUOTES, 'UTF-8'));
            $voucherUse = filter_input(INPUT_POST, 'voucherUse', FILTER_SANITIZE_NUMBER_INT);

            // Begin transaction
            $pdo->beginTransaction();

            // Only add voucher back if age is 8 or above
            if ($age >= 8) {
                // Check if operator has a voucher record
                $checkVoucherStmt = $pdo->prepare("SELECT * FROM cb_vouchers WHERE operator_id = :operator_id");
                $checkVoucherStmt->execute([':operator_id' => $tourOperatorId]);
                $voucherRecord = $checkVoucherStmt->fetch(PDO::FETCH_ASSOC);

                // If no voucher record exists, create one with default values
                if (!$voucherRecord) {
                    $createVoucherStmt = $pdo->prepare("INSERT INTO cb_vouchers (operator_id, voucher_vinzons, voucher_others) VALUES (:operator_id, :voucher_vinzons, :voucher_others)");
                    $createVoucherStmt->execute([
                        ':operator_id' => $tourOperatorId,
                        ':voucher_vinzons' => 1, // Start with 1 since we're adding a voucher back
                        ':voucher_others' => 1  // Start with 1 since we're adding a voucher back
                    ]);
                } else {
                    // Update the appropriate voucher count
                    if (preg_match('/Vinzons/i', $portName)) {
                        $currentVoucher = (int)$voucherRecord['voucher_vinzons'];
                        $setVoucher = $currentVoucher + 1;

                        $stmtVoucherVinzons = $pdo->prepare("UPDATE cb_vouchers SET voucher_vinzons = :voucher WHERE operator_id = :operator_id");
                        $stmtVoucherVinzons->execute([
                            ':voucher' => $setVoucher,
                            ':operator_id' => $tourOperatorId
                        ]);
                    } else {
                        $currentVoucher = (int)$voucherRecord['voucher_others'];
                        $setVoucher = $currentVoucher + 1;

                        $stmtVoucherOthers = $pdo->prepare("UPDATE cb_vouchers SET voucher_others = :voucher WHERE operator_id = :operator_id");
                        $stmtVoucherOthers->execute([
                            ':voucher' => $setVoucher,
                            ':operator_id' => $tourOperatorId
                        ]);
                    }
                }

                // Update voucher use count in payments table if needed
                if ($voucherUse >= 1) {
                    // Cast voucherUse to integer before subtraction to avoid type errors
                    $voucher_use = (int)$voucherUse - 1;
                    $stmtVoucherUse = $pdo->prepare("UPDATE cb_payments SET voucher_use = :voucher_use WHERE booking_id = :booking_id");
                    $stmtVoucherUse->execute([
                        ':voucher_use' => $voucher_use,
                        ':booking_id' => $bookingId
                    ]);
                }
            }

            // Delete the tourist record
            $stmt = $pdo->prepare("DELETE FROM cb_tourists WHERE tourist_id = :tourist_id");
            $stmt->execute([
                ':tourist_id' => $passengerId
            ]);

            // Commit the transaction
            $pdo->commit();

            $_SESSION['success'] = "Passenger info has been deleted successfully.";
            header("Location: ../view-approved-transaction.php?id=" . $bookingId); // Redirect back to the form
            exit();
        } catch (Exception $e) {
            // Rollback if transaction is active
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }

            $_SESSION['error'] = $e->getMessage();
            header("Location: ../view-approved-transaction.php?id=" . $bookingId); // Redirect back to the form
            exit();
        }
    }
}


  // For checking
            // $bookingDetails = getBookingDetails2($pdo, $getBookingId);
            // $counts = getTouristAndCrewCounts($pdo, $getBookingId);

            // // Expectec Final Count of Passengers
            // $adultCount = $bookingDetails['total_adults'];
            // $childrenCount = $bookingDetails['total_children'];

            // // Ongoing Count of Passengers
            // $actual_adultCount = $counts['adults'];
            // $actual_childrenCount = $counts['children'];

            // if ($adultCount === $actual_adultCount) {
            //     throw new Exception("The number of adults exceeds the total number of adults that have been submitted");
            // }

            // if ($childrenCount === $actual_childrenCount) {
            //     throw new Exception("The number of children exceeds the total number of children that have been submitted");
            // }