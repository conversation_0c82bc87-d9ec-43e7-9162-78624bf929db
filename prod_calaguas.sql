-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 01, 2025 at 03:25 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `prod_calaguas`
--

-- --------------------------------------------------------

--
-- Table structure for table `account_declined`
--

CREATE TABLE `account_declined` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `message` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `account_declined`
--

INSERT INTO `account_declined` (`id`, `user_id`, `message`) VALUES
(4, 13, 'nothing test '),
(5, 14, 'Malabo ang pictures');

-- --------------------------------------------------------

--
-- Table structure for table `boat_operator_boatlist`
--

CREATE TABLE `boat_operator_boatlist` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `boatName` tinytext NOT NULL,
  `capacity` int(11) NOT NULL,
  `boatStatus` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `boat_operator_boatlist`
--

INSERT INTO `boat_operator_boatlist` (`id`, `user_id`, `boatName`, `capacity`, `boatStatus`) VALUES
(6, 27, 'BOAT 1', 35, 1),
(7, 27, 'BOAT 2', 35, 1);

-- --------------------------------------------------------

--
-- Table structure for table `cb_bookings`
--

CREATE TABLE `cb_bookings` (
  `booking_id` int(11) NOT NULL,
  `referenceNum` varchar(250) NOT NULL,
  `control_number` varchar(20) DEFAULT 'N/A',
  `tour_operator_id` int(11) NOT NULL,
  `resort_operator_id` int(11) NOT NULL,
  `boat_id` int(11) NOT NULL,
  `port_id` int(11) NOT NULL,
  `check_in_date` date NOT NULL,
  `check_out_date` date NOT NULL,
  `booking_status` enum('draft','voucher','pending','declined','approved','completed') DEFAULT 'draft',
  `date_created` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cb_bookings`
--

INSERT INTO `cb_bookings` (`booking_id`, `referenceNum`, `control_number`, `tour_operator_id`, `resort_operator_id`, `boat_id`, `port_id`, `check_in_date`, `check_out_date`, `booking_status`, `date_created`) VALUES
(30, 'CB-2025-0852ED-8E6B', '1234567', 25, 26, 6, 3, '2025-05-16', '2025-05-17', 'completed', '2025-05-15 09:18:55'),
(31, 'CB-2025-3F8188-5CB5', 'N/A', 25, 26, 6, 3, '2025-05-23', '2025-05-24', 'pending', '2025-05-22 13:35:16');

-- --------------------------------------------------------

--
-- Table structure for table `cb_booking_approvals`
--

CREATE TABLE `cb_booking_approvals` (
  `approval_id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `resort` enum('Waiting','Pending','Approved','Declined') DEFAULT 'Pending',
  `boat` enum('Waiting','Pending','Approved','Declined') DEFAULT 'Waiting',
  `treasurer` enum('Waiting','Pending','Approved','Declined') DEFAULT 'Pending',
  `mtho` enum('Waiting','Pending','Approved','Declined') DEFAULT 'Waiting'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cb_booking_approvals`
--

INSERT INTO `cb_booking_approvals` (`approval_id`, `booking_id`, `resort`, `boat`, `treasurer`, `mtho`) VALUES
(20, 30, 'Approved', 'Approved', 'Approved', 'Approved'),
(21, 31, 'Pending', 'Waiting', 'Pending', 'Waiting');

-- --------------------------------------------------------

--
-- Table structure for table `cb_payments`
--

CREATE TABLE `cb_payments` (
  `payment_id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `total_adults` int(11) NOT NULL,
  `total_children` int(11) NOT NULL,
  `total_crew` int(11) NOT NULL DEFAULT 4,
  `port_fee` decimal(10,2) NOT NULL,
  `total_amount` decimal(10,2) DEFAULT 0.00,
  `voucher_use` int(11) NOT NULL DEFAULT 0,
  `payment_status` enum('unpaid','paid') DEFAULT 'unpaid',
  `or_num` varchar(25) DEFAULT NULL,
  `payment_method` enum('Bank_Transfer','Over_the_Counter') DEFAULT 'Over_the_Counter',
  `receipt_image` varchar(255) DEFAULT NULL,
  `date_created` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cb_payments`
--

INSERT INTO `cb_payments` (`payment_id`, `booking_id`, `total_adults`, `total_children`, `total_crew`, `port_fee`, `total_amount`, `voucher_use`, `payment_status`, `or_num`, `payment_method`, `receipt_image`, `date_created`) VALUES
(27, 30, 2, 0, 3, 50.00, 100.00, 0, 'paid', 'ABC-012312', 'Over_the_Counter', 'calaguas_booking_workflow.png', '2025-05-15 09:18:55'),
(28, 31, 1, 0, 2, 50.00, 50.00, 0, 'unpaid', NULL, 'Over_the_Counter', NULL, '2025-05-22 13:35:16');

-- --------------------------------------------------------

--
-- Table structure for table `cb_tourists`
--

CREATE TABLE `cb_tourists` (
  `tourist_id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `demographic` varchar(50) NOT NULL,
  `address` varchar(250) NOT NULL,
  `gender` enum('Male','Female','Other') NOT NULL,
  `age` int(11) NOT NULL,
  `contact_number` varchar(20) NOT NULL,
  `info_type` enum('tourist','crewTts','crewMbca') NOT NULL,
  `date_created` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cb_tourists`
--

INSERT INTO `cb_tourists` (`tourist_id`, `booking_id`, `full_name`, `demographic`, `address`, `gender`, `age`, `contact_number`, `info_type`, `date_created`) VALUES
(100, 30, 'TOURIST 1', 'Camarines Norte', 'LABO CAMARINES NORTE', 'Male', 35, '09123671531', 'tourist', '2025-05-15 09:19:57'),
(102, 30, 'CREW 1', 'Camarines Norte', 'VINZONS CAMARINES NORTE', 'Female', 35, '09123173561', 'crewTts', '2025-05-15 09:21:21'),
(103, 30, 'CREW4', 'Camarines Norte', 'VINZONS CN', 'Male', 35, '09821536721', 'crewMbca', '2025-05-15 10:29:14'),
(105, 30, 'TOURIST 2', 'Camarines Norte', 'LABO CAMARINES NORTE', 'Male', 35, '09821536721', 'tourist', '2025-05-21 18:14:32'),
(106, 31, 'TOURIST 01', 'Camarines Norte', 'LABO CAMARINES NORTE', 'Female', 35, '09123713131', 'tourist', '2025-05-22 13:40:14'),
(107, 31, 'CREW01', 'Camarines Norte', 'VINZONS', 'Male', 35, '09123131313', 'crewTts', '2025-05-22 13:40:40'),
(108, 31, 'CREW02', 'Camarines Norte', 'VINZONS', 'Male', 40, '09123713131', 'crewTts', '2025-05-22 19:46:49');

-- --------------------------------------------------------

--
-- Table structure for table `cb_vouchers`
--

CREATE TABLE `cb_vouchers` (
  `id` int(11) NOT NULL,
  `operator_id` int(11) NOT NULL,
  `voucher_vinzons` int(11) NOT NULL DEFAULT 0,
  `voucher_others` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cb_vouchers`
--

INSERT INTO `cb_vouchers` (`id`, `operator_id`, `voucher_vinzons`, `voucher_others`) VALUES
(5, 25, 10, 10);

-- --------------------------------------------------------

--
-- Table structure for table `landbank_transactions`
--

CREATE TABLE `landbank_transactions` (
  `id` int(11) NOT NULL,
  `transaction_id` varchar(100) NOT NULL,
  `booking_id` int(11) DEFAULT NULL,
  `merchant_ref_num` varchar(100) DEFAULT NULL,
  `lbp_ref_num` varchar(100) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `status` enum('initiated','pending','completed','failed','cancelled','response_received','error') NOT NULL,
  `request_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`request_data`)),
  `response_data` text DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `lbp_conf_num` varchar(100) DEFAULT NULL,
  `lbp_conf_date` datetime DEFAULT NULL,
  `callback_received_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `operator_account`
--

CREATE TABLE `operator_account` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(200) NOT NULL,
  `email` varchar(50) NOT NULL,
  `accountCreated` varchar(50) NOT NULL,
  `accountExpired` varchar(50) DEFAULT NULL,
  `rememberToken` varchar(200) NOT NULL,
  `accType` tinytext NOT NULL,
  `accountStatus` varchar(20) NOT NULL,
  `login_status` text NOT NULL DEFAULT 'isLogout'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `operator_account`
--

INSERT INTO `operator_account` (`id`, `username`, `password`, `email`, `accountCreated`, `accountExpired`, `rememberToken`, `accType`, `accountStatus`, `login_status`) VALUES
(10, 'admin01', '$2y$10$tJcVnM39pxh7aHlDfJnUSuKejjoZDI5skpsQfacC/jhZyo6R6CM7O', '<EMAIL>', '2025-01-20', NULL, 'f09306d2a055c466f48cfb1e2c5b7b34c9362163388ee395c7d5df2fd8d137be', 'admin', 'Activated', 'isLogin'),
(25, 'tour01', '$2y$10$tLmi.rChghNOwRgTniMYme6sMPydot7BE8Y/LGSwXxaal2ABfrwKC', '<EMAIL>', '2025-05-12', '2025-05-30 04:35:22', '843d3c942903d7220e8814c8bf9f90757820e14ccc556ea3a31b38994885ce48', 'user', 'Activated', 'isLogin'),
(26, 'resort01', '$2y$10$tJcVnM39pxh7aHlDfJnUSuKejjoZDI5skpsQfacC/jhZyo6R6CM7O', '<EMAIL>', '2025-05-12', '2025-05-30 04:37:05', 'ff15851032b271841da7ee37b46a69e90e9e7251b83d978805556377edff291f', 'user', 'Activated', 'isLogout'),
(27, 'boat01', '$2y$10$NMpvDWZDqAUnPKWJuu05heDqzGZQw74ZTvAfRCMGf4swOKLLkhgcC', '<EMAIL>', '2025-05-12', '2025-12-31', '31ff902e5389ffd31fccc460c0239b7d27dd984b3ebad7134111e2b9742f489f', 'user', 'Activated', 'isLogout'),
(28, 'treasurer01', '$2y$10$2tRWghLSOgge4c1.GEB8TessV2.OYTN5wICrdf2iSryKJn7OKS1uG', '<EMAIL>', '2025-05-15', NULL, '0c187ae949361b5df9d24c682ebc11dfb997f5c8e5bc0eeb5b6427d521669b8c', 'treasurer', 'Activated', 'isLogout');

-- --------------------------------------------------------

--
-- Table structure for table `operator_credentials`
--

CREATE TABLE `operator_credentials` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `picture_id` varchar(100) DEFAULT NULL,
  `picture_permit` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `operator_credentials`
--

INSERT INTO `operator_credentials` (`id`, `user_id`, `picture_id`, `picture_permit`) VALUES
(20, 25, 'id_calaguas_857cb24af6.jpg', 'permit_calaguas_34b8cb9fa9.png'),
(21, 26, 'id_calaguas_9c93a2e0c7.png', 'permit_calaguas_26e1df1c1e.png'),
(22, 27, 'id_calaguas_a15f6fdf7a.png', 'permit_calaguas_216116fc55.png');

-- --------------------------------------------------------

--
-- Table structure for table `operator_info`
--

CREATE TABLE `operator_info` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `firstname` tinytext NOT NULL,
  `middlename` tinytext DEFAULT NULL,
  `lastname` tinytext NOT NULL,
  `extname` tinytext DEFAULT NULL,
  `address` varchar(100) DEFAULT NULL,
  `contact` text DEFAULT NULL,
  `operatorType` enum('Tour operator','Boat operator','Resort operator') DEFAULT NULL,
  `designation` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `operator_info`
--

INSERT INTO `operator_info` (`id`, `user_id`, `firstname`, `middlename`, `lastname`, `extname`, `address`, `contact`, `operatorType`, `designation`) VALUES
(10, 10, 'JOHN', NULL, 'DOE', NULL, 'LABO CAMARINES NORTE', '09297570016', NULL, NULL),
(24, 25, 'GERALD', 'PAJARILLO', 'MESA', '', 'LABO CAMARINES NORTE', '01923183618', 'Tour operator', 'CALAGUAS TOURS'),
(25, 26, 'GERALD', 'PAJARILLO', 'MESA', '', 'LABO CAMARINES NORTE', '09297570016', 'Resort operator', 'CALAGUAS RESORT'),
(26, 27, 'GERALD', 'PAJARILLO', 'MESA', '', 'LABO CAMARINES NORTE', '09297570016', 'Boat operator', NULL),
(27, 28, 'JONNY', '', 'DOE', '', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `port_list`
--

CREATE TABLE `port_list` (
  `id` int(11) NOT NULL,
  `portName` varchar(20) NOT NULL,
  `fee` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `port_list`
--

INSERT INTO `port_list` (`id`, `portName`, `fee`) VALUES
(3, 'VINZONS PORT', 50),
(4, 'PARACALE PORT', 120);

-- --------------------------------------------------------

--
-- Table structure for table `resort_operator_roomlist`
--

CREATE TABLE `resort_operator_roomlist` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` tinytext NOT NULL,
  `capacity` tinyint(4) NOT NULL,
  `quantity` tinyint(4) NOT NULL,
  `comRoom` tinyint(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `resort_operator_roomlist`
--

INSERT INTO `resort_operator_roomlist` (`id`, `user_id`, `type`, `capacity`, `quantity`, `comRoom`) VALUES
(8, 26, 'COTTAGE', 20, 5, 0);

-- --------------------------------------------------------

--
-- Table structure for table `system`
--

CREATE TABLE `system` (
  `id` int(11) NOT NULL,
  `status_maintenance` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `system`
--

INSERT INTO `system` (`id`, `status_maintenance`) VALUES
(1, 0);

-- --------------------------------------------------------

--
-- Table structure for table `system_logs`
--

CREATE TABLE `system_logs` (
  `id` int(11) NOT NULL,
  `type` varchar(20) NOT NULL,
  `description` varchar(100) NOT NULL,
  `date` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `system_logs`
--

INSERT INTO `system_logs` (`id`, `type`, `description`, `date`) VALUES
(1, 'Setup', 'Password reset system verified', '2025-05-29 23:19:14'),
(2, 'SMTP Test', 'SMTP test email sent to: <EMAIL>', '2025-05-29 23:51:35'),
(3, 'Reset Request', 'Password reset for: <EMAIL> (ID: 25)', '2025-05-29 23:53:15'),
(4, 'Email', 'Email: Password <NAME_EMAIL>', '2025-05-29 23:53:15'),
(5, 'Reset Complete', 'Password reset for: tour01 (ID: 25)', '2025-05-29 23:54:23'),
(6, 'Email', 'Email: Password Reset <NAME_EMAIL>', '2025-05-29 23:54:26'),
(7, 'Reset Request', 'Password reset for: <EMAIL> (Tour operator) (ID: 25)', '2025-05-30 01:35:23'),
(8, 'Email', 'Email: Password <NAME_EMAIL>', '2025-05-30 01:35:23'),
(9, 'Reset Request', 'Password reset for: <EMAIL> (Resort operator) (ID: 26)', '2025-05-30 01:37:05'),
(10, 'Email', 'Email: Password <NAME_EMAIL>', '2025-05-30 01:37:05');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `account_declined`
--
ALTER TABLE `account_declined`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `boat_operator_boatlist`
--
ALTER TABLE `boat_operator_boatlist`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `cb_bookings`
--
ALTER TABLE `cb_bookings`
  ADD PRIMARY KEY (`booking_id`),
  ADD KEY `tour_operator_id` (`tour_operator_id`),
  ADD KEY `resort_operator_id` (`resort_operator_id`),
  ADD KEY `boat_id` (`boat_id`),
  ADD KEY `port_id` (`port_id`);

--
-- Indexes for table `cb_booking_approvals`
--
ALTER TABLE `cb_booking_approvals`
  ADD PRIMARY KEY (`approval_id`),
  ADD KEY `cb_booking_approvals_ibfk_1` (`booking_id`);

--
-- Indexes for table `cb_payments`
--
ALTER TABLE `cb_payments`
  ADD PRIMARY KEY (`payment_id`),
  ADD UNIQUE KEY `booking_id` (`booking_id`);

--
-- Indexes for table `cb_tourists`
--
ALTER TABLE `cb_tourists`
  ADD PRIMARY KEY (`tourist_id`),
  ADD KEY `cb_tourists_ibfk_1` (`booking_id`);

--
-- Indexes for table `cb_vouchers`
--
ALTER TABLE `cb_vouchers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `voucher_id` (`operator_id`);

--
-- Indexes for table `landbank_transactions`
--
ALTER TABLE `landbank_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `transaction_id` (`transaction_id`),
  ADD KEY `idx_booking_id` (`booking_id`),
  ADD KEY `idx_transaction_id` (`transaction_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `operator_account`
--
ALTER TABLE `operator_account`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `operator_credentials`
--
ALTER TABLE `operator_credentials`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `operator_info`
--
ALTER TABLE `operator_info`
  ADD PRIMARY KEY (`id`),
  ADD KEY `account_id` (`user_id`);

--
-- Indexes for table `port_list`
--
ALTER TABLE `port_list`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `resort_operator_roomlist`
--
ALTER TABLE `resort_operator_roomlist`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `system`
--
ALTER TABLE `system`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `account_declined`
--
ALTER TABLE `account_declined`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `boat_operator_boatlist`
--
ALTER TABLE `boat_operator_boatlist`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `cb_bookings`
--
ALTER TABLE `cb_bookings`
  MODIFY `booking_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `cb_booking_approvals`
--
ALTER TABLE `cb_booking_approvals`
  MODIFY `approval_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `cb_payments`
--
ALTER TABLE `cb_payments`
  MODIFY `payment_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `cb_tourists`
--
ALTER TABLE `cb_tourists`
  MODIFY `tourist_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=109;

--
-- AUTO_INCREMENT for table `cb_vouchers`
--
ALTER TABLE `cb_vouchers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `landbank_transactions`
--
ALTER TABLE `landbank_transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `operator_account`
--
ALTER TABLE `operator_account`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `operator_credentials`
--
ALTER TABLE `operator_credentials`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `operator_info`
--
ALTER TABLE `operator_info`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `port_list`
--
ALTER TABLE `port_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `resort_operator_roomlist`
--
ALTER TABLE `resort_operator_roomlist`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `system`
--
ALTER TABLE `system`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `system_logs`
--
ALTER TABLE `system_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `boat_operator_boatlist`
--
ALTER TABLE `boat_operator_boatlist`
  ADD CONSTRAINT `boat_operator_boatlist_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `operator_info` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `cb_bookings`
--
ALTER TABLE `cb_bookings`
  ADD CONSTRAINT `cb_bookings_ibfk_1` FOREIGN KEY (`tour_operator_id`) REFERENCES `operator_info` (`user_id`),
  ADD CONSTRAINT `cb_bookings_ibfk_2` FOREIGN KEY (`resort_operator_id`) REFERENCES `operator_info` (`user_id`),
  ADD CONSTRAINT `cb_bookings_ibfk_3` FOREIGN KEY (`boat_id`) REFERENCES `boat_operator_boatlist` (`id`),
  ADD CONSTRAINT `cb_bookings_ibfk_4` FOREIGN KEY (`port_id`) REFERENCES `port_list` (`id`);

--
-- Constraints for table `cb_booking_approvals`
--
ALTER TABLE `cb_booking_approvals`
  ADD CONSTRAINT `cb_booking_approvals_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `cb_bookings` (`booking_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `cb_payments`
--
ALTER TABLE `cb_payments`
  ADD CONSTRAINT `cb_payments_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `cb_bookings` (`booking_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `cb_tourists`
--
ALTER TABLE `cb_tourists`
  ADD CONSTRAINT `cb_tourists_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `cb_bookings` (`booking_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `operator_credentials`
--
ALTER TABLE `operator_credentials`
  ADD CONSTRAINT `operator_credentials_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `operator_info` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `operator_info`
--
ALTER TABLE `operator_info`
  ADD CONSTRAINT `operator_info_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `operator_account` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `resort_operator_roomlist`
--
ALTER TABLE `resort_operator_roomlist`
  ADD CONSTRAINT `resort_operator_roomlist_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `operator_info` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
