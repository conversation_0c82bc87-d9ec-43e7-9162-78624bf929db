<?php
include '../../../../connection/dbconnect.php';
include '../../../assets/dompdf/autoload.inc.php';
include '../inc/inc.function.php';

use Dompdf\Dompdf;
use Dompdf\Options;

$options = new Options();
$options->set('chroot', realpath('img/'));

$dompdf = new Dompdf($options);

$getBookingId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
$statusBooking = "approved";
$dateCreated = date("F j, Y", strtotime(date("Y-m-d")));
$yearNow = date("Y");

if (empty($getBookingId)) {
    header("Location: transaction-approved.php");
    exit;
}

$bookingDetails = getBookingDetails($pdo, $getBookingId);
if ($bookingDetails['booking_status'] != $statusBooking) {
    header("Location: ../transaction-approved.php");
    exit;
} else {
    if ($bookingDetails !== null) {
        $extname = $bookingDetails['extname'] ?? ''; // Use null coalescing operator for defaults
        $middlename = $bookingDetails['middlename'] ?? '';
        $operatorName = trim($bookingDetails['firstname'] . ' ' . $middlename . ' ' . $bookingDetails['lastname'] . ' ' . $extname);
        $designationTour = $bookingDetails['tour_operator_designation'];
        $controlNumber = $bookingDetails['control_number'];
        $resortName = $bookingDetails['resort_operator_designation'];
        $checkin = date("M. j, Y", strtotime($bookingDetails['check_in_date']));
        $loginStatus = $bookingDetails['login_status'];


        if ($loginStatus != "isLogin") {
            header("Location: ../logout.php");
            exit;
        }

        // Generate TTS Manifest
        $ttsHtml = generateManifest($pdo, $getBookingId, "crewTts");

        // Generate MBCA Manifest
        $mbcaHtml = generateManifest($pdo, $getBookingId, "crewMbca");

        // Generate Tourist Manifest
        $touristHtml = generateManifest($pdo, $getBookingId, "tourist");
    }
}

function generateManifest($pdo, $getBookingId, $infoType)
{
    $html = '<table id="details" style="width: 100%;">
        <thead>
            <tr>
                <th>NO.</th>
                <th>NAME</th>
                <th>ADDRESS</th>
                <th>GENDER</th>
                <th>AGE</th>
                <th>EMERGENCY NO.</th>
                <th>SIGNATURE</th>
            </tr>
        </thead>
        <tbody>';

    $sql = "SELECT * FROM cb_tourists WHERE booking_id = :booking_id AND info_type = :info_type";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_INT);
    $stmt->bindParam(':info_type', $infoType, PDO::PARAM_STR);
    $stmt->execute();
    $count = $stmt->rowCount();

    if ($count > 0) {
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $counter = 1;

        foreach ($data as $row) {
            $html .= '
                    <tr>
                        <td>' . $counter . '</td>
                        <td style="text-align: center;">' . $row['full_name'] . '</td>
                        <td style="text-align: center;">' . $row['demographic'] . ',' . $row['address'] . '</td>
                        <td style="text-align: center;">' . $row['gender'] . '</td>
                        <td style="text-align: center;">' . $row['age'] . '</td>
                        <td style="text-align: center;">' . $row['contact_number'] . '</td>
                        <td></td>
                    </tr>

                    ';
            $counter++;
        }
    } else {
        $html .= '<tr><td colspan="7">No data available</td></tr>';
    }

    $html .= '
        </tbody>
    </table>';

    return $html;
}


$html = '
 <style>
            #details {
                border-collapse: collapse;
                width: 100%;
                font-size:11px;
            }

            #details th,
            #details td {
                border: 1px solid #000;
                text-align: left;
            }

            #details th {
                text-align: center;
                background-color: #002060;
                color: white;
            }
        </style>
        <div>
            <table style="width:100%;">
                <tr>
                    <td style="text-align: center;">
                        <img src="img/dotr.png" class="logo" style="height: 75px;">
                    </td>
                    <td style="text-align: center; font-size:11px;">
                        <p>
                            DEPARTMENT OF TRANSPORTATION <br>
                            <b>PHILIPPINE COAST GUARD</b> <br>
                            COAST GUARD DISTRICT BICOL<br>
                            HEADQUARTERS COAST GUARD STATION CAMARINES NORTE <br>
                            <b>COAST GUARD CLEARING OUT POST VINZONS</b><br>
                            Peñafrancia Site, Calangcawan Norte Vinzons, Camarines Norte
                        </p>
                    </td>
                    <td style="text-align: center;">
                        <img src="img/cg.png" class="logo2" style="height: 75px;">
                    </td>
                </tr>
            </table>
            <br>
            <table style="width:100%; font-size:11px; ">
                <tr>
                    <td>Name of MBCA:</td>
                    <td style="color:blue;"><b>' . $operatorName . '</b></td>
                    <td>Date:</td>
                    <td style="color:blue;"><b>' . $checkin . '</b></td>
                </tr>
                <tr>
                    <td>Destination:</td>
                    <td style="color:blue;"><b>MAHABANG BUHANGIN, Brgy. Mangcawayan, Calaguas, VCN</b></td>
                    <td>Accommodation:</td>
                    <td style="color:blue;"><b>' . $resortName . '</b></td>
                </tr>
            </table>
            <span style="text-align: center;">
                <div style="padding:10px 0 10px 0;">
                    <b>P A S S E N G E R &nbsp; M A N I F E S T</b>
                </div>
            </span>

            ' . $touristHtml . '

            <span style="text-align: center;">
                <div style="padding:10px 0 10px 0;">
                    <b>TRAVEL AND TOURS STAFF</b>
                </div>
            </span>

            ' . $ttsHtml . '

            <span style="text-align: center;">
                <div style="padding:10px 0 10px 0;">
                    <b>MBCA CREW</b>
                </div>
            </span>

            ' . $mbcaHtml . '

            <table style="width:100%; font-size:9px; margin-top:30px; text-align:left;">
            <tr>
                <td><b>CAMARINES NORTE</b> Coast Guard Station</td>
                <td>: 054 - 2161094</td>
                <td style="text-align: center;">__________________________________________</td>
            </tr>
            <tr>
                <td><b>VINZONS</b> Coast Guard Clearing Out Post</td>
                <td>: 09212478054 (Smart)</td>
                <td style="text-align: center;">SIGNATURE OVER PRINTED NAME</td>
            </tr>
            <tr>
                <td><b>M</b>unicipal <b>T</b>ourism and <b>H</b>eritage <b>O</b>perations</td>
                <td>: 09778043251 (Globe Landline)</td>
                <td style="text-align: center;"><b>Boat Captain / Boat Master</b></td>
            </tr>
        </table>

            <p style="font-size:8px;">
                This a computer generated document. <br>
                No signature required for MTO and MTHO. <br>
                <b>DO NOT COPY</b><br>
                Date Created:' . $dateCreated . '
            </p>
        </div>
';

$dompdf->loadHtml($html);

$dompdf->setPaper('A4', 'portrait');

$dompdf->render();

$filename = $controlNumber . '-manifesto';
$dompdf->stream($filename, array("Attachment" => true));
