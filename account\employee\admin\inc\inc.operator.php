<?php
session_start();
include '../../../../connection/dbconnect.php';

// Approved Pending Operator
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['approveOperatorBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['accountType'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $id = $_POST['id'];
            $newStatus = "ready";
            $token = $_POST['token'];
            $username = $_POST['username'];
            $adminUsername = $_POST['adminUsername'];

            // Start transaction
            $pdo->beginTransaction();

            // Update the account Status
            $stmt = $pdo->prepare("UPDATE operator_account SET accountStatus = :newStatus WHERE id = :id");
            $stmt->execute(['newStatus' => $newStatus, 'id' => $id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update.');
            }

            // Commit the transaction

            // For Logs
            $type = "Approved - Account";
            $description = "Admin: " . $adminUsername . " approved: " . $username . "'s Account";
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => $type,
                ':description' => $description
            ]);

            $pdo->commit();

            // Redirect on success
            $_SESSION['success'] = "The account is now active and approved.";
            header("Location: ../operator.php");
            exit;
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../operator.php?token=" . $token);
            exit;
        }
    }
}

// Declined Pending Operator
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['declineOperatorBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['accountType'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $id = $_POST['id'];
            $newStatus = "declined";
            $token = $_POST['token'];
            $message = $_POST['declineMessage'];
            $username = $_POST['username'];
            $adminUsername = $_POST['adminUsername'];


            // Start transaction
            $pdo->beginTransaction();

            // Update the account status
            $stmt = $pdo->prepare("UPDATE operator_account SET accountStatus = :newStatus WHERE id = :id");
            $stmt->execute(['newStatus' => $newStatus, 'id' => $id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update account status.');
            }

            // Check if entry exists in account_declined
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM account_declined WHERE user_id = :user_id");
            $stmt->execute(['user_id' => $id]);
            $exists = $stmt->fetchColumn() > 0;

            if ($exists) {
                // Update message if user_id exists
                $stmt = $pdo->prepare("UPDATE account_declined SET message = :message WHERE user_id = :user_id");
                $stmt->execute(['message' => $message, 'user_id' => $id]);

                if ($stmt->rowCount() === 0) {
                    throw new Exception('Failed to update reason.');
                }
            } else {
                // Insert a new entry if user_id does not exist
                $stmt = $pdo->prepare("INSERT INTO account_declined (user_id, message) VALUES (:user_id, :message)");
                $stmt->execute(['user_id' => $id, 'message' => $message]);

                if ($stmt->rowCount() === 0) {
                    throw new Exception('Failed to insert reason.');
                }
            }

            // For Logs
            $type = "Declined - Account";
            $description = "Admin: " . $adminUsername . " declined: " . $username . "'s Account. Reason: " . $message;
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => $type,
                ':description' => $description
            ]);


            // Commit the transaction
            $pdo->commit();

            // Redirect on success
            $_SESSION['success'] = "The account has been rejected.";
            header("Location: ../operator.php");
            exit;
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../operator.php?token=" . $token);
            exit;
        }
    }
}
