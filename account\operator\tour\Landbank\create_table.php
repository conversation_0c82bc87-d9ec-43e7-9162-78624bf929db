<?php
/**
 * Landbank Transactions Table Creator
 * Run this file once to create the required database table
 */

// Include database connection
require_once '../../../connection/dbconnect.php';

echo "<h2>Landbank Transactions Table Creator</h2>\n";
echo "<p>Creating landbank_transactions table...</p>\n";

try {
    // Create the landbank_transactions table
    $sql = "CREATE TABLE IF NOT EXISTS landbank_transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        transaction_id VARCHAR(100) NOT NULL UNIQUE,
        booking_id INT NULL,
        status ENUM('initiated', 'pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'initiated',
        amount DECIMAL(10,2) NULL,
        request_data JSON NULL,
        response_data TEXT NULL,
        error_message TEXT NULL,
        lbp_ref_num VARCHAR(100) NULL,
        lbp_conf_num VARCHAR(100) NULL,
        lbp_conf_date DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_booking_id (booking_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        INDEX idx_lbp_ref_num (lbp_ref_num)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($sql);
    
    echo "<p style='color: green;'>✓ Table 'landbank_transactions' created successfully!</p>\n";
    
    // Test the table by inserting a sample record
    $testSql = "INSERT INTO landbank_transactions 
                (transaction_id, booking_id, status, amount) 
                VALUES ('TEST-" . date('YmdHis') . "', 999, 'initiated', 100.00)
                ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP";
    
    $pdo->exec($testSql);
    echo "<p style='color: green;'>✓ Test record inserted successfully!</p>\n";
    
    // Clean up test record
    $pdo->exec("DELETE FROM landbank_transactions WHERE transaction_id LIKE 'TEST-%'");
    echo "<p style='color: green;'>✓ Test record cleaned up!</p>\n";
    
    // Show table structure
    $result = $pdo->query("DESCRIBE landbank_transactions");
    echo "<h3>Table Structure:</h3>\n";
    echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<h3>Setup Complete!</h3>\n";
    echo "<p>The Landbank payment system is now ready to use.</p>\n";
    echo "<p><strong>Important:</strong> You can now delete this file for security.</p>\n";
    echo "<p><a href='../payment-tdf.php'>← Back to Payment System</a></p>\n";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Error creating table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Please check your database connection and permissions.</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Unexpected error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Landbank Table Creator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; margin: 10px 0; }
        th { background-color: #f0f0f0; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Landbank Payment System Setup</h1>
    <p>This script creates the required database table for Landbank payment tracking.</p>
    
    <div style="background: #f9f9f9; padding: 15px; border-left: 4px solid #007cba; margin: 20px 0;">
        <h3>What this script does:</h3>
        <ul>
            <li>Creates the <code>landbank_transactions</code> table</li>
            <li>Sets up proper indexes for performance</li>
            <li>Tests the table with a sample record</li>
            <li>Shows the table structure</li>
        </ul>
    </div>
    
    <div style="background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0;">
        <h3>Security Note:</h3>
        <p><strong>Delete this file after running it!</strong> This script should only be run once during setup.</p>
    </div>
</body>
</html>
