<?php
require '_header.php';

// Get statistics for the dashboard
try {
    // Count pending transactions
    $sqlPending = "SELECT COUNT(*) as count FROM cb_booking_approvals WHERE treasurer = 'Pending'";
    $stmtPending = $pdo->prepare($sqlPending);
    $stmtPending->execute();
    $pendingTransactions = $stmtPending->fetchColumn();

    // Count approved transactions
    $sqlApproved = "SELECT COUNT(*) as count FROM cb_booking_approvals WHERE treasurer = 'Approved'";
    $stmtApproved = $pdo->prepare($sqlApproved);
    $stmtApproved->execute();
    $approvedTransactions = $stmtApproved->fetchColumn();

    // Count declined transactions (if applicable)
    $sqlDeclined = "SELECT COUNT(*) as count FROM cb_booking_approvals WHERE treasurer = 'Declined'";
    $stmtDeclined = $pdo->prepare($sqlDeclined);
    $stmtDeclined->execute();
    $declinedTransactions = $stmtDeclined->fetchColumn();

    // Count total transactions
    $totalTransactions = $pendingTransactions + $approvedTransactions + $declinedTransactions;
} catch (PDOException $e) {
    // Default values if query fails
    $totalTransactions = 0;
    $pendingTransactions = 0;
    $approvedTransactions = 0;
    $declinedTransactions = 0;
}

// Get monthly transaction data for chart
$monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
$shortMonthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
$monthlyData = array_fill(0, 12, 0); // Initialize with zeros

try {
    $sql = "SELECT
                MONTH(bb.date_created) as month,
                COUNT(*) as count
            FROM cb_bookings bb
            JOIN cb_booking_approvals cba ON bb.booking_id = cba.booking_id
            WHERE YEAR(bb.date_created) = YEAR(CURRENT_DATE())
            GROUP BY MONTH(bb.date_created)";

    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $monthlyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($monthlyStats as $stat) {
        $monthIndex = $stat['month'] - 1; // Adjust for 0-based array
        $monthlyData[$monthIndex] = (int)$stat['count'];
    }
} catch (PDOException $e) {
    // Keep default empty data
}

// Get recent transactions (limit to 5)
try {
    $sql = "SELECT
                bb.booking_id,
                bb.referenceNum,
                bb.date_created,
                cba.treasurer,
                oi.designation AS tour_operator,
                oi.firstname AS tour_firstname,
                oi.lastname AS tour_lastname,
                cp.or_num
            FROM cb_bookings bb
            JOIN cb_booking_approvals cba ON bb.booking_id = cba.booking_id
            LEFT JOIN operator_info oi ON bb.tour_operator_id = oi.user_id
            LEFT JOIN cb_payments cp ON bb.booking_id = cp.booking_id
            ORDER BY bb.date_created DESC
            LIMIT 5";

    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $recentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $recentTransactions = [];
}
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Welcome Section -->
        <div class="bg-blue-600 rounded-lg shadow-lg p-6 mb-6 text-white">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Welcome back, <?= explode(' ', $nameEscaped)[0]; ?>!</h1>
                    <p class="mt-1 text-blue-100">Here's what's happening with your treasury operations today.</p>
                </div>
                <div class="hidden md:block">
                    <span class="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">
                        <?= date('l, F j, Y'); ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <!-- Total Transactions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Total Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $totalTransactions ?></h3>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-chart-line text-blue-500"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="#" class="text-blue-600 text-sm font-medium hover:underline flex items-center">
                        View all bookings
                        <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>

            <!-- Pending Transactions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Pending Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $pendingTransactions ?></h3>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-clock text-yellow-500"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="transaction-pending.php" class="text-yellow-600 text-sm font-medium hover:underline flex items-center">
                        View pending
                        <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>

            <!-- Approved Transactions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Approved Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $approvedTransactions ?></h3>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-check-circle text-green-500"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="transaction-approved.php" class="text-green-600 text-sm font-medium hover:underline flex items-center">
                        View approved
                        <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>

            <!-- Declined Transactions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Declined Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $declinedTransactions ?></h3>
                    </div>
                    <div class="p-3 bg-red-100 rounded-full">
                        <i class="fas fa-times-circle text-red-500"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="#" class="text-red-600 text-sm font-medium hover:underline flex items-center">
                        View declined
                        <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Chart and Recent Transactions -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Chart -->
            <div class="bg-white rounded-lg shadow-md p-6 lg:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Booking Trends</h3>
                    <div class="text-sm text-gray-500">Monthly statistics for <?= date('Y') ?></div>
                </div>
                <div>
                    <canvas id="bookingChart" height="300"></canvas>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Recent Bookings</h3>
                    <a href="transaction-pending.php" class="text-sm text-blue-600 hover:underline">View all</a>
                </div>
                <div class="space-y-4">
                    <?php if (empty($recentTransactions)): ?>
                    <div class="text-center py-4">
                        <div class="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                            <i class="fas fa-exclamation-triangle text-gray-400"></i>
                        </div>
                        <p class="text-gray-500 text-sm">No recent bookings found</p>
                    </div>
                    <?php else: ?>
                        <?php foreach ($recentTransactions as $transaction): ?>
                        <?php
                            $statusColor = 'gray';
                            $statusBg = 'bg-gray-100';
                            $statusIcon = 'question';

                            if ($transaction['treasurer'] === 'Pending') {
                                $statusColor = 'yellow';
                                $statusBg = 'bg-yellow-100';
                                $statusIcon = 'clock';
                            } elseif ($transaction['treasurer'] === 'Approved') {
                                $statusColor = 'green';
                                $statusBg = 'bg-green-100';
                                $statusIcon = 'check-circle';
                            } elseif ($transaction['treasurer'] === 'Declined') {
                                $statusColor = 'red';
                                $statusBg = 'bg-red-100';
                                $statusIcon = 'times-circle';
                            }
                        ?>
                        <div class="flex items-center p-3 border border-gray-100 rounded-lg hover:bg-gray-50">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 rounded-full <?= $statusBg ?> flex items-center justify-center text-<?= $statusColor ?>-600">
                                    <i class="fas fa-<?= $statusIcon ?>"></i>
                                </div>
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars($transaction['referenceNum']) ?></p>
                                    <span class="text-xs px-2 py-1 rounded-full bg-<?= $statusColor ?>-100 text-<?= $statusColor ?>-800">
                                        <?= ucfirst(htmlspecialchars($transaction['treasurer'])) ?>
                                    </span>
                                </div>
                                <div class="flex justify-between mt-1">
                                    <p class="text-xs text-gray-500">
                                        <?= date('M d, Y', strtotime($transaction['date_created'])) ?>
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        <?= htmlspecialchars($transaction['tour_firstname'] . ' ' . $transaction['tour_lastname']) ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('bookingChart').getContext('2d');
    const bookingChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: <?= json_encode($shortMonthNames) ?>,
            datasets: [{
                label: 'Bookings',
                data: <?= json_encode($monthlyData) ?>,
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true,
                pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    titleColor: '#1F2937',
                    bodyColor: '#1F2937',
                    borderColor: 'rgba(59, 130, 246, 0.5)',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    usePointStyle: true,
                    callbacks: {
                        label: function(context) {
                            return `Bookings: ${context.raw}`;
                        }
                    }
                }
            }
        }
    });
});
</script>

<?php
require '_footer.php';
?>