<?php
// Update booking_status enum to include 'cancelled'
require __DIR__ . '/../../../connection/dbconnect.php';

try {
    // Add 'cancelled' to the booking_status enum
    $sql = "ALTER TABLE cb_bookings MODIFY COLUMN booking_status ENUM('draft','voucher','pending','declined','approved','completed','cancelled') DEFAULT 'draft'";
    
    $pdo->exec($sql);
    
    echo "Database schema updated successfully. 'cancelled' status added to booking_status enum.";
    
} catch (PDOException $e) {
    echo "Error updating database schema: " . $e->getMessage();
}
?>
