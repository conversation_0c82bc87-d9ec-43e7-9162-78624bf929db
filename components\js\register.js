/**
 * Enhanced Registration Form JavaScript
 * Provides improved validation, CAPTCHA, and user experience
 */

// Generate a more visually appealing CAPTCHA
function generateCaptcha() {
  // Generate random numbers between 1 and 20 for a slightly more challenging but still simple CAPTCHA
  const num1 = Math.floor(Math.random() * 20) + 1;
  const num2 = Math.floor(Math.random() * 10) + 1;

  // Create the question with a nicer format
  const question = `${num1} + ${num2} = ?`;

  // Update the CAPTCHA display and store the answer
  document.getElementById("captcha-question").textContent = question;
  document.getElementById("captcha-answer").value = (num1 + num2).toString();

  // Add a refresh button if it doesn't exist
  if (!document.getElementById("refresh-captcha")) {
    const captchaContainer = document.getElementById("captcha-question").parentNode;
    const refreshButton = document.createElement("button");
    refreshButton.id = "refresh-captcha";
    refreshButton.type = "button";
    refreshButton.className = "absolute top-2 right-2 text-blue-500 hover:text-blue-700";
    refreshButton.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
      </svg>
    `;
    refreshButton.addEventListener("click", generateCaptcha);
    captchaContainer.style.position = "relative";
    captchaContainer.appendChild(refreshButton);
  }
}

// Ensure only numeric input for phone fields
function enforceNumericInput(inputId) {
  const numericElement = document.getElementById(inputId);
  if (numericElement) {
    // Replace non-numeric characters
    numericElement.value = numericElement.value.replace(/\D/g, "");

    // Format the phone number if it's the right length (optional)
    if (numericElement.value.length === 11) {
      // Add visual feedback for valid phone number
      numericElement.classList.add("border-green-500");
    } else {
      numericElement.classList.remove("border-green-500");
    }
  }
}

// Enhanced form validation
function validateForm(event) {
  let isValid = true;

  // Captcha Validation
  const userAnswer = document.querySelector('input[name="captcha"]').value;
  const correctAnswer = document.getElementById("captcha-answer").value;
  const captchaAlertBox = document.getElementById("captcha-alert");

  if (userAnswer !== correctAnswer) {
    captchaAlertBox.classList.remove("hidden");
    captchaAlertBox.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Incorrect CAPTCHA. Please try again.
      </div>
    `;
    isValid = false;

    // Regenerate CAPTCHA for security
    generateCaptcha();
  } else {
    captchaAlertBox.classList.add("hidden");
  }

  // Password Validation
  const password = document.querySelector('input[name="password"]').value;
  const confirmPassword = document.querySelector('input[name="confirm_password"]').value;
  const passwordAlertBox = document.getElementById("password-alert");

  if (password !== confirmPassword) {
    passwordAlertBox.classList.remove("hidden");
    passwordAlertBox.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Passwords do not match. Please re-enter.
      </div>
    `;
    isValid = false;
  } else {
    passwordAlertBox.classList.add("hidden");
  }

  // Check password strength
  if (password.length < 8) {
    passwordAlertBox.classList.remove("hidden");
    passwordAlertBox.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Password must be at least 8 characters long.
      </div>
    `;
    isValid = false;
  }

  // Prevent form submission if validation fails
  if (!isValid) {
    event.preventDefault();

    // Scroll to the first error
    const firstError = document.querySelector(".hidden:not([id])");
    if (firstError) {
      firstError.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  }
}

// Toggle password visibility
function togglePasswordVisibility() {
  const passwordFields = document.querySelectorAll(".password-field");
  const showPasswordCheckbox = document.getElementById("show-password");

  passwordFields.forEach((field) => {
    field.type = showPasswordCheckbox.checked ? "text" : "password";
  });
}

// Initialize on page load
window.onload = function() {
  generateCaptcha();

  // Add input validation for phone field
  const phoneInput = document.getElementById("phone");
  if (phoneInput) {
    phoneInput.addEventListener("input", function() {
      enforceNumericInput('phone');
    });
  }

  // Add real-time password matching validation
  const confirmPasswordInput = document.getElementById("confirm_password");
  if (confirmPasswordInput) {
    confirmPasswordInput.addEventListener("input", function() {
      const password = document.getElementById("password").value;
      const confirmPassword = this.value;
      const passwordAlertBox = document.getElementById("password-alert");

      if (confirmPassword && password !== confirmPassword) {
        passwordAlertBox.classList.remove("hidden");
        passwordAlertBox.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Passwords do not match.
          </div>
        `;
      } else {
        passwordAlertBox.classList.add("hidden");
      }
    });
  }
};
