<?php
session_start();
include '../../../../connection/dbconnect.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['addPortBtn'])) {

        try {
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                throw new Exception("Invalid CSRF token.");
            }

            $portName = strtoupper(trim($_POST['portName']));
            $fee = $_POST['portFee'];


            $pdo->beginTransaction();

            // Check if port already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM port_list WHERE portName LIKE :portName");
            $stmt->execute([':portName' => '%' . $portName . '%']);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("Port already exists");
            }

            $stmt = $pdo->prepare("INSERT INTO port_list (portName, fee) VALUES (:portname, :fee)");
            $stmt->execute([
                ':portname' => $portName,
                ':fee' => $fee
            ]);

            $pdo->commit();

            $_SESSION['success'] = "New Port has been added: " . $portName;
            header("Location: ../port.php"); // Redirect back to the form
            exit();
        } catch (Exception $e) {
            $pdo->rollBack();
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../port.php"); // Redirect back to the form
            exit();
        }
    }
}


if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['deletePort'])) {

        try {
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                throw new Exception("Invalid CSRF token.");
            }

            $id = $_POST['rowId'];

            $pdo->beginTransaction();

            $stmt = $pdo->prepare("DELETE FROM port_list where id = :id");
            $stmt->execute([
                ':id' => $id
            ]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to delete.');
            }


            $pdo->commit();

            $_SESSION['success'] = "Port has been Deleted";
            header("Location: ../port.php"); // Redirect back to the form
            exit();
        } catch (Exception $e) {
            $pdo->rollBack();
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../port.php"); // Redirect back to the form
            exit();
        }
    }
}
