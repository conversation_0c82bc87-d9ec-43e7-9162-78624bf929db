<?php
session_start();
include '../connection/dbconnect.php';

// Redirect to login.php if session variables are not set
if (empty($_SESSION['operator']) || empty($_SESSION['id'])) {
    header("Location: ../login.php");
    exit();
}

// Generate a CSRF token if it doesn't exist or has expired
if (empty($_SESSION['csrf_token']) || time() > $_SESSION['csrf_token_expiration']) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    $_SESSION['csrf_token_expiration'] = time() + 3600; // 1-hour validity
}

$op = $_SESSION['operator'];
$id = $_SESSION['id'];

// Combined query to fetch operator info and account status
$sql = 'SELECT 
            i.firstname, 
            i.middlename, 
            i.lastname, 
            i.extname,
            a.accountStatus
        FROM 
            operator_info i
        INNER JOIN 
            operator_account a
        ON 
            i.user_id = a.id
        WHERE 
            i.user_id = :id';


$stmt = $pdo->prepare($sql);
$stmt->bindParam(':id', $id, PDO::PARAM_INT);
if (!$stmt->execute()) {
    error_log("Error executing combined query: " . implode(", ", $stmt->errorInfo()));
    die("An error occurred. Please try again later.");
}

$row = $stmt->fetch(PDO::FETCH_ASSOC);
if (!$row) {
    die("Error: User not found.");
}

if ($row && $row['accountStatus'] !== "pending" && $row['accountStatus'] !== "ready" && $row['accountStatus'] !== "declined") {
    header('Location: ../login.php');
    exit();
} else {
    $name = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);

    if ($row['accountStatus'] === "pending") {
        $icon = '
        <div class="w-16 h-16 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-500 mx-auto mb-5">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-10 h-10">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                </svg>
            </div>
        ';
        $title = "We are currently verifying your account.";
        $message = "
        You’ll receive an update within 1-3 business days.
        ";
        $button = '
         <div class="flex flex-col items-center sm:flex-row sm:justify-center">
                <button class="bg-yellow-400 text-dark px-6 py-3 rounded-md font-semibold hover:bg-yellow-500 transition mb-4 sm:mb-0 sm:mr-4 flex items-center" disabled>
                    <svg class="animate-spin h-5 w-5 mr-3 text-dark" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                    </svg>
                    Verifying...
                </button>
            </div>

        ';
    } elseif ($row['accountStatus'] === "ready") {
        $icon = '
          <div class="w-16 h-16 flex items-center justify-center rounded-full bg-green-100 text-green-500 mx-auto mb-5">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-10 h-10">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                </svg>
            </div>
        ';
        $title = "Your account has been verified.";
        $message = "
        You can now use your account to process booking transactions. Thank You!
        ";
        $button = '
        <form action="inc/inc.complete.php" method="POST">
                    <button type="submit" name="activateBtn" class="bg-blue-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-blue-700 transition mb-4 sm:mb-0 sm:mr-4 flex items-center">
                        Go to Dashboard
                    </button>
                </form>
        ';
    } else {

        $sql2 = 'SELECT message from account_declined WHERE user_id = :id';
        $stmt2 = $pdo->prepare($sql2);
        $stmt2->bindParam(':id', $id, PDO::PARAM_INT);
        if (!$stmt2->execute()) {
            error_log("Error executing combined query: " . implode(", ", $stmt->errorInfo()));
            die("An error occurred. Please try again later.");
        }

        $row2 = $stmt2->fetch(PDO::FETCH_ASSOC);
        if (!$row2) {
            die("Error: User not found.");
        }

        $icon = '
        <div class="w-16 h-16 flex items-center justify-center rounded-full bg-red-100 text-red-500 mx-auto mb-5">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-10 h-10">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
                </svg>
            </div>
        ';
        $title = "We are unable to very you account. Please Click the button below to register again";
        $message = strtoupper($row2['message']);
        $button = '
        <form action="inc/inc.complete.php" method="POST">
                    <button type="submit" name="registerAgainBtn" class="bg-red-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-red-700 transition mb-4 sm:mb-0 sm:mr-4 flex items-center">
                        Register Again
                    </button>
                </form>

        ';
    }
}

?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
    <title>Registration Form</title>
</head>

<body class="flex flex-col min-h-screen">
    <!-- Header -->
    <header class="bg-blue-500 text-white p-5 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold uppercase">Hello, <?= $name; ?></h1>
            <p class="text-sm">Account Type: <?= $op; ?></p>
        </div>
        <a
            href="Logout.php"
            class="bg-white text-blue-500 px-4 py-2 rounded-md font-semibold hover:bg-blue-100 transition">
            Logout
        </a>
    </header>

    <!-- Main Content -->
    <div class="flex-grow flex items-center justify-center">
        <main class="text-center max-w-lg">
            <!-- Check Icon -->
            <?= $icon; ?>
            <!-- Congratulation Message -->
            <p class="text-xl font-bold mb-2"><?= $title; ?></p>
            <p class="text-gray-600 mb-6"><?= $message; ?></p>
            <!-- Actions -->
            <div class="flex flex-col items-center sm:flex-row sm:justify-center">
                <?= $button ?>
            </div>

        </main>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-200 p-4 text-center text-sm text-gray-600">
        Copyright © 2025 Municipality of Vinzons, Camarines Norte | All Rights Reserved.
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>

    <?php if (isset($_SESSION['error'])): ?>
        <script>
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: '<?php echo htmlspecialchars($_SESSION['error'], ENT_QUOTES, 'UTF-8'); ?>',
            });
        </script>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>
</body>




</html>