<?php
require '_header.php';

// Sample data for dashboard - in a real application, this would come from database queries
$totalBookings = 125;
$pendingBookings = $notif['Pending'] ?? 15;
$approvedBookings = $notif['Approved'] ?? 85;
$completedBookings = 25;

// Sample data for chart
$monthlyData = [28, 45, 35, 50, 32, 48, 42, 68, 54, 72, 60, $totalBookings];
$monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
?>

<div class="p-4 sm:ml-64">
    <div class="p-4 mt-14"> <!-- Adjusted for top navbar -->
        
        <!-- Welcome Section -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-700 rounded-lg shadow-lg p-6 mb-6 text-white">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Welcome back, <?= explode(' ', $name)[0]; ?>!</h1>
                    <p class="mt-1 text-blue-100">Here's what's happening with your operations today.</p>
                </div>
                <div class="hidden md:block">
                    <span class="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">
                        <?= date('l, F j, Y'); ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- Total Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Total Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $totalBookings ?></h3>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-clipboard-list h-6 w-6 text-blue-500"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-green-500 text-sm font-medium flex items-center">
                        <i class="fas fa-arrow-up h-3 w-3 mr-1"></i>
                        12% increase
                    </span>
                </div>
            </div>

            <!-- Pending Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Pending Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $pendingBookings ?></h3>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-clock h-6 w-6 text-yellow-500"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="transaction-pending.php" class="text-yellow-600 text-sm font-medium hover:underline flex items-center">
                        View pending
                        <i class="fas fa-chevron-right h-3 w-3 ml-1"></i>
                    </a>
                </div>
            </div>

            <!-- Approved Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Approved Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $approvedBookings ?></h3>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-check-circle h-6 w-6 text-green-500"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="transaction-approved.php" class="text-green-600 text-sm font-medium hover:underline flex items-center">
                        View approved
                        <i class="fas fa-chevron-right h-3 w-3 ml-1"></i>
                    </a>
                </div>
            </div>

            <!-- Completed Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Completed Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $completedBookings ?></h3>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-flag-checkered h-6 w-6 text-purple-500"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="transaction-completed.php" class="text-purple-600 text-sm font-medium hover:underline flex items-center">
                        View completed
                        <i class="fas fa-chevron-right h-3 w-3 ml-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Chart and Recent Activity -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Chart -->
            <div class="bg-white rounded-lg shadow-md p-6 lg:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Booking Trends</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded-full hover:bg-blue-200">Monthly</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200">Weekly</button>
                    </div>
                </div>
                <div>
                    <canvas id="bookingChart" height="300"></canvas>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-plus h-4 w-4 text-blue-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-800">New booking request</p>
                            <p class="text-xs text-gray-500">30 minutes ago</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-check h-4 w-4 text-green-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-800">Booking #1234 approved</p>
                            <p class="text-xs text-gray-500">2 hours ago</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-bell h-4 w-4 text-yellow-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-800">Payment reminder sent</p>
                            <p class="text-xs text-gray-500">5 hours ago</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user-plus h-4 w-4 text-purple-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-800">New operator registered</p>
                            <p class="text-xs text-gray-500">Yesterday</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <a href="logs.php" class="text-sm font-medium text-blue-600 hover:underline">View all activity</a>
                </div>
            </div>
        </div>

        <!-- Quick Actions and Operators -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
                <div class="grid grid-cols-2 gap-3">
                    <a href="transaction-pending.php" class="flex flex-col items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                        <div class="p-2 bg-blue-100 rounded-full mb-2">
                            <i class="fas fa-clipboard-check h-5 w-5 text-blue-500"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-700">Review Bookings</span>
                    </a>
                    <a href="operator.php" class="flex flex-col items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                        <div class="p-2 bg-green-100 rounded-full mb-2">
                            <i class="fas fa-users-cog h-5 w-5 text-green-500"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-700">Manage Operators</span>
                    </a>
                    <a href="personnel.php" class="flex flex-col items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                        <div class="p-2 bg-purple-100 rounded-full mb-2">
                            <i class="fas fa-user-tie h-5 w-5 text-purple-500"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-700">Staff</span>
                    </a>
                    <a href="logs.php" class="flex flex-col items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition">
                        <div class="p-2 bg-yellow-100 rounded-full mb-2">
                            <i class="fas fa-clipboard-list h-5 w-5 text-yellow-500"></i>
                        </div>
                        <span class="text-xs font-medium text-gray-700">View Logs</span>
                    </a>
                </div>
            </div>

            <!-- Operators Overview -->
            <div class="bg-white rounded-lg shadow-md p-6 lg:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Operators Overview</h3>
                    <a href="operator.php" class="text-sm font-medium text-blue-600 hover:underline">View all</a>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-route h-4 w-4 text-blue-500"></i>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">Tour Operators</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">15</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">12</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Good</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-hotel h-4 w-4 text-green-500"></i>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">Resort Operators</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">8</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">7</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Good</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-ship h-4 w-4 text-yellow-500"></i>
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">Boat Operators</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">10</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">8</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Attention</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('bookingChart').getContext('2d');
    const bookingChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: <?= json_encode($monthNames) ?>,
            datasets: [{
                label: 'Bookings',
                data: <?= json_encode($monthlyData) ?>,
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true,
                pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false,
                        color: 'rgba(200, 200, 200, 0.2)'
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    padding: 10,
                    titleFont: {
                        size: 14
                    },
                    bodyFont: {
                        size: 13
                    },
                    displayColors: false
                }
            }
        }
    });
});
</script>

<?php
require '_footer.php';
?>