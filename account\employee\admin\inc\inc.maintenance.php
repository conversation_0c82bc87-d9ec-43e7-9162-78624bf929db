<?php
session_start();
include '../../../../connection/dbconnect.php';

// Approved Pending Operator
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['maintenanceBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['accountType'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $id = 1;
            $mtStatus = $_POST['mtState'];
            $adminUsername = $_POST['adminUsername'];

            if ($mtStatus == 1) {
                $setLogs = "Enable maintenance mode";
            } else {
                $setLogs = "Disable maintenance mode";
            }

            // Start transaction
            $pdo->beginTransaction();

            // Update the account Status
            $stmt = $pdo->prepare("UPDATE system SET status_maintenance = :mtStatus WHERE id = :id");
            $stmt->execute(['mtStatus' => $mtStatus, 'id' => $id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update.');
            }

            // Commit the transaction

            // For Logs
            $type = "System - Maintenance";
            $description = "Admin: " . $adminUsername . " : " . $setLogs;
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => $type,
                ':description' => $description
            ]);

            $pdo->commit();

            // Redirect on success
            $_SESSION['success'] = $setLogs;
            header("Location: ../system-maintenance.php");
            exit;
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../system-maintenance.php");
            exit;
        }
    }
}
