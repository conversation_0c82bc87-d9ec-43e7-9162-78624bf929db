<?php
session_start();
include '../../../connection/dbconnect.php';

$id = isset($_SESSION['id']) ? (int) $_SESSION['id'] : 0;

$loginStatus = "isLogout";
$stmt = $pdo->prepare("UPDATE operator_account SET login_status = :loginStatus WHERE id = :id");
$stmt->execute([
    ':loginStatus' => $loginStatus,
    ':id' => $id // Use $id instead of $user['id']
]);

// Unset all of the session variables
$_SESSION = [];

// If it's desired to kill the session, also delete the session cookie.
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(
        session_name(),
        '',
        time() - 42000,
        $params["path"],
        $params["domain"],
        $params["secure"],
        $params["httponly"]
    );
}

// Destroy the session.
session_destroy();

// Redirect to the login page.
header('Location: ../../../login.php');
exit();
