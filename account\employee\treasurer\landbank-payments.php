<?php
/**
 * Landbank Payment Dashboard for Treasurer
 * Monitor and manage Landbank payment transactions
 */

require_once '_header.php';

// Security check for Landbank access
define('LANDBANK_ACCESS', true);
require_once '../../operator/tour/Landbank/config.php';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF protection
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        $_SESSION['error'] = 'Invalid security token';
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;
    }

    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'check_status':
            $transactionId = $_POST['transaction_id'] ?? '';
            if (!empty($transactionId)) {
                try {
                    // Include the status check function if not already loaded
                    if (!function_exists('checkTransactionStatus')) {
                        require_once '../../operator/tour/Landbank/inquireStatus.php';
                    }

                    $result = checkTransactionStatus($transactionId, $pdo);
                    if ($result['success']) {
                        $_SESSION['success'] = 'Status: ' . ($result['status'] ?? 'Unknown');

                        // Update local transaction record if completed
                        if ($result['status'] === 'completed') {
                            logTransaction($pdo, $transactionId, null, 'completed', 'Status verified by treasurer');
                        }
                    } else {
                        $_SESSION['error'] = 'Status check failed: ' . $result['error'];
                    }
                } catch (Exception $e) {
                    $_SESSION['error'] = 'Error checking status: ' . $e->getMessage();
                }
            }
            break;

        case 'mark_failed':
            $transactionId = $_POST['transaction_id'] ?? '';
            $reason = $_POST['reason'] ?? 'Manually marked as failed by treasurer';
            if (!empty($transactionId)) {
                logTransaction($pdo, $transactionId, null, 'failed', null, $reason);
                $_SESSION['success'] = 'Transaction marked as failed';
            }
            break;

        case 'approve_payment':
            $transactionId = $_POST['transaction_id'] ?? '';
            if (!empty($transactionId)) {
                // Get booking ID from transaction
                $stmt = $pdo->prepare("SELECT booking_id FROM landbank_transactions WHERE transaction_id = :id");
                $stmt->execute([':id' => $transactionId]);
                $transaction = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($transaction && $transaction['booking_id']) {
                    updatePaymentStatus($pdo, $transaction['booking_id'], 'paid');
                    logTransaction($pdo, $transactionId, $transaction['booking_id'], 'completed', 'Manually approved by treasurer');
                    $_SESSION['success'] = 'Payment approved and marked as paid';
                }
            }
            break;
    }

    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Get filter parameters
$status = $_GET['status'] ?? '';
$dateFrom = $_GET['date_from'] ?? date('Y-m-d', strtotime('-7 days'));
$dateTo = $_GET['date_to'] ?? date('Y-m-d');

// Get transactions
$transactions = [];
try {
    $sql = "SELECT lt.*, bb.referenceNum, oi.firstname, oi.lastname
            FROM landbank_transactions lt
            LEFT JOIN cb_bookings bb ON lt.booking_id = bb.booking_id
            LEFT JOIN operator_info oi ON bb.tour_operator_id = oi.user_id";
    $params = [];
    $conditions = [];

    if (!empty($status)) {
        $conditions[] = "lt.status = :status";
        $params[':status'] = $status;
    }

    if (!empty($dateFrom)) {
        $conditions[] = "DATE(lt.created_at) >= :date_from";
        $params[':date_from'] = $dateFrom;
    }

    if (!empty($dateTo)) {
        $conditions[] = "DATE(lt.created_at) <= :date_to";
        $params[':date_to'] = $dateTo;
    }

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(' AND ', $conditions);
    }

    $sql .= " ORDER BY lt.created_at DESC LIMIT 100";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

// Get statistics using the simplified function
$stats = getTransactionStats($pdo, $dateFrom, $dateTo);
?>

<div class="p-4 sm:ml-64">
    <div class="p-4 mt-14">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">
                        <i class="fas fa-university text-green-600 mr-2"></i>
                        Landbank Payment Monitoring
                    </h1>
                    <p class="text-gray-600">Monitor and manage Landbank payment transactions</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="window.location.reload()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                <i class="fas fa-check-circle mr-2"></i><?= htmlspecialchars($_SESSION['success']) ?>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <i class="fas fa-exclamation-circle mr-2"></i><?= htmlspecialchars($_SESSION['error']) ?>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <i class="fas fa-exclamation-triangle mr-2"></i><?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <?php foreach ($stats as $stat): ?>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-800 capitalize"><?= htmlspecialchars($stat['status']) ?></h3>
                            <p class="text-2xl font-bold text-blue-600"><?= $stat['count'] ?></p>
                            <p class="text-sm text-gray-600">₱<?= number_format($stat['total_amount'], 2) ?></p>
                        </div>
                        <div class="text-3xl text-gray-400">
                            <?php
                            switch ($stat['status']) {
                                case 'completed':
                                    echo '<i class="fas fa-check-circle text-green-500"></i>';
                                    break;
                                case 'pending':
                                    echo '<i class="fas fa-clock text-yellow-500"></i>';
                                    break;
                                case 'failed':
                                    echo '<i class="fas fa-times-circle text-red-500"></i>';
                                    break;
                                default:
                                    echo '<i class="fas fa-chart-bar"></i>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">
                <i class="fas fa-filter mr-2"></i>Filters
            </h2>
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Statuses</option>
                        <option value="initiated" <?= $status === 'initiated' ? 'selected' : '' ?>>Initiated</option>
                        <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>Pending</option>
                        <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>Completed</option>
                        <option value="failed" <?= $status === 'failed' ? 'selected' : '' ?>>Failed</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date From</label>
                    <input type="date" name="date_from" value="<?= htmlspecialchars($dateFrom) ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Date To</label>
                    <input type="date" name="date_to" value="<?= htmlspecialchars($dateTo) ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>Filter
                    </button>
                </div>
            </form>
        </div>

        <!-- Transactions Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold">
                    <i class="fas fa-list mr-2"></i>Recent Transactions
                </h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($transactions)): ?>
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                    <i class="fas fa-inbox text-4xl mb-2 block"></i>
                                    No transactions found
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($transactions as $transaction): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <span class="font-mono"><?= htmlspecialchars(substr($transaction['transaction_id'], -12)) ?></span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= htmlspecialchars($transaction['referenceNum'] ?? 'N/A') ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= htmlspecialchars(($transaction['firstname'] ?? '') . ' ' . ($transaction['lastname'] ?? '')) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-semibold">
                                        ₱<?= number_format($transaction['amount'], 2) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?php
                                            switch ($transaction['status']) {
                                                case 'completed':
                                                    echo 'bg-green-100 text-green-800';
                                                    break;
                                                case 'pending':
                                                    echo 'bg-yellow-100 text-yellow-800';
                                                    break;
                                                case 'failed':
                                                    echo 'bg-red-100 text-red-800';
                                                    break;
                                                default:
                                                    echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?= htmlspecialchars($transaction['status']) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= date('M j, Y H:i', strtotime($transaction['created_at'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <!-- Check Status -->
                                            <form method="POST" class="inline">
                                                <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                                                <input type="hidden" name="action" value="check_status">
                                                <input type="hidden" name="transaction_id" value="<?= htmlspecialchars($transaction['transaction_id']) ?>">
                                                <button type="submit" class="text-blue-600 hover:text-blue-900" title="Check Status">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </form>

                                            <?php if ($transaction['status'] === 'pending'): ?>
                                                <!-- Approve Payment -->
                                                <form method="POST" class="inline" onsubmit="return confirm('Approve this payment?')">
                                                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                                                    <input type="hidden" name="action" value="approve_payment">
                                                    <input type="hidden" name="transaction_id" value="<?= htmlspecialchars($transaction['transaction_id']) ?>">
                                                    <button type="submit" class="text-green-600 hover:text-green-900" title="Approve Payment">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>

                                                <!-- Mark Failed -->
                                                <form method="POST" class="inline" onsubmit="return confirm('Mark this transaction as failed?')">
                                                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                                                    <input type="hidden" name="action" value="mark_failed">
                                                    <input type="hidden" name="transaction_id" value="<?= htmlspecialchars($transaction['transaction_id']) ?>">
                                                    <button type="submit" class="text-red-600 hover:text-red-900" title="Mark as Failed">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php require_once '_footer.php'; ?>
