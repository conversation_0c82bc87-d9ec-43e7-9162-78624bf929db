<?php
ob_start();
require '_header.php';
if ($accType === "mtho staff") {
    header("Location: home.php");
    exit;
}
ob_end_flush();
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16">
<?= getAutoBreadcrumb() ?>

        <h2 class="text-2xl font-bold text-gray-900">Personnel Accounts</h2>
        <p class="text-sm text-gray-600 mt-2">Oversee and manage personnel information and roles efficiently</p>

        <!-- Modal -->
        <div class="mt-6">
            <!-- Modal toggle -->
            <button data-modal-target="addPersonnel-modal" data-modal-toggle="addPersonnel-modal" class="inline-flex items-center text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-200" type="button">
                <i class="fas fa-user-plus mr-2"></i>
                Add Personnel
            </button>

            <!-- Main modal -->
            <div id="addPersonnel-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-7xl max-h-full">
                    <!-- Modal content -->
                    <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                        <!-- Modal header -->
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-600 to-blue-800 rounded-t-lg">
                            <div class="flex items-center">
                                <i class="fas fa-user-tie text-white mr-2"></i>
                                <h3 class="text-lg font-semibold text-white">
                                    Add New Personnel Account
                                </h3>
                            </div>
                            <button type="button" class="text-white hover:text-gray-200 rounded-full w-7 h-7 flex justify-center items-center transition-colors duration-200 bg-blue-800 hover:bg-blue-900" data-modal-hide="addPersonnel-modal">
                                <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>
                        <form action="inc/inc.personnel.php" method="POST">
                            <div class="p-4 md:p-5 space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Personal Details Section -->
                                    <fieldset class="border p-4 rounded-lg shadow-sm bg-gray-50">
                                        <legend class="text-lg font-semibold text-blue-700 px-2">
                                            <i class="fas fa-user-circle mr-2"></i>Personal Details
                                        </legend>

                                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                        <input type="hidden" value="<?= $row_global['username']; ?>" name="adminUsername">

                                        <div class="block mt-4">
                                            <label class="text-sm font-medium text-gray-700 flex items-center">
                                                <i class="fas fa-user mr-2 text-blue-500"></i>
                                                First Name <span class="text-red-600 ml-1">*</span>
                                            </label>
                                            <div class="mt-1 relative">
                                                <input type="text" name="firstname" class="w-full p-2.5 pl-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" maxlength="20" minlength="5" placeholder="Enter your first name" required>
                                            </div>
                                        </div>

                                        <div class="block mt-4">
                                            <label class="text-sm font-medium text-gray-700 flex items-center">
                                                <i class="fas fa-user-edit mr-2 text-blue-500"></i>
                                                Middle Name <span class="text-gray-400 text-xs ml-1">(Optional)</span>
                                            </label>
                                            <div class="mt-1 relative">
                                                <input type="text" name="middlename" class="w-full p-2.5 pl-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" maxlength="20" minlength="5" placeholder="Enter your middle name">
                                            </div>
                                        </div>

                                        <div class="block mt-4">
                                            <label class="text-sm font-medium text-gray-700 flex items-center">
                                                <i class="fas fa-user-tag mr-2 text-blue-500"></i>
                                                Last Name <span class="text-red-600 ml-1">*</span>
                                            </label>
                                            <div class="mt-1 relative">
                                                <input type="text" name="lastname" class="w-full p-2.5 pl-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" maxlength="20" minlength="3" placeholder="Enter your last name" required>
                                            </div>
                                        </div>

                                        <div class="block mt-4">
                                            <label class="text-sm font-medium text-gray-700 flex items-center">
                                                <i class="fas fa-id-card mr-2 text-blue-500"></i>
                                                Extension Name <span class="text-gray-400 text-xs ml-1">(Optional)</span>
                                            </label>
                                            <div class="mt-1 relative">
                                                <input type="text" name="extname" class="w-full p-2.5 pl-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" maxlength="5" minlength="1" placeholder="e.g., Jr, II, III">
                                            </div>
                                        </div>
                                    </fieldset>

                                    <!-- Account Details Section -->
                                    <fieldset class="border p-4 rounded-lg shadow-sm bg-gray-50">
                                        <legend class="text-lg font-semibold text-blue-700 px-2">
                                            <i class="fas fa-user-shield mr-2"></i>Account Details
                                        </legend>

                                        <div class="block mt-4">
                                            <label class="text-sm font-medium text-gray-700 flex items-center">
                                                <i class="fas fa-envelope mr-2 text-blue-500"></i>
                                                Email Address <span class="text-red-600 ml-1">*</span>
                                            </label>
                                            <div class="mt-1 relative">
                                                <input type="email" name="email" class="w-full p-2.5 pl-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" maxlength="50" minlength="5" placeholder="Enter your email address" required>
                                            </div>
                                        </div>

                                        <div class="block mt-4">
                                            <label class="text-sm font-medium text-gray-700 flex items-center">
                                                <i class="fas fa-user-cog mr-2 text-blue-500"></i>
                                                Account Type <span class="text-red-600 ml-1">*</span>
                                            </label>
                                            <div class="mt-1 relative">
                                                <select class="w-full p-2.5 pl-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" name="accountType">
                                                    <option value="" disabled selected>Select account type...</option>
                                                    <option value="admin">Administrator</option>
                                                    <option value="mtho staff">MTHO staff</option>
                                                    <option value="treasurer">Treasurer</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="block mt-4">
                                            <label class="text-sm font-medium text-gray-700 flex items-center">
                                                <i class="fas fa-user-tag mr-2 text-blue-500"></i>
                                                Username <span class="text-red-600 ml-1">*</span>
                                            </label>
                                            <div class="mt-1 relative">
                                                <input type="text" name="username" class="w-full p-2.5 pl-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500" maxlength="15" minlength="5" placeholder="Choose a username" required>
                                            </div>
                                            <p class="mt-1 text-xs text-gray-500">Username must be 5-15 characters long.</p>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>

                            <div class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                <button data-modal-hide="addPersonnel-modal" type="button" class="flex items-center py-2 px-4 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200 mr-2">
                                    <i class="fas fa-times-circle mr-1.5"></i> Cancel
                                </button>
                                <button type="submit" name="addPersonnelBtn" class="flex items-center py-2 px-4 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 transition-all duration-200">
                                    <i class="fas fa-user-plus mr-1.5"></i> Register Personnel
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- DataTable will handle search and entries per page -->

        <!-- Table -->
        <div class="mt-6 overflow-hidden">
            <div class="overflow-x-auto rounded-lg">
                <table id="search-table" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-responsive">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Username</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Full Name</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Email</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Account Type</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Status</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                        </tr>
                    </thead>
                <tbody>
                    <?php
                    try {
                        $voidAccountType = "user";
                        $sql = "SELECT
                                    i.firstname, i.middlename, i.lastname, i.extname,
                                    a.accType, a.accountStatus, a.email, a.username, a.id
                                FROM
                                    operator_info i
                                INNER JOIN
                                    operator_account a
                                ON
                                    i.user_id = a.id
                                WHERE
                                    a.accType != :accType";
                        $stmt = $pdo->prepare($sql);
                        $stmt->bindParam(':accType', $voidAccountType, PDO::PARAM_STR);
                        $stmt->execute();

                        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        if ($rows) {
                            foreach ($rows as $row) {
                                $fullname = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                                $badgeStatus = $row['accountStatus'] === "Activated"
                                    ? '<span class="inline-flex items-center bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full"><span class="w-2 h-2 me-1 bg-green-500 rounded-full"></span>Active</span>'
                                    : '<span class="inline-flex items-center bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full"><span class="w-2 h-2 me-1 bg-red-500 rounded-full"></span>Deactivated</span>';
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($row['username']); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500"><?= htmlspecialchars($fullname); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500"><?= htmlspecialchars($row['email']); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500">
                                        <?php if($row['accType'] === "admin"): ?>
                                            <span class="inline-flex items-center bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                                <i class="fas fa-user-shield text-xs mr-1"></i> Administrator
                                            </span>
                                        <?php elseif($row['accType'] === "mtho staff"): ?>
                                            <span class="inline-flex items-center bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                                <i class="fas fa-user-tie text-xs mr-1"></i> MTHO Staff
                                            </span>
                                        <?php elseif($row['accType'] === "treasurer"): ?>
                                            <span class="inline-flex items-center bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                                <i class="fas fa-coins text-xs mr-1"></i> Treasurer
                                            </span>
                                        <?php else: ?>
                                            <?= htmlspecialchars($row['accType']); ?>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500"><?= $badgeStatus; ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-center">
                                        <div class="flex items-center justify-center space-x-2">
                                            <button data-modal-target="change-status-modal-<?= $row['id']; ?>" data-modal-toggle="change-status-modal-<?= $row['id']; ?>" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md text-xs px-2 py-1.5 transition-colors duration-200" type="button" title="Change Status">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
                                                </svg>
                                            </button>
                                            <button data-modal-target="reset-password-modal-<?= $row['id']; ?>" data-modal-toggle="reset-password-modal-<?= $row['id']; ?>" class="inline-flex items-center justify-center bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md text-xs px-2 py-1.5 transition-colors duration-200" type="button" title="Reset Password">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5V6.75a4.5 4.5 0 1 1 9 0v3.75M3.75 21.75h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H3.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z" />
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Change Status Modal -->
                                <div id="change-status-modal-<?= $row['id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-md max-h-full">
                                        <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                            <!-- Modal Header -->
                                            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-600 to-blue-800 rounded-t-lg">
                                                <div class="flex items-center">
                                                    <i class="fas fa-exchange-alt text-white mr-2"></i>
                                                    <h3 class="text-lg font-semibold text-white">
                                                        Change Account Status
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white hover:text-gray-200 rounded-full w-7 h-7 flex justify-center items-center transition-colors duration-200 bg-blue-800 hover:bg-blue-900" data-modal-hide="change-status-modal-<?= $row['id']; ?>">
                                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <div class="p-4 md:p-5 text-center">
                                                <form action="inc/inc.personnel.php" method="POST">
                                                    <div class="bg-yellow-50 p-4 rounded-lg mb-4">
                                                        <div class="flex justify-center mb-3">
                                                            <div class="bg-yellow-100 rounded-full p-2">
                                                                <svg class="w-6 h-6 text-yellow-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                                </svg>
                                                            </div>
                                                        </div>
                                                        <h3 class="text-lg font-medium text-gray-800 mb-2">
                                                            Confirmation
                                                        </h3>
                                                        <p class="text-sm text-gray-600 mb-3">
                                                            Are you sure you want to <span class="text-red-600 font-bold">change the current status</span> of this account?
                                                        </p>
                                                        <p class="text-xs text-gray-500 italic">
                                                            Current status: <span class="font-medium"><?= $row['accountStatus'] === "Activated" ? "Active" : "Deactivated" ?></span>
                                                        </p>
                                                    </div>

                                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                    <input type="hidden" value="<?= $row['accountStatus']; ?>" name="rowStatus">
                                                    <input type="hidden" value="<?= $row['username']; ?>" name="username">
                                                    <input type="hidden" value="<?= $row_global['username']; ?>" name="adminUsername">
                                                    <input type="hidden" name="id" value="<?= $row['id']; ?>">

                                                    <div class="flex justify-center space-x-3">
                                                        <button data-modal-hide="change-status-modal-<?= $row['id']; ?>" type="button" class="flex items-center py-2 px-4 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                            <i class="fas fa-times-circle mr-1.5"></i> Cancel
                                                        </button>
                                                        <button type="submit" name="changeStatusBtn" class="flex items-center py-2 px-4 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 transition-all duration-200">
                                                            <i class="fas fa-check-circle mr-1.5"></i> Confirm
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Reset Password Modal -->
                                <div id="reset-password-modal-<?= $row['id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-md max-h-full">
                                        <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                            <!-- Modal Header -->
                                            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-600 to-gray-800 rounded-t-lg">
                                                <div class="flex items-center">
                                                    <i class="fas fa-key text-white mr-2"></i>
                                                    <h3 class="text-lg font-semibold text-white">
                                                        Reset Password
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white hover:text-gray-200 rounded-full w-7 h-7 flex justify-center items-center transition-colors duration-200 bg-gray-700 hover:bg-gray-900" data-modal-hide="reset-password-modal-<?= $row['id']; ?>">
                                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <div class="p-4 md:p-5 text-center">
                                                <form action="inc/inc.personnel.php" method="POST">
                                                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                                                        <div class="flex justify-center mb-3">
                                                            <div class="bg-gray-100 rounded-full p-2">
                                                                <i class="fas fa-lock text-gray-600 text-xl"></i>
                                                            </div>
                                                        </div>
                                                        <h3 class="text-lg font-medium text-gray-800 mb-2">
                                                            Password Reset Confirmation
                                                        </h3>
                                                        <p class="text-sm text-gray-600 mb-3">
                                                            Are you sure you want to <span class="text-red-600 font-bold">reset the password</span> for <span class="font-semibold"><?= htmlspecialchars($row['username']); ?></span>?
                                                        </p>
                                                        <p class="text-xs text-gray-500 italic">
                                                            This action will reset the password to the default value.
                                                        </p>
                                                    </div>

                                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                    <input type="hidden" value="<?= $row['id']; ?>" name="rowId">
                                                    <input type="hidden" value="<?= $row['username']; ?>" name="rowUsername">
                                                    <input type="hidden" value="<?= $row_global['username']; ?>" name="adminUsername">
                                                    <input type="hidden" name="id" value="<?= $row['id']; ?>">

                                                    <div class="flex justify-center space-x-3">
                                                        <button data-modal-hide="reset-password-modal-<?= $row['id']; ?>" type="button" class="flex items-center py-2 px-4 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                            <i class="fas fa-times-circle mr-1.5"></i> Cancel
                                                        </button>
                                                        <button type="submit" name="resetPasswordBtn" class="flex items-center py-2 px-4 text-sm font-medium text-white bg-gray-600 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                            <i class="fas fa-check-circle mr-1.5"></i> Confirm Reset
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="6" class="px-4 sm:px-6 py-4 text-center">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-3 inline-block mx-auto">
                                    <p class="text-sm text-red-600 font-medium">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </p>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>

        <!-- DataTable will handle pagination -->
    </div>
</div>


<?php
require '_footer.php';
?>