<!-- Footer -->
<footer class="mt-auto p-4 bg-white md:p-6 md:flex md:items-center md:justify-between border-t border-gray-200 sm:ml-64">
    <span class="text-sm text-gray-500 sm:text-center">© <?= date('Y') ?> <a href="#" class="hover:underline">Calaguas TDF</a>. All Rights Reserved.
    </span>
    <div class="flex mt-4 space-x-6 sm:justify-center md:mt-0">
        <a href="#" class="text-gray-400 hover:text-blue-600">
            <i class="fab fa-facebook-f"></i>
            <span class="sr-only">Facebook page</span>
        </a>
        <a href="#" class="text-gray-400 hover:text-blue-600">
            <i class="fab fa-instagram"></i>
            <span class="sr-only">Instagram page</span>
        </a>
        <a href="#" class="text-gray-400 hover:text-blue-600">
            <i class="fab fa-twitter"></i>
            <span class="sr-only">Twitter page</span>
        </a>
    </div>
</footer>

<!-- Back to top button -->
<button id="back-to-top" class="fixed bottom-5 right-5 z-50 p-2 bg-blue-600 text-white rounded-full shadow-lg opacity-0 transition-opacity duration-300">
    <i class="fas fa-arrow-up"></i>
</button>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/simple-datatables@9.0.3"></script>

<style>
    /* DataTable styling */
    .datatable-wrapper {
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        width: 100%;
    }

    .datatable-top {
        padding: 0.75rem 1rem;
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
    }

    .datatable-search {
        position: relative;
        width: 100%;
    }

    .datatable-input {
        padding-left: 2.5rem;
        border-radius: 0.375rem;
        border: 1px solid #d1d5db;
        width: 100%;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .datatable-search:before {
        content: "\f002";
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
    }

    .datatable-selector {
        padding: 0.5rem 2rem 0.5rem 1rem;
        border-radius: 0.375rem;
        border: 1px solid #d1d5db;
        font-size: 0.875rem;
        background-position: right 0.5rem center;
        max-width: 100%;
    }

    .datatable-container {
        overflow-x: auto;
        width: 100%;
        -webkit-overflow-scrolling: touch;
    }

    .datatable-table {
        width: 100%;
        border-collapse: collapse;
        min-width: 640px;
        /* Ensures table doesn't get too compressed */
    }

    .datatable-table th {
        background-color: #f9fafb;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
        color: #4b5563;
        padding: 0.75rem 1rem;
        text-align: left;
        white-space: nowrap;
    }

    .datatable-table td {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #e5e7eb;
        font-size: 0.875rem;
    }

    .datatable-table tr:last-child td {
        border-bottom: none;
    }

    .datatable-table tr:hover td {
        background-color: #f3f4f6;
    }

    .datatable-bottom {
        padding: 0.75rem 1rem;
        background-color: #f9fafb;
        border-top: 1px solid #e5e7eb;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;
        gap: 0.5rem;
    }

    @media (max-width: 640px) {
        .datatable-bottom {
            flex-direction: column;
            align-items: flex-start;
        }

        .datatable-info {
            order: 1;
            width: 100%;
            text-align: center;
            margin-top: 0.5rem;
        }

        .datatable-pagination {
            order: 0;
            width: 100%;
            justify-content: center;
        }

        .datatable-selector-container {
            order: 2;
            width: 100%;
            display: flex;
            justify-content: center;
            margin-top: 0.5rem;
        }
    }

    .datatable-info {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .datatable-pagination {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
        justify-content: center;
    }

    .datatable-pagination button {
        border: 1px solid #d1d5db;
        background-color: white;
        color: #4b5563;
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.2s;
        min-width: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .datatable-pagination button:hover:not(.active):not(:disabled) {
        background-color: #f3f4f6;
        color: #1f2937;
    }

    .datatable-pagination button.active {
        background-color: #2563eb;
        color: white;
        border-color: #2563eb;
    }

    .datatable-pagination button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* Responsive table styles */
    @media (max-width: 768px) {
        .datatable-table {
            font-size: 0.875rem;
        }

        .datatable-table th,
        .datatable-table td {
            padding: 0.5rem 0.75rem;
        }
    }

    @media (max-width: 640px) {
        .datatable-table {
            font-size: 0.75rem;
        }

        .datatable-table th,
        .datatable-table td {
            padding: 0.375rem 0.5rem;
        }
    }
        /* SweetAlert Button Styling */
        .swal-confirm-btn {
        background-color: #3085d6 !important;
        color: white !important;
        border: none !important;
        border-radius: 6px !important;
        padding: 10px 20px !important;
        font-size: 16px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        min-width: 80px !important;
        height: auto !important;
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 9999 !important;
    }
    
    .swal-confirm-btn:hover {
        background-color: #2563eb !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
    }
    
    /* Ensure SweetAlert container is properly styled */
    .swal2-popup {
        z-index: 9999 !important;
        position: fixed !important;
    }
    
    .swal2-actions {
        display: flex !important;
        justify-content: center !important;
        margin-top: 20px !important;
    }
    
    .swal2-confirm {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
</style>

<?php if (isset($_SESSION['error'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: '<?php echo htmlspecialchars($_SESSION['error'], ENT_QUOTES, 'UTF-8'); ?>',
                confirmButtonText: 'OK',
                confirmButtonColor: '#dc2626',
                buttonsStyling: true,
                allowOutsideClick: false,
                allowEscapeKey: false,
                customClass: {
                    confirmButton: 'swal-confirm-btn'
                }
            });
        });
    </script>
    <?php unset($_SESSION['error']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['success'])): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            Swal.fire({
                icon: 'success',
                title: 'Done!',
                text: '<?php echo htmlspecialchars($_SESSION['success'], ENT_QUOTES, 'UTF-8'); ?>',
                confirmButtonText: 'OK',
                confirmButtonColor: '#16a34a',
                buttonsStyling: true,
                allowOutsideClick: false,
                allowEscapeKey: false,
                customClass: {
                    confirmButton: 'swal-confirm-btn'
                }
            });
        });
    </script>
    <?php unset($_SESSION['success']); ?>
<?php endif; ?>

<script>
    // Initialize DataTables
    document.addEventListener('DOMContentLoaded', function() {
        // Back to top button
        const backToTopButton = document.getElementById('back-to-top');

        if (backToTopButton) {
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    backToTopButton.classList.remove('opacity-0');
                    backToTopButton.classList.add('opacity-100');
                } else {
                    backToTopButton.classList.remove('opacity-100');
                    backToTopButton.classList.add('opacity-0');
                }
            });

            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // Initialize DataTables
        if (document.getElementById("search-table") && typeof simpleDatatables.DataTable !== 'undefined') {
            const dataTable = new simpleDatatables.DataTable("#search-table", {
                searchable: true,
                sortable: true,
                fixedHeight: false,
                perPage: 10,
                perPageSelect: [5, 10, 15, 20, 25],
                labels: {
                    placeholder: "Search...",
                    perPage: "{select} entries per page",
                    noRows: "No entries found",
                    info: "Showing {start} to {end} of {rows} entries",
                },
                // Add responsive handling
                layout: {
                    top: "{search}",
                    bottom: "{info}{pager}{select}"
                }
            });

            // Add responsive class to the DataTable wrapper
            const dataTableWrapper = document.querySelector('.datatable-wrapper');
            if (dataTableWrapper) {
                dataTableWrapper.classList.add('w-full', 'overflow-hidden');
            }

            // Make sure the table container is responsive
            const tableContainer = document.querySelector('.datatable-container');
            if (tableContainer) {
                tableContainer.classList.add('overflow-x-auto', 'w-full');
            }

            // Adjust table for small screens
            const adjustTableForScreenSize = () => {
                const table = document.querySelector('.datatable-table');
                if (table) {
                    if (window.innerWidth < 640) {
                        table.classList.add('text-sm');
                    } else {
                        table.classList.remove('text-sm');
                    }
                }
            };

            // Run on load and on resize
            adjustTableForScreenSize();
            window.addEventListener('resize', adjustTableForScreenSize);

            // Add a class to the datatable-selector container for responsive styling
            const selectorContainer = document.querySelector('.datatable-dropdown');
            if (selectorContainer) {
                selectorContainer.classList.add('datatable-selector-container');
            }

            // Add responsive scrolling indicator if table is wider than container
            const addScrollIndicator = () => {
                const container = document.querySelector('.datatable-container');
                if (!container) return;

                // Remove any existing indicators
                const existingIndicator = document.querySelector('.table-scroll-indicator');
                if (existingIndicator) {
                    existingIndicator.remove();
                }

                // Check if scrolling is needed
                if (container.scrollWidth > container.clientWidth) {
                    const indicator = document.createElement('div');
                    indicator.className = 'table-scroll-indicator text-xs text-gray-500 text-center mt-2';
                    indicator.innerHTML = '<i class="fas fa-arrows-left-right mr-1"></i> Swipe horizontally to view more data';
                    container.parentNode.insertBefore(indicator, container.nextSibling);
                }
            };

            // Call on initial load and on window resize
            addScrollIndicator();
            window.addEventListener('resize', addScrollIndicator);
        }

        // Initialize filter tables
        if (document.getElementById("filter-logs") && typeof simpleDatatables.DataTable !== 'undefined') {
            const dataTable = new simpleDatatables.DataTable("#filter-logs", {
                tableRender: (_data, table, type) => {
                    if (type === "print") {
                        return table
                    }
                    const tHead = table.childNodes[0]
                    const filterHeaders = {
                        nodeName: "TR",
                        attributes: {
                            class: "search-filtering-row"
                        },
                        childNodes: tHead.childNodes[0].childNodes.map(
                            (_th, index) => ({
                                nodeName: "TH",
                                childNodes: [{
                                    nodeName: "INPUT",
                                    attributes: {
                                        class: "datatable-input",
                                        type: "search",
                                        "data-columns": "[" + index + "]"
                                    }
                                }]
                            })
                        )
                    }
                    tHead.childNodes.push(filterHeaders)
                    return table
                }
            });
        }
    });
</script>
</body>

</html>
