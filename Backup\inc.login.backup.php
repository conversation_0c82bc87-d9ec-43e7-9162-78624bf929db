<?php
include 'connection/dbconnect.php';
session_start();

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Clear any previous errors
    unset($_SESSION['error']);

    $username = trim($_POST['username']);
    $password = trim($_POST['password']);

    try {

        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
            throw new Exception("Something went wrong, Try Again.");
        }

        if (!isset($_SESSION['csrf_token'])) {
            throw new Exception("Invalid CSRF token.");
        }


        // Check if username exists
        $stmt = $pdo->prepare("SELECT a.id, a.password, a.accountStatus, a.accountExpired, a.accType, i.operatorType 
                                FROM operator_account a 
                                LEFT JOIN operator_info i ON a.id = i.user_id 
                                WHERE a.username = :username");
        $stmt->execute([':username' => $username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);


        if (!$user) {
            throw new Exception("Invalid credentials. Please try again.");
        }

        // Check if password matches
        if (!password_verify($password, $user['password'])) {
            throw new Exception("Invalid credentials. Please try again.");
        }

        $sql = 'SELECT status_maintenance FROM system';
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$row) {
            throw new Exception("Something went wrong. Try Again later");
        }
        // Check if system under maintenance
        if ($row['status_maintenance'] === 1) {
            if ($user['accType'] === "admin" || $user['accType'] === "mtho staff") {
                if ($user['accountStatus'] === "Activated") {
                    $_SESSION['id'] = $user['id'];
                    $_SESSION['accountType'] = $user['accType'];
                    if ($user['accType'] === "admin" || $user['accType'] === "mtho staff") {
                        header("Location: account/employee/admin/home.php");
                        exit;
                    } elseif ($user['accType'] === "treasurer") {
                        header("Location: account/employee/treasurer/home.php");
                        exit;
                    }
                } else {
                    throw new Exception("Account is deactivated.");
                }
            } else {
                throw new Exception("System under maintenance, Try Again later");
            }
        } else {
            // Check account status
            if ($user['accountStatus'] === "Incomplete") {

                // Set session variables
                $_SESSION['id'] = $user['id'];
                $_SESSION['operator'] = $user['operatorType'];
                $_SESSION['accountStatus'] = $user['accountStatus'];

                // Redirect to register page
                header("Location: auth/register-other-details.php");
                exit();
            }

            if ($user['accountStatus'] === "pending" || $user['accountStatus'] === "ready" || $user['accountStatus'] === "declined") {
                // Set session variables
                $_SESSION['id'] = $user['id'];
                $_SESSION['operator'] = $user['operatorType'];
                $_SESSION['accountStatus'] = $user['accountStatus'];
                // Redirect to register page
                header("Location: auth/registration-complete.php");
                exit();;
            }

            if ($user['accountStatus'] === "Deactivated") {
                throw new Exception("Account is deactivated.");
            }

            if ($user['accountStatus'] === "Activated") {
                $_SESSION['id'] = $user['id'];
                $_SESSION['accountType'] = $user['accType'];
                $_SESSION['username'] = $username;
                if ($user['accType'] === "user") {
                    $operator = $user['operatorType'];
                    if ($operator == "Tour operator") {
                        header("Location: account/operator/tour/home.php");
                        exit;
                    } elseif ($operator == "Resort operator") {
                        header("Location: account/operator/resort/home.php");
                        exit;
                    } else {
                        header("Location: account/operator/boat/home.php");
                        exit;
                    }
                } else {
                    if ($user['accType'] === "admin" || $user['accType'] === "mtho staff") {
                        header("Location: account/employee/admin/home.php");
                        exit;
                    } elseif ($user['accType'] === "treasurer") {
                        header("Location: account/employee/treasurer/home.php");
                        exit;
                    }
                }
            }
        }
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
        header("Location: login.php");
        exit();;
    }
}
