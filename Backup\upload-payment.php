<?php
ob_start();
require '_header.php';

$getBookingId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
$statusBooking = "pending";
$statusPayment = "unpaid";

if (empty($getBookingId)) {
    header("Location: transaction-draft.php");
    exit;
}

$bookingDetails = getBookingDetails($pdo, $getBookingId);
$disContactPassenger = getDistinctTourist($pdo, "contact_number", $getBookingId);
$disAddressPassenger = getDistinctTourist($pdo, "address", $getBookingId);

if ($bookingDetails['booking_status'] != $statusBooking || $bookingDetails['payment_status'] != $statusPayment) {
    $_SESSION['error'] = "Please try again!";
    header("Location: transaction-pending.php");
    exit;
} else {

    if ($bookingDetails !== null) {
        $extname = $bookingDetails['extname'] ?? ''; // Use null coalescing operator for defaults
        $middlename = $bookingDetails['middlename'] ?? '';
        $operatorName = trim($bookingDetails['firstname'] . ' ' . $middlename . ' ' . $bookingDetails['lastname'] . ' ' . $extname);
        $designationTour = $bookingDetails['tour_operator_designation'];
        $referenceNumber = $bookingDetails['referenceNum'];
        $portName = $bookingDetails['portName'];
        $portFee = $bookingDetails['port_fee'];
        $checkout = $bookingDetails['check_out_date'];
        $adultCount = $bookingDetails['total_adults'];
        $childrenCount = $bookingDetails['total_children'];
        $voucherUse = $bookingDetails['voucher_use'];
        $total_amount = $bookingDetails['total_amount'];
        $paymentStatus = $bookingDetails['payment_status'];
        $paymentMethod = $bookingDetails['payment_method'];
    }

    if ($name !== $operatorName) {
        header("Location: transaction-draft.php");
        exit;
    }
}

// Get Count from inserted tourist info
$counts = getTouristAndCrewCounts($pdo, $getBookingId);
$actual_adultCount = $counts['adults'];
$actual_childrenCount = $counts['children'];
$csrfToken = htmlspecialchars($_SESSION["csrf_token"], ENT_QUOTES, "UTF-8");


if ($paymentMethod === "Bank_Transfer") {
    $importantTxt = '
             <ul class="list-disc pl-5 text-sm">
                    <li>Click <strong>"Pay with Landbank"</strong> to proceed to the Landbank payment portal.</li>
                    <li>Save a copy of your receipt by printing or taking a screenshot after a successful payment.</li>
                    <li>Wait for the treasurer to verify and confirm your payment.</li>
                    <li>The receipt upload option will be enabled once your payment is confirmed.</li>
        </ul>
    ';
    $bankTransferBtn = '
             <button type="button" class="text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-6 py-3 text-center inline-flex items-center space-x-2 shadow-md h-14 w-full md:w-auto">
                <img src="../../../components/img/landbank2.svg" alt="Landbank Logo" class="w-6 h-6">
                <span>Pay with Landbank</span>
            </button>
            ';
} else {
    $importantTxt = '
        <ul class="list-disc pl-5 text-sm">
            <li>Visit the Treasurer Office counter.</li>
            <li>Make the Payment for the Environmental Fee.</li>
            <li>Provide the <b class="uppercase">Reference Number</b> to the cashier.</li>
            <li>Wait for the treasurer to verify and confirm your payment.</li>
            <li>The receipt upload option will be enabled once your payment is confirmed.</li>
        </ul>
        ';
    $bankTransferBtn = '';
}

if ($paymentStatus === "paid") {
    $treasurerConfirmation = '
       <div class="w-full bg-green-300 shadow-md rounded-lg border border-gray-200 p-4 mb-4">
                <div class="flex items-center space-x-4">
                    <!-- Icon Box -->
                    <div class="flex items-center justify-center w-10 h-10 bg-green-500 rounded-md">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-white">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                        </svg>

                    </div>
                    <!-- Text Content -->
                    <div class="flex flex-col">
                        <p class="text-sm font-semibold text-gray-800">Treasurer Confirmation</p>
                        <p class="text-xl uppercase font-bold text-green-800">CONFIRMED</p>
                    </div>
                </div>
            </div>
    ';
    $uploadReceipt = '
    <div class="block mt-4">
        <form method="POST" id="receiptForm" action="inc/inc.payment.php" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
            <input type="hidden" name="booking_id" value="' . $getBookingId . '">
            <input type="hidden" name="referenceNumber" value="' . $referenceNumber . '">

            <label class="block mt-4">
                <span class="text-sm font-medium">Upload Receipt</span>
                <div class="flex items-center">
                    <input name="uploadReceipt" class="block w-full text-sm text-gray-900 border rounded-lg cursor-pointer bg-gray-50 focus:outline-none" type="file" required>
                </div>
            </label>
        </form>
    </div>
    ';
    $proceedBtn = '
        <button data-modal-target="proceed-payment-modal" data-modal-toggle="proceed-payment-modal" type="button" class="inline-flex items-center justify-center w-full md:w-36 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-sm p-3">
                Proceed
        </button>
    ';
} else {
    $treasurerConfirmation = '
     <div class="w-full bg-yellow-200 shadow-md rounded-lg border border-gray-200 p-4 mb-4">
                <div class="flex items-center space-x-4">
                    <!-- Icon Box -->
                    <div class="flex items-center justify-center w-10 h-10 bg-yellow-400 rounded-md">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-gray-800">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                        </svg>
                    </div>
                    <!-- Text Content -->
                    <div class="flex flex-col">
                        <p class="text-sm font-semibold text-gray-800">Treasurer Confirmation</p>
                        <p class="text-xl uppercase font-bold text-yellow-800">PENDING</p>
                    </div>
                </div>
            </div>
    ';
    $uploadReceipt = '
    <div class="block mt-4">
        <label class="block mt-4">
            <span class="text-sm font-medium">Upload Receipt</span>
            <div class="flex items-center">
                <input class="block w-full text-sm text-gray-900 border rounded-lg cursor-pointer bg-gray-50 focus:outline-none" placeholder="Waiting for treasurer confirmation" type="text" required disabled>
            </div>
         </label>
    </div>
    ';
    $proceedBtn = '
      <button type="button" class="inline-flex items-center justify-center w-full md:w-36 bg-gray-300 text-white font-medium rounded-md text-sm p-3" disabled>
                Proceed
        </button>
    ';
}


ob_end_flush();
?>

<div class="p-4 sm:ml-64">
    <div class="border p-6 rounded-xl shadow-2xl bg-white mt-14">
        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-2">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-semibold text-gray-800 hover:text-blue-600">
                        <svg class="w-4 h-4 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                            viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page" class="flex items-center">
                    <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500">Draft</span>
                </li>
                <li aria-current="page" class="flex items-center">
                    <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500">Upload Receipt</span>
                </li>
            </ol>
        </nav>

        <!-- Heading -->
        <h2 class="text-3xl font-bold text-gray-900 mt-6">Upload Receipt
        </h2>

        <!-- Alert -->
        <div class="flex items-center p-4 my-4 text-blue-800 rounded-lg bg-blue-100" role="alert">
            <span class="sr-only">Info</span>
            <div>
                <p class="font-medium">
                    <svg class="shrink-0 inline w-4 h-4 " aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                    </svg>
                    Payment Instructions:
                </p>
                <?= $importantTxt; ?>
            </div>
        </div>


        <!-- Invoice Header -->
        <?= $treasurerConfirmation; ?>
        <div class="p-6 sm:p-8 rounded-lg shadow-md">
            <div class="border-b pb-6 mb-6">
                <div class="flex flex-wrap">
                    <h1 class="text-md font-semibold text-gray-700">
                        Reference Number <br>
                        <span class="text-blue-600 font-bold text-sm md:text-lg lg:text-2xl">
                            <?= $referenceNumber; ?>
                        </span>
                    </h1>
                </div>
            </div>

            <!-- Invoice Detail Table -->
            <div class="mb-6">
                <h2 class="text-lg font-bold text-gray-700 mb-2">Summary</h2>
                <table class="w-full border-collapse text-sm sm:text-base">
                    <thead class="border-b text-gray-500">
                        <tr>
                            <th class="py-2 text-left font-medium">Type</th>
                            <th class="py-2 text-left font-medium">Count</th>
                            <th class="py-2 text-left font-medium">Price</th>
                            <th class="py-2 text-right font-medium">Voucher Use</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-700">
                        <!-- Example Item Row #1 -->
                        <tr class="border-b">
                            <td class="py-3">Adults</td>
                            <td class="py-3"><?= $actual_adultCount ?></td>
                            <td class="py-3">₱<?= $portFee ?></td>
                            <td class="py-3 text-right">
                                <?= $voucherUse; ?>
                            </td>
                        </tr>
                        <!-- Example Item Row #2 -->
                        <tr class="border-b">
                            <td class="py-3">Children</td>
                            <td class="py-3"><?= $actual_childrenCount; ?></td>
                            <td class="py-3">0</td>
                            <td class="py-3 text-right">0</td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="3" class="pt-4 text-right font-bold text-gray-700">
                                Grand Total
                            </td>
                            <td class="pt-4 text-right text-blue-600 font-extrabold">
                                ₱<?= $total_amount; ?>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Hide/Show Upload input -->
            <?= $uploadReceipt; ?>

            <!-- For Bank Transfer -->
            <div class="flex justify-end w-full">
                <?= $bankTransferBtn; ?>
            </div>

            <div class="flex flex-col md:flex-row justify-center md:justify-end mt-4 space-y-4 md:space-y-0 md:space-x-0">
                <?= $proceedBtn; ?>
                <div id="proceed-payment-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                    <div class="relative p-4 w-full max-w-md max-h-full">
                        <div class="relative bg-white rounded-lg shadow">
                            <button type="button" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="proceed-payment-modal">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                            <div class="p-4 md:p-5 text-center">
                                <svg class="mx-auto mb-4 text-gray-400 w-12 h-12" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                </svg>

                                <h3 class="mb-5 text-md font-normal text-gray-700">
                                    Are you sure you want to <span class="text-green-600 font-bold uppercase">Upload</span> this receipt? <br>
                                    <span class="text-xs text-red-600">Be aware that this action is irreversible. Make sure the image is clear</span>
                                </h3>
                                <button data-modal-hide="proceed-payment-modal" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200">
                                    No, cancel
                                </button>
                                <button type="submit" name="uploadReceiptBtn" form="receiptForm" class="text-white bg-green-600 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
                                    Yes, I'm sure
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
require '_footer.php';
?>