<?php
require '_header.php';
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Main Content Section -->
        <div class="border p-4 rounded-lg shadow-lg bg-white mb-6">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                    <li class="inline-flex items-center">
                        <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                            <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                            </svg>
                            Home
                        </a>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Transactions</span>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Pending</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Header Section with Icon -->
            <div class="flex items-center mt-6">
                <div class="bg-yellow-100 p-3 rounded-lg mr-4">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">Pending Bookings</h2>
                    <p class="text-sm text-gray-600 mt-1">Bookings awaiting approval from operators</p>
                </div>
            </div>

            <!-- Table -->
            <div class="mt-6 overflow-hidden">
                <div class="overflow-x-auto rounded-lg">
                    <table id="search-table" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-responsive">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">#</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Tour Operator</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Designation</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Destination</th>
                                <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                    <?php
                    try {
                        $getMthoStatus = "Pending";
                        $getBookingStatus = "pending";

                        $bookingDetails = getBookingDetails($pdo, $getMthoStatus, $getBookingStatus);

                        if ($bookingDetails) {
                            foreach ($bookingDetails as $row) {
                                $booking_id = $row['booking_id'];
                                $operatorName = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                                $designationTour = $row['tour_operator_designation'];
                                $destination = $row['resort_operator_designation'];
                                $referenceNumber = $row['referenceNum'];
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-blue-600"><?= htmlspecialchars($referenceNumber); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($operatorName); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($designationTour); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($destination); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-center">
                                        <a href="view-pending-transaction.php?id=<?= $booking_id; ?>" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md text-xs px-2 sm:px-3 py-1.5 w-full sm:w-auto">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                            </svg>
                                            <span class="whitespace-nowrap">View Details</span>
                                        </a>
                                    </td>
                                </tr>
                        <?php
                            }
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="5" class="px-4 sm:px-6 py-4 text-center">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-3 inline-block mx-auto">
                                    <p class="text-sm text-red-600 font-medium">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </p>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }

                    if (empty($bookingDetails)) {
                    ?>
                        <tr>
                            <td colspan="5" class="px-4 sm:px-6 py-6 sm:py-8 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <div class="bg-yellow-100 p-3 rounded-full mb-3">
                                        <i class="fas fa-inbox text-yellow-500 text-xl"></i>
                                    </div>
                                    <p class="text-gray-500 text-sm font-medium">No pending bookings found</p>
                                    <p class="text-gray-400 text-xs mt-1 max-w-xs mx-auto">When bookings are pending approval, they will appear here</p>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
        </div>
    </div>
</div>


<?php
require '_footer.php';
?>