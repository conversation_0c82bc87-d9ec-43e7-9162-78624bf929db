<?php
require '_header.php';

// Get booking statistics directly from the database
try {
    // Count total bookings
    $sqlTotal = "SELECT COUNT(*) as count FROM cb_bookings WHERE resort_operator_id = :id";
    $stmtTotal = $pdo->prepare($sqlTotal);
    $stmtTotal->bindParam(':id', $id, PDO::PARAM_INT);
    $stmtTotal->execute();
    $totalBookings = $stmtTotal->fetchColumn();

    // Count pending bookings
    $sqlPending = "SELECT COUNT(*) as count FROM cb_bookings WHERE resort_operator_id = :id AND booking_status = 'pending'";
    $stmtPending = $pdo->prepare($sqlPending);
    $stmtPending->bindParam(':id', $id, PDO::PARAM_INT);
    $stmtPending->execute();
    $pendingBookings = $stmtPending->fetchColumn();

    // Count approved bookings
    $sqlApproved = "SELECT COUNT(*) as count FROM cb_bookings WHERE resort_operator_id = :id AND booking_status = 'approved'";
    $stmtApproved = $pdo->prepare($sqlApproved);
    $stmtApproved->bindParam(':id', $id, PDO::PARAM_INT);
    $stmtApproved->execute();
    $approvedBookings = $stmtApproved->fetchColumn();

    // Count declined bookings
    $sqlDeclined = "SELECT COUNT(*) as count FROM cb_bookings WHERE resort_operator_id = :id AND booking_status = 'declined'";
    $stmtDeclined = $pdo->prepare($sqlDeclined);
    $stmtDeclined->bindParam(':id', $id, PDO::PARAM_INT);
    $stmtDeclined->execute();
    $declinedBookings = $stmtDeclined->fetchColumn();
} catch (PDOException $e) {
    // Default values if query fails
    $totalBookings = 0;
    $pendingBookings = 0;
    $approvedBookings = 0;
    $declinedBookings = 0;
}

// Get monthly booking data for chart
$monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
$monthlyData = array_fill(0, 12, 0); // Initialize with zeros

try {
    $sql = "SELECT
                MONTH(date_created) as month,
                COUNT(*) as count
            FROM cb_bookings
            WHERE resort_operator_id = :resort_operator_id
            AND YEAR(date_created) = YEAR(CURRENT_DATE())
            GROUP BY MONTH(date_created)";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':resort_operator_id', $id, PDO::PARAM_INT);
    $stmt->execute();
    $monthlyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($monthlyStats as $stat) {
        $monthIndex = $stat['month'] - 1; // Adjust for 0-based array
        $monthlyData[$monthIndex] = (int)$stat['count'];
    }
} catch (PDOException $e) {
    // Keep default empty data
}

// Get recent bookings (limit to 5)
try {
    $sql = "SELECT
                bb.booking_id,
                bb.referenceNum,
                bb.booking_status,
                bb.check_in_date,
                bb.check_out_date,
                bb.date_created,
                oi.designation AS tour_operator,
                oi.firstname AS tour_firstname,
                oi.lastname AS tour_lastname,
                bob.boatName
            FROM cb_bookings bb
            LEFT JOIN operator_info oi ON bb.tour_operator_id = oi.user_id
            LEFT JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
            WHERE bb.resort_operator_id = :resort_operator_id
            ORDER BY bb.date_created DESC
            LIMIT 5";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':resort_operator_id', $id, PDO::PARAM_INT);
    $stmt->execute();
    $recentBookings = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $recentBookings = [];
}
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Welcome Section -->
        <div class="bg-gradient-to-r from-blue-500 to-blue-700 rounded-lg shadow-lg p-6 mb-6 text-white">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold">Welcome back, <?= explode(' ', $nameEscaped)[0]; ?>!</h1>
                    <p class="mt-1 text-blue-100">Here's what's happening with your resort operations today.</p>
                </div>
                <div class="hidden md:block">
                    <span class="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">
                        <?= date('l, F j, Y'); ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- Total Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Total Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $totalBookings ?></h3>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="#" class="text-blue-600 text-sm font-medium hover:underline flex items-center">
                        View all bookings
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Pending Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Pending Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $pendingBookings ?></h3>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="transaction-pending.php" class="text-yellow-600 text-sm font-medium hover:underline flex items-center">
                        View pending
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Approved Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Approved Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $approvedBookings ?></h3>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="transaction-approved.php" class="text-green-600 text-sm font-medium hover:underline flex items-center">
                        View approved
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Declined Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-red-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Declined Bookings</p>
                        <h3 class="text-2xl font-bold text-gray-800 mt-1"><?= $declinedBookings ?></h3>
                    </div>
                    <div class="p-3 bg-red-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4">
                    <a href="transaction-declined.php" class="text-red-600 text-sm font-medium hover:underline flex items-center">
                        View declined
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>

        <!-- Chart and Recent Bookings -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Chart -->
            <div class="bg-white rounded-lg shadow-md p-6 lg:col-span-2">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Booking Trends</h3>
                    <div class="text-sm text-gray-500">Monthly statistics for <?= date('Y') ?></div>
                </div>
                <div>
                    <canvas id="bookingChart" height="300"></canvas>
                </div>
            </div>

            <!-- Recent Bookings -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Recent Bookings</h3>
                    <a href="#" class="text-sm text-blue-600 hover:underline">View all</a>
                </div>
                <div class="space-y-4">
                    <?php if (empty($recentBookings)): ?>
                    <div class="text-center py-4">
                        <div class="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <p class="text-gray-500 text-sm">No recent bookings found</p>
                    </div>
                    <?php else: ?>
                        <?php foreach ($recentBookings as $booking): ?>
                        <div class="flex items-center p-3 border border-gray-100 rounded-lg hover:bg-gray-50">
                            <div class="flex-shrink-0">
                                <?php
                                $statusColor = 'gray';
                                $statusBg = 'bg-gray-100';
                                $statusIcon = 'question';

                                if ($booking['booking_status'] === 'pending') {
                                    $statusColor = 'yellow';
                                    $statusBg = 'bg-yellow-100';
                                    $statusIcon = 'clock';
                                } elseif ($booking['booking_status'] === 'approved') {
                                    $statusColor = 'green';
                                    $statusBg = 'bg-green-100';
                                    $statusIcon = 'check-circle';
                                } elseif ($booking['booking_status'] === 'declined') {
                                    $statusColor = 'red';
                                    $statusBg = 'bg-red-100';
                                    $statusIcon = 'times-circle';
                                } elseif ($booking['booking_status'] === 'completed') {
                                    $statusColor = 'purple';
                                    $statusBg = 'bg-purple-100';
                                    $statusIcon = 'check-double';
                                }
                                ?>
                                <div class="w-10 h-10 rounded-full <?= $statusBg ?> flex items-center justify-center text-<?= $statusColor ?>-600">
                                    <i class="fas fa-<?= $statusIcon ?>"></i>
                                </div>
                            </div>
                            <div class="ml-3 flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars($booking['referenceNum']) ?></p>
                                    <span class="text-xs px-2 py-1 rounded-full bg-<?= $statusColor ?>-100 text-<?= $statusColor ?>-800">
                                        <?= ucfirst(htmlspecialchars($booking['booking_status'])) ?>
                                    </span>
                                </div>
                                <div class="flex justify-between mt-1">
                                    <p class="text-xs text-gray-500">
                                        <?= date('M d, Y', strtotime($booking['check_in_date'])) ?> -
                                        <?= date('M d, Y', strtotime($booking['check_out_date'])) ?>
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        <?php
                                        if (!empty($booking['tour_firstname']) && !empty($booking['tour_lastname'])) {
                                            echo htmlspecialchars($booking['tour_firstname'] . ' ' . $booking['tour_lastname']);
                                        } else {
                                            echo htmlspecialchars($booking['tour_operator'] ?? 'N/A');
                                        }
                                        ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('bookingChart').getContext('2d');
    const bookingChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: <?= json_encode($monthNames) ?>,
            datasets: [{
                label: 'Bookings',
                data: <?= json_encode($monthlyData) ?>,
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true,
                pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    titleColor: '#1F2937',
                    bodyColor: '#1F2937',
                    borderColor: 'rgba(59, 130, 246, 0.5)',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    usePointStyle: true,
                    callbacks: {
                        label: function(context) {
                            return `Bookings: ${context.raw}`;
                        }
                    }
                }
            }
        }
    });
});
</script>

<?php
require '_footer.php';
?>