<?php
/**
 * Security Functions and Configuration
 *
 * This file contains security-related functions and configurations.
 * Uses configuration from config.php for security settings.
 */

// Load configuration
require_once __DIR__ . '/config.php';

// Security headers (for reference)
define('SECURITY_HEADERS', [
    'X-Content-Type-Options' => 'nosniff',
    'X-Frame-Options' => 'SAMEORIGIN',
    'X-XSS-Protection' => '1; mode=block',
    'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
    'Referrer-Policy' => 'strict-origin-when-cross-origin',
    'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()'
]);

// CSRF token settings
define('CSRF_TOKEN_EXPIRY', 3600); // 1 hour in seconds

/**
 * Function to apply security headers
 */
function apply_security_headers() {
    foreach (SECURITY_HEADERS as $header => $value) {
        header("$header: $value");
    }
}

/**
 * Generate a secure random string
 *
 * @param int $length Length of the random string
 * @return string Secure random string
 */
function generate_secure_string($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Generate a new encryption key
 *
 * @return string New encryption key
 */
function generate_encryption_key() {
    return bin2hex(random_bytes(32));
}

/**
 * Generate a new initialization vector
 *
 * @return string New initialization vector
 */
function generate_iv() {
    return random_bytes(16);
}

/**
 * Generate CSRF token
 *
 * @return string CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION)) {
        session_start();
    }

    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    $_SESSION['csrf_token_time'] = time();

    return $token;
}

/**
 * Validate CSRF token
 *
 * @param string $token Token to validate
 * @return bool True if valid, false otherwise
 */
function validateCSRFToken($token) {
    if (!isset($_SESSION)) {
        session_start();
    }

    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }

    // Check if token has expired
    if (time() - $_SESSION['csrf_token_time'] > CSRF_TOKEN_EXPIRY) {
        unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
        return false;
    }

    return hash_equals($_SESSION['csrf_token'], $token);
}
