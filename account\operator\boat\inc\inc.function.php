<?php

function getBookingDetails(PDO $pdo, $getBoatStatus, $id)
{
    try {
        $sql = "SELECT bb.booking_id AS booking_id,
                       bb.booking_status AS booking_status,
                       bb.referenceNum,
                       bb.check_in_date,
                       bb.check_out_date,
                       oi.designation AS resort_operator_designation, 
                       oi2.designation AS tour_operator_designation, 
                       oi2.firstname, 
                       oi2.middlename, 
                       oi2.lastname, 
                       oi2.extname, 
                       bob.boatName,
                       bob.user_id, 
                       pl.portName,
                       cp.total_adults,
                       cp.total_children,
                       cba.boat 
                FROM cb_bookings bb
                JOIN operator_info oi ON bb.resort_operator_id = oi.user_id
                JOIN operator_info oi2 ON bb.tour_operator_id = oi2.user_id
                JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
                JOIN port_list pl ON bb.port_id = pl.id
                JOIN cb_payments cp ON bb.booking_id = cp.booking_id
                JOIN cb_booking_approvals cba ON bb.booking_id = cba.booking_id
                WHERE cba.boat = :boat
                AND bob.user_id = :boat_id";

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':boat', $getBoatStatus, PDO::PARAM_STR);
        $stmt->bindParam(':boat_id', $id, PDO::PARAM_INT);
        $stmt->execute();

        // Fetch all rows
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $rows ?: []; // Return empty array if no rows found
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return [];
    }
}

function getBookingDetails2(PDO $pdo, $getBookingId)
{
    try {
        $sql = "SELECT 
                    bb.booking_id AS booking_id,
                    bb.booking_status AS booking_status,
                    bb.referenceNum,
                    bb.check_in_date,
                    bb.check_out_date,
                    oi.designation AS resort_operator_designation, 
                    oi2.designation AS tour_operator_designation, 
                    oi2.firstname, 
                    oi2.middlename, 
                    oi2.lastname, 
                    oi2.extname, 
                    bob.boatName,
                    bob.user_id AS boat_operator_id, 
                    pl.portName,
                    cp.total_adults,
                    cp.total_children,
                    cp.total_crew,
                    cba.boat 
                FROM 
                    cb_bookings bb
                    JOIN operator_info oi ON bb.resort_operator_id = oi.user_id
                    JOIN operator_info oi2 ON bb.tour_operator_id = oi2.user_id
                    JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
                    JOIN port_list pl ON bb.port_id = pl.id
                    JOIN cb_payments cp ON bb.booking_id = cp.booking_id
                    JOIN cb_booking_approvals cba ON bb.booking_id = cba.booking_id
                WHERE 
                    bb.booking_id = :booking_id;"; // Fixed WHERE clause

        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_INT);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch single row
        return $row ?: [];
    } catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return [];
    }
}

function getCrewCount(PDO $pdo, $getBookingId)
{
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(CASE WHEN info_type = 'crewMbca' THEN 1 ELSE NULL END) AS crew_count
        FROM cb_tourists
        WHERE info_type = 'crewMbca'
        AND booking_id = :booking_id
    ");
    $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'crew' => $result['crew_count'] ?? 0
    ];
}

function getDistinctTourist(PDO $pdo, $parameter, $getBookingId)
{
    // Make sure $parameter is a valid column name to prevent SQL injection
    $allowedColumns = ["contact_number", "address", "info_type"];
    if (!in_array($parameter, $allowedColumns)) {
        // Handle invalid parameter error
        return false;
    }
    $infoType = "crewMbca";

    $sql = "SELECT DISTINCT $parameter FROM cb_tourists WHERE info_type = :info_type AND booking_id = :booking_id";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':info_type', $infoType, PDO::PARAM_STR);
    $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $stmt->closeCursor();

    return $result;
}

function getNotification(PDO $pdo, $boat_operator_id): array
{
    $stmt = $pdo->prepare("
        SELECT 
            SUM(cba.boat = 'Pending') AS pending_count
        FROM cb_booking_approvals cba
        JOIN cb_bookings bb ON cba.booking_id = bb.booking_id
        JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
        WHERE bob.user_id = :boat_operator_id
    ");
    $stmt->bindParam(':boat_operator_id', $boat_operator_id, PDO::PARAM_STR);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'pending' => (int) ($result['pending_count'] ?? 0)
    ];
}
