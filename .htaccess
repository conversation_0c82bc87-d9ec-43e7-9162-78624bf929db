# Protect environment files
<FilesMatch "^\.env">
    Order Allow,<PERSON><PERSON> from all
</FilesMatch>

# Protect sensitive files
<FilesMatch "\.(env|ini|conf|log)$">
    Order Allow,<PERSON>y
    <PERSON> from all
</FilesMatch>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Disable server signature
ServerSignature Off

# Hide Apache version
<IfModule mod_headers.c>
    Header unset Server
    Header always unset X-Powered-By
</IfModule>
