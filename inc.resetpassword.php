<?php
// Start the session with secure cookie parameters
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);

include 'connection/dbconnect.php';
include 'config/email.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Clear any previous errors
    unset($_SESSION['error']);

    $token = trim($_POST['token'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $confirm_password = trim($_POST['confirm_password'] ?? '');

    try {
        // Ensure CSRF tokens exist and compare them in constant time
        if (
            empty($_SESSION['csrf_token']) || empty($_POST['csrf_token']) ||
            !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])
        ) {
            throw new Exception("Invalid request. Please try again.");
        }

        // Validate required fields
        if (empty($token) || empty($password) || empty($confirm_password)) {
            throw new Exception("All fields are required.");
        }

        // Password complexity check
        if (strlen($password) < 8) {
            throw new Exception("Password must be at least 8 characters long.");
        }

        // Ensure passwords match
        if ($password !== $confirm_password) {
            throw new Exception("Passwords do not match.");
        }

        // Additional password strength validation
        if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $password)) {
            throw new Exception("Password must contain at least one uppercase letter, one lowercase letter, and one number.");
        }

        // Hash the token to compare with database
        $hashedToken = hash('sha256', $token);

        // Check if token exists and is not expired
        $stmt = $pdo->prepare("SELECT id, username, email, accountExpired FROM operator_account WHERE rememberToken = :token AND accountStatus = 'Activated'");
        $stmt->execute([':token' => $hashedToken]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception("Invalid or expired reset token.");
        }

        // Check if token has expired
        $expiry = strtotime($user['accountExpired']);
        if (!$expiry || $expiry <= time()) {
            throw new Exception("Reset token has expired. Please request a new password reset.");
        }

        // Start transaction
        $pdo->beginTransaction();

        // Hash the new password
        $hashed_password = password_hash($password, PASSWORD_BCRYPT);

        // Update the password and clear the reset token
        $stmt = $pdo->prepare("UPDATE operator_account SET password = :password, rememberToken = NULL, accountExpired = NULL WHERE id = :id");
        $stmt->execute([
            ':password' => $hashed_password,
            ':id' => $user['id']
        ]);

        if ($stmt->rowCount() === 0) {
            throw new Exception('Failed to update password. Please try again.');
        }

        // Log the password reset completion
        $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
        $stmt->execute([
            ':type' => 'Reset Complete',
            ':description' => 'Password reset for: ' . $user['username'] . ' (ID: ' . $user['id'] . ')'
        ]);

        // Commit the transaction
        $pdo->commit();

        // Send confirmation email
        $emailSent = sendPasswordResetConfirmationEmail($user['email'], $user['username']);

        // Log email sending attempt
        logEmailAttempt('Password Reset Confirmation', $user['email'], $emailSent, $emailSent ? '' : 'Mail function failed');

        if (!$emailSent) {
            error_log("Failed to send password reset confirmation email to: " . $user['email']);
        }

        // Set success message and redirect to login
        $_SESSION['success'] = "Your password has been successfully reset. You can now log in with your new password.";
        header("Location: login.php");
        exit();

    } catch (Exception $e) {
        // Rollback on error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        $_SESSION['error'] = $e->getMessage();

        // Redirect back to reset form with token
        if (!empty($token)) {
            header("Location: resetpassword.php?token=" . urlencode($token));
        } else {
            header("Location: forgotpassword.php");
        }
        exit();
    }
} else {
    // Redirect if not POST request
    header("Location: forgotpassword.php");
    exit();
}
?>
