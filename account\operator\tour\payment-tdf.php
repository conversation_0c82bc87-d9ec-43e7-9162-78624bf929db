<?php
ob_start();
require '_header.php';

$getBookingId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
$statusBooking = "pending";
$statusPayment = "unpaid";
$otherOperators = "Approved";

if (empty($getBookingId)) {
    header("Location: transaction-draft.php");
    exit;
}

$bookingDetails = getBookingDetails($pdo, $getBookingId);
$disContactPassenger = getDistinctTourist($pdo, "contact_number", $getBookingId);
$disAddressPassenger = getDistinctTourist($pdo, "address", $getBookingId);

// Check if both resort and boat operators have approved
if ($bookingDetails['resort'] != $otherOperators || $bookingDetails['boat'] != $otherOperators) {
    header("Location: transaction-pending.php");
    exit;
}

if ($bookingDetails['booking_status'] != $statusBooking) {
    $_SESSION['error'] = "Please try again!";
    header("Location: transaction-pending.php");
    exit;
} else {
    if ($bookingDetails !== null) {
        $extname = $bookingDetails['extname'] ?? ''; // Use null coalescing operator for defaults
        $middlename = $bookingDetails['middlename'] ?? '';
        $operatorName = trim($bookingDetails['firstname'] . ' ' . $middlename . ' ' . $bookingDetails['lastname'] . ' ' . $extname);
        $designationTour = $bookingDetails['tour_operator_designation'];
        $referenceNumber = $bookingDetails['referenceNum'];
        $portName = $bookingDetails['portName'];
        $portFee = $bookingDetails['port_fee'];
        $checkout = $bookingDetails['check_out_date'];
        $adultCount = $bookingDetails['total_adults'];
        $childrenCount = $bookingDetails['total_children'];
        $voucherUse = $bookingDetails['voucher_use'];
        $total_amount = $bookingDetails['total_amount'];
        $paymentStatus = $bookingDetails['payment_status'];
        $paymentMethod = $bookingDetails['payment_method'] ?? '';
    }

    if ($name !== $operatorName) {
        header("Location: transaction-draft.php");
        exit;
    }
}

// Get Count from inserted tourist info
$counts = getTouristAndCrewCounts($pdo, $getBookingId);
$actual_adultCount = $counts['adults'];
$actual_childrenCount = $counts['children'];
$csrfToken = htmlspecialchars($_SESSION["csrf_token"], ENT_QUOTES, "UTF-8");

// Set default values for dynamic content
$treasurerConfirmation = '';
$uploadReceipt = '';
$proceedBtn = '';
$importantTxt = '';
$bankTransferBtn = '';

// Get the payment method from the URL parameter or database
$selectedPaymentMethod = isset($_GET['method']) ? $_GET['method'] : ($paymentMethod ?: 'Over_the_Counter');

// Set instructions based on selected payment method
if ($selectedPaymentMethod === "Bank_Transfer") {
    $importantTxt = '
        <div class="space-y-2">
            <div class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2">
                    <span class="text-green-600 font-bold text-xs">1</span>
                </div>
                <p class="text-xs">Click <strong class="text-green-700">"Pay with Landbank"</strong> to proceed to the Landbank payment portal.</p>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2">
                    <span class="text-green-600 font-bold text-xs">2</span>
                </div>
                <p class="text-xs">Save a copy of your receipt by printing or taking a screenshot after payment.</p>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2">
                    <span class="text-green-600 font-bold text-xs">3</span>
                </div>
                <p class="text-xs">Wait for the treasurer to verify and confirm your payment.</p>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2">
                    <span class="text-green-600 font-bold text-xs">4</span>
                </div>
                <p class="text-xs">The receipt upload option will be enabled once payment is confirmed.</p>
            </div>
        </div>
    ';
    $bankTransferBtn = ''; // Button is now directly embedded in the Bank Transfer card
} else {
    $importantTxt = '
        <div class="space-y-2">
            <div class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                    <span class="text-blue-600 font-bold text-xs">1</span>
                </div>
                <p class="text-xs">Visit the Treasurer Office counter.</p>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                    <span class="text-blue-600 font-bold text-xs">2</span>
                </div>
                <p class="text-xs">Make the Payment for the Environmental Fee.</p>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                    <span class="text-blue-600 font-bold text-xs">3</span>
                </div>
                <p class="text-xs">Provide the <strong class="text-blue-700 uppercase">Reference Number</strong> to the cashier.</p>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                    <span class="text-blue-600 font-bold text-xs">4</span>
                </div>
                <p class="text-xs">Wait for the treasurer to verify and confirm your payment.</p>
            </div>
            <div class="flex items-start">
                <div class="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                    <span class="text-blue-600 font-bold text-xs">5</span>
                </div>
                <p class="text-xs">The receipt upload option will be enabled once payment is confirmed.</p>
            </div>
        </div>
    ';
    $bankTransferBtn = '';
}

// Set treasurer confirmation and upload options based on payment status
if ($paymentStatus === "paid") {
    $treasurerConfirmation = '
        <div class="w-full bg-gradient-to-r from-green-100 to-green-50 shadow-sm rounded-lg border border-green-200 p-3">
            <div class="flex items-center">
                <!-- Icon Box -->
                <div class="flex items-center justify-center w-10 h-10 bg-green-500 rounded-full shadow-sm mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6 text-white">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                    </svg>
                </div>
                <!-- Text Content -->
                <div>
                    <p class="text-xs font-medium text-green-700">Payment Status</p>
                    <div class="flex items-center">
                        <span class="inline-flex items-center justify-center w-2 h-2 mr-1 bg-green-500 rounded-full">
                            <span class="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-green-400 opacity-75"></span>
                        </span>
                        <p class="text-lg uppercase font-bold text-green-800 tracking-wider">CONFIRMED</p>
                    </div>
                    <p class="text-xs text-green-600">Verified by treasurer</p>
                </div>
            </div>
        </div>
    ';
    $uploadReceipt = '
        <div class="mt-4">
            <form method="POST" id="receiptForm" action="inc/inc.payment.php" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                <input type="hidden" name="booking_id" value="' . $getBookingId . '">
                <input type="hidden" name="referenceNumber" value="' . $referenceNumber . '">
                <input type="hidden" name="paymentMethod" value="' . $selectedPaymentMethod . '">

                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div class="flex items-center mb-2">
                        <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 text-blue-600">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
                            </svg>
                        </div>
                        <h3 class="text-sm font-bold text-blue-800">Upload Payment Receipt</h3>
                    </div>

                    <p class="text-xs text-blue-700 mb-2">Please upload a clear image of your payment receipt</p>

                    <div class="relative border border-dashed border-blue-300 rounded-lg p-4 bg-white hover:bg-blue-50 transition-colors">
                        <input name="uploadReceipt" id="receiptUpload" class="absolute inset-0 w-full h-full opacity-0 z-50 cursor-pointer" type="file" accept="image/*" required>
                        <div id="uploadPlaceholder" class="text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mx-auto h-8 w-8 text-blue-400">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
                            </svg>
                            <h4 class="mt-1 text-xs font-medium text-blue-700">Click to upload or drag and drop</h4>
                            <p class="mt-1 text-xs text-blue-500">PNG, JPG or JPEG (Max. 5MB)</p>
                        </div>

                        <!-- Image Preview (Hidden by default) -->
                        <div id="imagePreview" class="hidden flex flex-col items-center">
                            <div class="relative w-full max-w-xs mx-auto">
                                <img id="previewImage" src="#" alt="Receipt Preview" class="max-h-48 mx-auto object-contain rounded border border-blue-200 shadow-sm">
                                <button type="button" id="removeImage" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 shadow-sm hover:bg-red-600 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </div>
                            <p id="fileName" class="mt-2 text-xs text-blue-600 font-medium truncate max-w-full"></p>
                            <p class="text-xs text-gray-500 mt-1">Click the image to change</p>
                        </div>
                    </div>
                </div>
            </form>
        </div>


    ';
    $proceedBtn = '
        <button id="uploadButton" data-modal-target="proceed-payment-modal" data-modal-toggle="proceed-payment-modal" type="button" class="inline-flex items-center justify-center w-full md:w-auto px-4 py-2 bg-gray-300 text-white font-medium rounded text-xs shadow-sm transition-all duration-300 cursor-not-allowed" disabled>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
            </svg>
            Upload Receipt
        </button>
    ';
} else {
    $treasurerConfirmation = '
        <div class="w-full bg-gradient-to-r from-yellow-100 to-yellow-50 shadow-sm rounded-lg border border-yellow-200 p-3">
            <div class="flex items-center">
                <!-- Icon Box -->
                <div class="flex items-center justify-center w-10 h-10 bg-yellow-400 rounded-full shadow-sm mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6 text-yellow-800">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                    </svg>
                </div>
                <!-- Text Content -->
                <div>
                    <p class="text-xs font-medium text-yellow-700">Payment Status</p>
                    <div class="flex items-center">
                        <span class="inline-flex items-center justify-center w-2 h-2 mr-1 bg-yellow-500 rounded-full pulse-slow">
                            <span class="animate-ping absolute inline-flex h-2 w-2 rounded-full bg-yellow-400 opacity-75"></span>
                        </span>
                        <p class="text-lg uppercase font-bold text-yellow-800 tracking-wider">PENDING</p>
                    </div>
                    <p class="text-xs text-yellow-700">Waiting for treasurer verification</p>
                </div>
            </div>
        </div>
    ';
    $uploadReceipt = '
        <div class="mt-4">
            <div class="bg-gray-100 border border-gray-200 rounded-lg p-3 opacity-75">
                <div class="flex items-center mb-2">
                    <div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 text-gray-500">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
                        </svg>
                    </div>
                    <h3 class="text-sm font-bold text-gray-600">Upload Payment Receipt</h3>
                </div>

                <p class="text-xs text-gray-500 mb-2">Please wait for treasurer confirmation before uploading receipt</p>

                <div class="relative border border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mx-auto h-8 w-8 text-gray-400">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                        </svg>
                        <h4 class="mt-1 text-xs font-medium text-gray-600">Waiting for treasurer confirmation</h4>
                        <p class="mt-1 text-xs text-gray-500">Upload will be enabled once payment is confirmed</p>
                    </div>
                </div>
            </div>
        </div>
    ';
    $proceedBtn = '
        <button type="button" class="inline-flex items-center justify-center w-full md:w-auto px-4 py-2 bg-gray-300 text-gray-500 font-medium rounded text-xs shadow-sm cursor-not-allowed" disabled>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                <path stroke-linecap="round" stroke-linejoin="round" d="M18.364 18.364A9 9 0 0 0 5.636 5.636m12.728 12.728A9 9 0 0 1 5.636 5.636m12.728 12.728L5.636 5.636" />
            </svg>
            Upload Disabled
        </button>
    ';
}

ob_end_flush();
?>

<div class="p-4 sm:ml-64">
    <div class="border p-6 rounded-xl shadow-2xl bg-white mt-14">
        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-2">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-semibold text-gray-800 hover:text-blue-600">
                        <svg class="w-4 h-4 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                            viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page" class="flex items-center">
                    <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500">Pending</span>
                </li>
                <li aria-current="page" class="flex items-center">
                    <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="text-sm font-medium text-gray-500">TDF Payment</span>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-blue-100 p-3 rounded-lg mr-4">
                <i class="fas fa-credit-card text-blue-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Tourism Development Fee Payment</h2>
                <p class="text-md text-gray-600">Complete your transaction with secure payment options</p>
            </div>
        </div>

        <!-- Payment Method Selection Section -->
        <div class="mt-6">
            <!-- Payment Method Selection Label -->
            <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Select Payment Method</h3>
                <p class="text-sm text-gray-600">Choose how you want to pay for your Tourism Development Fee</p>
            </div>

            <!-- Payment Options Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                <!-- Over the Counter Option -->
                <a href="inc/inc.payment.php?id=<?= $getBookingId; ?>&method=Over_the_Counter"
                   class="relative block cursor-pointer transition-all duration-300 hover:shadow-md
                   <?= $selectedPaymentMethod === 'Over_the_Counter' ? 'ring-2 ring-blue-500 shadow-sm' : 'border border-gray-200 hover:border-blue-300' ?>
                   rounded-lg overflow-hidden">

                    <!-- Selection Indicator -->
                    <div class="absolute top-2 right-2 z-10">
                        <div class="w-5 h-5 rounded-full border <?= $selectedPaymentMethod === 'Over_the_Counter' ? 'border-blue-600 bg-blue-600' : 'border-gray-300 bg-white' ?> flex items-center justify-center">
                            <?php if($selectedPaymentMethod === 'Over_the_Counter'): ?>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Card Content -->
                    <div class="p-3 <?= $selectedPaymentMethod === 'Over_the_Counter' ? 'bg-gradient-to-r from-blue-50 to-blue-50' : 'bg-gradient-to-r from-gray-50 to-gray-100 hover:from-blue-50 hover:to-blue-50' ?>">
                        <div class="flex items-center">
                            <div class="bg-white p-2 rounded-full mr-3 shadow-sm <?= $selectedPaymentMethod === 'Over_the_Counter' ? 'ring-2 ring-blue-200' : '' ?>">
                                <i class="fas fa-cash-register text-blue-600 text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-base font-bold text-blue-800">Over the Counter</h3>
                                <p class="text-xs text-blue-700">Pay directly at the municipal office</p>
                            </div>
                        </div>

                        <!-- Call to Action Button -->
                        <div class="mt-3 text-center">
                            <span class="inline-block w-full px-3 py-2 bg-blue-600 text-white text-xs font-medium rounded hover:bg-blue-700 shadow-sm transition-all duration-300 <?= $selectedPaymentMethod === 'Over_the_Counter' ? 'animate-pulse' : '' ?>">
                                <?= $selectedPaymentMethod === 'Over_the_Counter' ? '✓ Selected' : 'Select This Option' ?>
                            </span>
                        </div>
                    </div>
                </a>

                <!-- Bank Transfer Option -->
                <a href="inc/inc.payment.php?id=<?= $getBookingId; ?>&method=Bank_Transfer"
                   class="relative block cursor-pointer transition-all duration-300 hover:shadow-md
                   <?= $selectedPaymentMethod === 'Bank_Transfer' ? 'ring-2 ring-green-500 shadow-sm' : 'border border-gray-200 hover:border-green-300' ?>
                   rounded-lg overflow-hidden">

                    <!-- Selection Indicator -->
                    <div class="absolute top-2 right-2 z-10">
                        <div class="w-5 h-5 rounded-full border <?= $selectedPaymentMethod === 'Bank_Transfer' ? 'border-green-600 bg-green-600' : 'border-gray-300 bg-white' ?> flex items-center justify-center">
                            <?php if($selectedPaymentMethod === 'Bank_Transfer'): ?>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Card Content -->
                    <div class="p-3 <?= $selectedPaymentMethod === 'Bank_Transfer' ? 'bg-gradient-to-r from-green-50 to-green-50' : 'bg-gradient-to-r from-gray-50 to-gray-100 hover:from-green-50 hover:to-green-50' ?>">
                        <div class="flex items-center">
                            <div class="bg-white p-2 rounded-full mr-3 shadow-sm <?= $selectedPaymentMethod === 'Bank_Transfer' ? 'ring-2 ring-green-200' : '' ?>">
                                <i class="fas fa-university text-green-600 text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-base font-bold text-green-800">Bank Transfer</h3>
                                <p class="text-xs text-green-700">Transfer payment via Landbank</p>
                            </div>
                        </div>

                        <!-- Call to Action Button -->
                        <div class="mt-3 text-center">
                            <span class="inline-block w-full px-3 py-2 bg-green-600 text-white text-xs font-medium rounded hover:bg-green-700 shadow-sm transition-all duration-300 <?= $selectedPaymentMethod === 'Bank_Transfer' ? 'animate-pulse' : '' ?>">
                                <?= $selectedPaymentMethod === 'Bank_Transfer' ? '✓ Selected' : 'Select This Option' ?>
                            </span>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Payment Instructions and Receipt Upload Section -->

        <!-- Alert with Payment Instructions -->
        <div class="p-3 my-4 rounded-lg shadow-sm border-l-4 <?= $selectedPaymentMethod === 'Bank_Transfer' ? 'border-l-green-500 bg-gradient-to-r from-green-50 to-white' : 'border-l-blue-500 bg-gradient-to-r from-blue-50 to-white' ?>" role="alert">
            <div class="flex items-center mb-2">
                <div class="flex-shrink-0 w-7 h-7 rounded-full <?= $selectedPaymentMethod === 'Bank_Transfer' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600' ?> flex items-center justify-center mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                    </svg>
                </div>
                <h3 class="text-base font-bold <?= $selectedPaymentMethod === 'Bank_Transfer' ? 'text-green-800' : 'text-blue-800' ?>">Payment Instructions</h3>
            </div>
            <div class="ml-2">
                <?= $importantTxt; ?>
            </div>

            <?php if($selectedPaymentMethod === 'Bank_Transfer'): ?>
            <!-- Landbank Payment Button (Only shown when Bank Transfer is selected) -->
            <div class="mt-3 pt-3 border-t border-green-200">
                <div class="flex items-center mb-2">
                    <div class="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 text-green-600">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5z" />
                        </svg>
                    </div>
                    <h3 class="text-sm font-bold text-green-800">Online Payment Option</h3>
                </div>
                <button type="button" id="landbankPayButton" onclick="processLandbankPayment()" class="text-white bg-green-600 hover:bg-green-700 focus:outline-none font-medium rounded text-sm px-4 py-2 text-center inline-flex items-center justify-center space-x-2 shadow-sm w-full transition-all duration-300">
                    <img src="../../../components/img/landbank2.svg" alt="Landbank Logo" class="w-5 h-5">
                    <span class="text-sm font-bold">Pay with Landbank</span>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-3 h-3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 19.5l15-15m0 0H8.25m11.25 0v11.25" />
                    </svg>
                </button>
            </div>
            <?php endif; ?>
        </div>

        <!-- Treasurer Confirmation Status -->
        <div class="mb-6">
            <?= $treasurerConfirmation; ?>
        </div>

        <!-- Invoice Details -->
        <div class="p-4 rounded-lg shadow-md bg-white border border-gray-100">
            <!-- Reference Number Section -->
            <div class="flex items-center justify-between border-b pb-3 mb-3">
                <div>
                    <p class="text-xs font-medium text-gray-500 mb-1">Reference Number</p>
                    <div class="flex items-center">
                        <span class="text-blue-600 font-bold text-base tracking-wide">
                            <?= $referenceNumber; ?>
                        </span>
                        <button class="ml-1 text-gray-400 hover:text-blue-600 transition-colors" title="Copy to clipboard" onclick="navigator.clipboard.writeText('<?= $referenceNumber; ?>')">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="text-right">
                        <p class="text-xs font-medium text-gray-500 mb-1">Check-out Date</p>
                        <p class="text-sm font-semibold text-gray-800"><?= date('F j, Y', strtotime($checkout)); ?></p>
                    </div>
                </div>
            </div>

            <!-- Invoice Detail Table -->
            <div class="mb-4">
                <div class="flex items-center mb-2">
                    <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-3 h-3 text-blue-600">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z" />
                        </svg>
                    </div>
                    <h2 class="text-sm font-bold text-gray-800">Payment Summary</h2>
                </div>

                <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                    <table class="w-full border-collapse text-xs">
                        <thead class="border-b border-gray-300 text-gray-600">
                            <tr>
                                <th class="py-2 text-left font-medium">Type</th>
                                <th class="py-2 text-center font-medium">Count</th>
                                <th class="py-2 text-right font-medium">Price</th>
                                <th class="py-2 text-right font-medium">Subtotal</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-700">
                            <!-- Adults Row -->
                            <tr class="border-b border-gray-200">
                                <td class="py-2 font-medium">Adults</td>
                                <td class="py-2 text-center"><?= $actual_adultCount ?></td>
                                <td class="py-2 text-right">₱<?= number_format($portFee, 2) ?></td>
                                <td class="py-2 text-right font-semibold">₱<?= number_format($actual_adultCount * $portFee, 2) ?></td>
                            </tr>
                            <!-- Children Row -->
                            <tr>
                                <td class="py-2 font-medium">Children</td>
                                <td class="py-2 text-center"><?= $actual_childrenCount; ?></td>
                                <td class="py-2 text-right">₱0.00</td>
                                <td class="py-2 text-right font-semibold">₱0.00</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Grand Total Outside Table -->
                <div class="mt-3 flex justify-end items-center">
                    <span class="text-sm font-bold text-gray-700 mr-2">Grand Total:</span>
                    <span class="text-lg text-blue-600 font-extrabold">₱<?= number_format($total_amount, 2); ?></span>
                </div>
            </div>

            <!-- Receipt Upload Section -->
            <?= $uploadReceipt; ?>

            <!-- Proceed Button and Modal -->
            <div class="flex flex-col md:flex-row justify-center md:justify-end mt-6 space-y-4 md:space-y-0 md:space-x-0">
                <?= $proceedBtn; ?>

                <!-- Landbank Payment Processing Modal -->
                <div id="landbank-processing-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full backdrop-blur-sm bg-gray-900/50">
                    <div class="relative p-4 w-full max-w-md max-h-full">
                        <div class="relative bg-white rounded-xl shadow-2xl border border-gray-200">
                            <!-- Modal Content -->
                            <div class="p-6 text-center">
                                <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center">
                                    <svg class="animate-spin h-8 w-8 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </div>

                                <h3 class="mb-2 text-xl font-bold text-gray-800">Processing Payment</h3>
                                <p class="mb-4 text-gray-600">
                                    Please wait while we prepare your Landbank payment...
                                </p>

                                <div class="p-3 mb-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="flex items-center text-left">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-blue-600 mr-2 flex-shrink-0">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                                        </svg>
                                        <span class="text-sm text-blue-800">Do not close this window or navigate away</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Confirmation Modal -->
                <div id="proceed-payment-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full backdrop-blur-sm bg-gray-900/50">
                    <div class="relative p-4 w-full max-w-md max-h-full">
                        <div class="relative bg-white rounded-xl shadow-2xl border border-gray-200">
                            <!-- Modal Close Button -->
                            <button type="button" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center transition-colors" data-modal-hide="proceed-payment-modal">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>

                            <!-- Modal Content -->
                            <div class="p-6 text-center">
                                <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-10 h-10 text-green-600">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z" />
                                    </svg>
                                </div>

                                <h3 class="mb-2 text-xl font-bold text-gray-800">Confirm Receipt Upload</h3>

                                <p class="mb-6 text-gray-600">
                                    Are you sure you want to upload this receipt? This action cannot be undone.
                                </p>

                                <!-- Preview of selected image -->
                                <div id="modalImagePreview" class="mb-4 p-3 bg-white border border-gray-200 rounded-lg hidden">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Receipt Preview:</h4>
                                    <div class="flex justify-center">
                                        <img id="modalPreviewImage" src="#" alt="Receipt Preview" class="max-h-48 object-contain rounded border border-gray-200">
                                    </div>
                                    <p id="modalFileName" class="mt-2 text-xs text-gray-600 text-center"></p>
                                </div>

                                <div class="p-3 mb-5 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <div class="flex items-center text-left">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-yellow-600 mr-2 flex-shrink-0">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
                                        </svg>
                                        <span class="text-sm text-yellow-800">Make sure the image is clear and all details are visible</span>
                                    </div>
                                </div>

                                <div class="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-3">
                                    <button data-modal-hide="proceed-payment-modal" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 transition-colors">
                                        Cancel
                                    </button>
                                    <button type="submit" name="uploadReceiptBtn" form="receiptForm" class="text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center transition-colors">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
                                        </svg>
                                        Confirm Upload
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


<?php
require '_footer.php';
?>

<!-- JavaScript for image preview -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Main form elements
    const fileInput = document.getElementById('receiptUpload');
    const previewContainer = document.getElementById('imagePreview');
    const uploadPlaceholder = document.getElementById('uploadPlaceholder');
    const previewImage = document.getElementById('previewImage');
    const fileNameDisplay = document.getElementById('fileName');
    const removeButton = document.getElementById('removeImage');

    // Modal elements
    const modalPreviewContainer = document.getElementById('modalImagePreview');
    const modalPreviewImage = document.getElementById('modalPreviewImage');
    const modalFileName = document.getElementById('modalFileName');
    const modalToggleButton = document.getElementById('uploadButton');

    if (fileInput) {
        fileInput.addEventListener('change', function(event) {
            const file = event.target.files[0];

            if (file) {
                // Check if file is an image
                if (!file.type.match('image.*')) {
                    alert('Please select an image file (PNG, JPG or JPEG)');
                    fileInput.value = '';
                    return;
                }

                // Check file size (5MB max)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size exceeds 5MB. Please select a smaller file.');
                    fileInput.value = '';
                    return;
                }

                // Display file name
                fileNameDisplay.textContent = file.name;
                if (modalFileName) modalFileName.textContent = file.name;

                // Create a preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Update main preview
                    previewImage.src = e.target.result;
                    uploadPlaceholder.classList.add('hidden');
                    previewContainer.classList.remove('hidden');

                    // Update modal preview
                    if (modalPreviewImage) modalPreviewImage.src = e.target.result;
                    if (modalPreviewContainer) modalPreviewContainer.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        });

        // Remove image when clicking the remove button
        if (removeButton) {
            removeButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                fileInput.value = '';
                previewImage.src = '#';
                previewContainer.classList.add('hidden');
                uploadPlaceholder.classList.remove('hidden');

                // Also reset modal preview
                if (modalPreviewContainer) modalPreviewContainer.classList.add('hidden');
            });
        }

        // Allow clicking on the preview to trigger file input
        if (previewContainer) {
            previewContainer.addEventListener('click', function(e) {
                // Don't trigger if clicking on the remove button
                if (e.target !== removeButton && !removeButton.contains(e.target)) {
                    fileInput.click();
                }
            });
        }

        // Update modal button state based on file selection
        if (modalToggleButton) {
            // Initial state check
            modalToggleButton.disabled = !fileInput.files.length;
            modalToggleButton.classList.toggle('cursor-not-allowed', !fileInput.files.length);
            modalToggleButton.classList.toggle('bg-gray-300', !fileInput.files.length);
            modalToggleButton.classList.toggle('bg-green-600', fileInput.files.length > 0);

            // Update on file change
            fileInput.addEventListener('change', function() {
                const hasFile = fileInput.files.length > 0;
                modalToggleButton.disabled = !hasFile;
                modalToggleButton.classList.toggle('cursor-not-allowed', !hasFile);
                modalToggleButton.classList.toggle('bg-gray-300', !hasFile);
                modalToggleButton.classList.toggle('bg-green-600', hasFile);
                modalToggleButton.classList.toggle('hover:bg-green-700', hasFile);
            });
        }
    }

    // Landbank Payment Processing
    window.processLandbankPayment = function() {
        const button = document.getElementById('landbankPayButton');
        const originalContent = button.innerHTML;
        const processingModal = document.getElementById('landbank-processing-modal');

        // Show processing modal
        processingModal.classList.remove('hidden');
        processingModal.classList.add('flex');

        // Show loading state on button
        button.disabled = true;
        button.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
        `;

        // Generate CSRF token if not exists
        if (!window.csrfToken) {
            window.csrfToken = '<?= $csrfToken ?>';
        }

        // Make request to Landbank payment system
        fetch('Landbank/bankTransfer.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'booking_id': <?= $getBookingId ?>,
                'csrf_token': window.csrfToken
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message briefly before redirect
                button.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Redirecting to Landbank...
                `;

                // Redirect to Landbank payment portal
                setTimeout(() => {
                    window.location.href = data.redirect_url;
                }, 1000);
            } else {
                // Hide processing modal
                processingModal.classList.add('hidden');
                processingModal.classList.remove('flex');

                // Show error message
                alert('Payment Error: ' + data.error);

                // Reset button
                button.disabled = false;
                button.innerHTML = originalContent;
            }
        })
        .catch(error => {
            console.error('Payment processing error:', error);

            // Hide processing modal
            processingModal.classList.add('hidden');
            processingModal.classList.remove('flex');

            alert('An error occurred while processing your payment. Please try again.');

            // Reset button
            button.disabled = false;
            button.innerHTML = originalContent;
        });
    };
});
</script>
