<?php
require '_header.php';
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Main Content Section -->
        <div class="border p-4 rounded-lg shadow-lg bg-white mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Transactions</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Approved</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-green-100 p-3 rounded-lg mr-4">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Approved Bookings</h2>
                <p class="text-sm text-gray-600 mt-1">Files Ready to downloads.</p>
            </div>
        </div>

        <p class="text-sm text-gray-600 mt-2"></p>
        <!-- Table -->
        <div class="mt-4 overflow-hidden">
            <div class="overflow-x-auto rounded-lg">
                <table id="search-table" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-responsive">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">#</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Resort</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Boat</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                        </tr>
                    </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php
                    try {
                        $booking_status = "approved";

                        $bookingDetails = getAllBookingDetails($pdo, $booking_status);

                        if ($bookingDetails) {
                            foreach ($bookingDetails as $row) {
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($row['referenceNum']); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500 text-center"><?= htmlspecialchars($row['boatName']); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-center">
                                        <button data-modal-target="view-approvals-modal-<?= $row['booking_id']; ?>" data-modal-toggle="view-approvals-modal-<?= $row['booking_id']; ?>" type="button" class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-2 sm:px-3 py-1.5 w-full sm:w-auto">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                            </svg>
                                            <span class="whitespace-nowrap">Download</span>
                                        </button>
                                    </td>
                                </tr>
                                <!-- View Passenger Modal -->
                                <div id="view-approvals-modal-<?= $row['booking_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative w-full max-w-lg max-h-full"> <!-- Increased max-width to max-w-lg -->
                                        <!-- Modal Container -->
                                        <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                            <!-- Modal Header -->
                                            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-green-600 to-green-700 rounded-t-lg">
                                                <div class="flex items-center">
                                                    <i class="fas fa-file-download text-white mr-2"></i>
                                                    <h3 class="text-xl font-semibold text-white">
                                                        Download Documents
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-green-800 hover:bg-green-900" data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>">
                                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <!-- Modal Body -->
                                            <div class="p-5 bg-white">
                                                <!-- Booking Information -->
                                                <div class="mb-4 bg-gray-50 p-4 rounded-lg border border-gray-200">
                                                    <div class="flex items-center mb-2">
                                                        <i class="fas fa-info-circle text-green-600 mr-2"></i>
                                                        <h4 class="text-sm font-semibold text-gray-800">Booking Information</h4>
                                                    </div>
                                                    <div class="grid grid-cols-2 gap-3">
                                                        <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                            <p class="text-xs font-medium text-gray-700">Reference #:</p>
                                                            <p class="text-sm font-semibold text-gray-900"><?= htmlspecialchars($row['referenceNum']); ?></p>
                                                        </div>
                                                        <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                            <p class="text-xs font-medium text-gray-700">Resort:</p>
                                                            <p class="text-sm font-semibold text-gray-900"><?= htmlspecialchars($row['designation']); ?></p>
                                                        </div>
                                                        <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                            <p class="text-xs font-medium text-gray-700">Boat:</p>
                                                            <p class="text-sm font-semibold text-gray-900"><?= htmlspecialchars($row['boatName']); ?></p>
                                                        </div>
                                                        <div class="bg-white p-3 rounded-md shadow-sm border border-gray-200">
                                                            <p class="text-xs font-medium text-gray-700">Status:</p>
                                                            <p class="text-sm font-semibold text-green-600">Approved</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Available Documents -->
                                                <div class="mb-2">
                                                    <div class="flex items-center mb-3">
                                                        <i class="fas fa-file-alt text-green-600 mr-2"></i>
                                                        <h4 class="text-sm font-semibold text-gray-800">Available Documents</h4>
                                                    </div>
                                                    <div class="space-y-3">
                                                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                                            <div class="flex items-center justify-between p-4">
                                                                <div class="flex items-center">
                                                                    <div class="bg-green-100 p-2 rounded-lg mr-3">
                                                                        <i class="fas fa-id-card text-green-600"></i>
                                                                    </div>
                                                                    <div>
                                                                        <h5 class="text-sm font-medium text-gray-900">Tourism Pass</h5>
                                                                        <p class="text-xs text-gray-500">Official tourism pass document</p>
                                                                    </div>
                                                                </div>
                                                                <a href="download/tourism-pass.php?id=<?= $row['booking_id']; ?>" class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-3 py-2 transition-colors duration-200">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                                                    </svg>
                                                                    Download
                                                                </a>
                                                            </div>
                                                        </div>

                                                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                                            <div class="flex items-center justify-between p-4">
                                                                <div class="flex items-center">
                                                                    <div class="bg-green-100 p-2 rounded-lg mr-3">
                                                                        <i class="fas fa-list-alt text-green-600"></i>
                                                                    </div>
                                                                    <div>
                                                                        <h5 class="text-sm font-medium text-gray-900">Manifesto</h5>
                                                                        <p class="text-xs text-gray-500">Passenger and crew manifest</p>
                                                                    </div>
                                                                </div>
                                                                <a href="download/manifesto.php?id=<?= $row['booking_id']; ?>" class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-3 py-2 transition-colors duration-200">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                                                    </svg>
                                                                    Download
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Modal Footer -->
                                            <div class="flex justify-between items-center p-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                                                <a href="#" onclick="downloadAllDocuments(<?= $row['booking_id']; ?>); return false;" class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-4 py-2 transition-colors duration-200">
                                                    <i class="fas fa-download mr-1"></i> Download All
                                                </a>
                                                <button data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>" type="button" class="py-2 px-4 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                    Close
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="4" class="px-4 sm:px-6 py-4 text-center">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-3 inline-block mx-auto">
                                    <p class="text-sm text-red-600 font-medium">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </p>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
        </div>
    </div>


<script>
    // Function to download all documents for a booking
    function downloadAllDocuments(bookingId) {
        // Create an array of download URLs
        const downloadUrls = [
            `download/tourism-pass.php?id=${bookingId}`,
            `download/manifesto.php?id=${bookingId}`
        ];

        // Function to trigger downloads sequentially with a small delay
        function downloadSequentially(index) {
            if (index >= downloadUrls.length) return;

            // Create a hidden iframe for the download
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            document.body.appendChild(iframe);

            // Set the source to trigger the download
            iframe.src = downloadUrls[index];

            // Schedule the next download after a short delay
            setTimeout(() => {
                // Remove the iframe after a delay to ensure download starts
                setTimeout(() => {
                    document.body.removeChild(iframe);
                }, 1000);

                // Start the next download
                downloadSequentially(index + 1);
            }, 800);
        }

        // Start the sequential download process
        downloadSequentially(0);
    }
</script>

<?php
require '_footer.php';
?>