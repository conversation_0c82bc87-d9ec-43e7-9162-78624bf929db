<?php
session_start();
include '../../connection/dbconnect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['businessUpdateBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['operator'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $designation = strtoupper(trim($_POST['designation']));
            $op = $_SESSION['operator'];
            $id = $_SESSION['id'];

            // Start transaction
            $pdo->beginTransaction();

            // Check if designation is empty for the current user
            $stmt = $pdo->prepare("SELECT designation FROM operator_info WHERE user_id = :user_id");
            $stmt->execute(['user_id' => $id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result && empty($result['designation'])) {
                // Update the empty designation
                $stmt = $pdo->prepare("UPDATE operator_info SET designation = :designation WHERE user_id = :user_id");
                $stmt->execute(['designation' => $designation, 'user_id' => $id]);

                if ($stmt->rowCount() === 0) {
                    throw new Exception('Failed to update the designation.');
                }
            } else {
                // Check if designation already exists for the same operator type and full name
                $stmt = $pdo->prepare("
                SELECT COUNT(*) 
                FROM operator_info 
                WHERE designation = :designation 
                  AND operatorType = :operatorType 
                  AND CONCAT(firstname, middlename, lastname, extname) = (
                    SELECT CONCAT(firstname, middlename, lastname, extname) 
                    FROM operator_info 
                    WHERE user_id = :user_id
                  )
            ");
                $stmt->execute(['designation' => $designation, 'operatorType' => $op, 'user_id' => $id]);

                if ($stmt->fetchColumn() > 0) {
                    throw new Exception('The same designation already exists for the provided operator type and full name.');
                }
            }

            $pdo->commit();
            header("Location: ../register-other-details.php");
            exit;
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}

// Delete Resort Name
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['businessDeleteBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['operator'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $designation = null;
            $id = $_SESSION['id'];

            // Start transaction
            $pdo->beginTransaction();

            // Update the empty designation
            $stmt = $pdo->prepare("UPDATE operator_info SET designation = :designation WHERE user_id = :user_id");
            $stmt->execute(['designation' => $designation, 'user_id' => $id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update the designation.');
            }

            // Commit the transaction
            $pdo->commit();

            // Redirect on success
            header("Location: ../register-other-details.php");
            exit;
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}

// Function for uploads
function uploadFile($file, $prefix, $uploadDir)
{
    $randomString = bin2hex(random_bytes(5)); // Generate 10 random characters
    $fileName = $prefix . $randomString . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
    $filePath = $uploadDir . $fileName;

    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        throw new Exception('Failed to upload file: ' . $file['name']);
    }

    return $fileName;
}

// Upload ID
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['idUploadBtn'])) {
        try {
            session_start();

            if (!isset($_SESSION['csrf_token'], $_SESSION['operator'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            if (!isset($_FILES['uploadID']) || $_FILES['uploadID']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('File upload error.');
            }

            $id = $_SESSION['id'];
            $uploadDir = '../components/files/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $uploadedID = uploadFile($_FILES['uploadID'], 'id_calaguas_', $uploadDir);

            $pdo->beginTransaction();

            $stmt = $pdo->prepare("
                UPDATE operator_credentials SET picture_id = :pid WHERE user_id = :user_id
            ");
            $stmt->execute([
                'user_id' => $id,
                'pid' => $uploadedID,
            ]);

            $pdo->commit();
            header("Location: ../register-other-details.php");
            exit;
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            error_log($e->getMessage(), 3, 'errors.log');
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}

// Upload Permit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['permitUploadBtn'])) {
        try {
            session_start();

            if (!isset($_SESSION['csrf_token'], $_SESSION['operator'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            if (!isset($_FILES['uploadPermit']) || $_FILES['uploadPermit']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('File upload error.');
            }

            $id = $_SESSION['id'];

            $uploadDir = '../components/files/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $uploadedPermit = uploadFile($_FILES['uploadPermit'], 'permit_calaguas_', $uploadDir);

            $pdo->beginTransaction();

            $stmt = $pdo->prepare("
                UPDATE operator_credentials SET picture_permit = :ppermit WHERE user_id = :user_id
            ");
            $stmt->execute([
                'user_id' => $id,
                'ppermit' => $uploadedPermit,
            ]);

            $pdo->commit();
            header("Location: ../register-other-details.php");
            exit;
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            error_log($e->getMessage(), 3, 'errors.log');
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}

// Delete ID
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['idDeleteBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['operator'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $picture_id = null;
            $id = $_SESSION['id'];

            // Start transaction
            $pdo->beginTransaction();

            // Update the empty designation
            $stmt = $pdo->prepare("UPDATE operator_credentials SET picture_id = :pid WHERE user_id = :user_id");
            $stmt->execute(['pid' => $picture_id, 'user_id' => $id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update.');
            }

            // Commit the transaction
            $pdo->commit();

            // Redirect on success
            header("Location: ../register-other-details.php");
            exit;
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}

// Delete Permit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['permitDeleteBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['operator'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $picture_permit = null;
            $id = $_SESSION['id'];

            // Start transaction
            $pdo->beginTransaction();

            // Update the empty designation
            $stmt = $pdo->prepare("UPDATE operator_credentials SET picture_permit = :ppermit WHERE user_id = :user_id");
            $stmt->execute(['ppermit' => $picture_permit, 'user_id' => $id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update.');
            }

            // Commit the transaction
            $pdo->commit();

            // Redirect on success
            header("Location: ../register-other-details.php");
            exit;
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}

// Insert Room
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['addRoomBtn'])) {
        try {
            session_start();
            if (!isset($_SESSION['csrf_token'], $_SESSION['operator'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            // Input validation
            $type = strtoupper(trim($_POST['roomType']));
            $capacity = intval($_POST['roomCap']);
            $qty = intval($_POST['roomQty']);
            $cr = isset($_POST['comfort_room']) ? $_POST['comfort_room'] : '0';
            $id = $_SESSION['id'];

            if (empty($type) || strlen($type) > 50 || strlen($type) < 5) {
                throw new Exception('Invalid room type.');
            }
            if ($capacity < 0) {
                throw new Exception('Invalid capacity.');
            }
            if ($qty < 0) {
                throw new Exception('Invalid quantity.');
            }

            $pdo->beginTransaction();

            // Insert into database
            $stmt = $pdo->prepare("INSERT INTO resort_operator_roomlist (user_id, type, capacity, quantity, comRoom) VALUES (:user_id, :roomType, :capacity, :quantity, :cr)");
            if (!$stmt->execute([
                ':user_id' => $id,
                ':roomType' => $type,
                ':capacity' => $capacity,
                ':quantity' => $qty,
                ':cr' => $cr,
            ])) {
                throw new Exception('Database error: ' . implode(', ', $stmt->errorInfo()));
            }

            $pdo->commit();
            header("Location: ../register-other-details.php");
            exit;
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}

// Delete Room
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['deleteRoomBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['operator'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $id = $_POST['tableId'];

            // Start transaction
            $pdo->beginTransaction();

            // Update the empty designation
            $stmt = $pdo->prepare("DELETE FROM resort_operator_roomlist WHERE id = :id");
            $stmt->execute(['id' => $id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update.');
            }

            // Commit the transaction
            $pdo->commit();

            // Redirect on success
            header("Location: ../register-other-details.php");
            exit;
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}

// Submit Registration
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['submitBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['operator'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $id = $_SESSION['id'];
            $status = "pending";

            // Start transaction
            $pdo->beginTransaction();

            // Update the empty designation
            $stmt = $pdo->prepare("UPDATE operator_account SET accountStatus = :accStatus WHERE id = :id");
            $stmt->execute(['accStatus' => $status, 'id' => $id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update the designation.');
            }

            // Commit the transaction
            $pdo->commit();

            // Redirect on success
            header("Location: ../registration-complete.php");
            exit;
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../register-other-details.php");
            exit;
        }
    }
}
