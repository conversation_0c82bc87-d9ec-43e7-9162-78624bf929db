<?php
ob_start();
require '_header.php';
// Check if user is logged in
if (!isset($_SESSION['loginStatus']) || $_SESSION['loginStatus'] != "isLogin") {
    header("Location: ../logout.php");
    exit;
}

// Get booking ID from URL
$bookingId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (empty($bookingId)) {
    $_SESSION['error'] = "Invalid booking ID.";
    header("Location: transaction-declined.php");
    exit;
}

// Get booking details
try {
    $id = $_SESSION['id']; // Get the current user ID

    $sql = "SELECT
                b.booking_id,
                b.referenceNum,
                b.tour_operator_id,
                b.resort_operator_id,
                b.boat_id,
                bob.user_id AS boat_operator_id,
                b.port_id,
                b.check_in_date,
                b.check_out_date,
                b.booking_status,
                ba.resort AS resort_status,
                ba.boat AS boat_status,
                CASE
                    WHEN ba.resort = 'Declined' THEN 'Resort Operator'
                    WHEN ba.boat = 'Declined' THEN 'Boat Operator'
                    ELSE 'Unknown'
                END AS declined_by
            FROM cb_bookings b
            JOIN cb_booking_approvals ba ON b.booking_id = ba.booking_id
            JOIN boat_operator_boatlist bob ON b.boat_id = bob.id
            WHERE b.booking_id = :booking_id
            AND b.tour_operator_id = :tour_operator_id
            AND b.booking_status = 'declined'";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':booking_id' => $bookingId,
        ':tour_operator_id' => $id
    ]);

    $booking = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$booking) {
        $_SESSION['error'] = "Booking not found or not declined.";
        header("Location: transaction-declined.php");
        exit;
    }

    // Get available resorts
    $resortSql = "SELECT oi.user_id, oi.designation
                  FROM operator_info oi
                  JOIN operator_account oa ON oi.user_id = oa.id
                  WHERE oi.operatorType = 'Resort operator'
                  AND oa.accountStatus = 'Activated'
                  ORDER BY oi.designation ASC";
    $resortStmt = $pdo->prepare($resortSql);
    $resortStmt->execute();
    $resorts = $resortStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get available boats
    $boatSql = "SELECT b.id, b.boatName, b.capacity, o.designation
                FROM boat_operator_boatlist b
                JOIN operator_info o ON b.user_id = o.user_id
                WHERE b.boatStatus = 1
                ORDER BY o.designation ASC, b.boatName ASC";
    $boatStmt = $pdo->prepare($boatSql);
    $boatStmt->execute();
    $boats = $boatStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get available ports
    $portSql = "SELECT id, portName, fee FROM port_list ORDER BY portName ASC";
    $portStmt = $pdo->prepare($portSql);
    $portStmt->execute();
    $ports = $portStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get current selections
    $currentResortSql = "SELECT user_id, designation FROM operator_info WHERE user_id = :resort_id";
    $currentResortStmt = $pdo->prepare($currentResortSql);
    $currentResortStmt->execute([':resort_id' => $booking['resort_operator_id']]);
    $currentResort = $currentResortStmt->fetch(PDO::FETCH_ASSOC);

    $currentBoatSql = "SELECT b.id, b.boatName, o.designation
                      FROM boat_operator_boatlist b
                      JOIN operator_info o ON b.user_id = o.user_id
                      WHERE b.id = :boat_id";
    $currentBoatStmt = $pdo->prepare($currentBoatSql);
    $currentBoatStmt->execute([':boat_id' => $booking['boat_id']]);
    $currentBoat = $currentBoatStmt->fetch(PDO::FETCH_ASSOC);

    $currentPortSql = "SELECT id, portName, fee FROM port_list WHERE id = :port_id";
    $currentPortStmt = $pdo->prepare($currentPortSql);
    $currentPortStmt->execute([':port_id' => $booking['port_id']]);
    $currentPort = $currentPortStmt->fetch(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $_SESSION['error'] = "Database error: " . $e->getMessage();
    header("Location: transaction-declined.php");
    exit;
}

// Determine what can be edited based on who declined
$canEditResort = false;
$canEditBoat = false;
$canEditPort = false;

if ($booking['declined_by'] === 'Resort Operator') {
    $canEditPort = true; // Can edit destination (port) if declined by resort
    $canEditResort = true; // Can edit resort if declined by resort operator
} elseif ($booking['declined_by'] === 'Boat Operator') {
    $canEditBoat = true; // Can edit boat if declined by boat operator
}

// Debug information
error_log("Declined by: " . $booking['declined_by']);
error_log("Can edit port: " . ($canEditPort ? 'true' : 'false'));
error_log("Can edit boat: " . ($canEditBoat ? 'true' : 'false'));
error_log("Can edit resort: " . ($canEditResort ? 'true' : 'false'));

?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Main Content Section -->
        <div class="border p-4 rounded-lg shadow-lg bg-white mb-6">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                    <li class="inline-flex items-center">
                        <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                            <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <a href="transaction-declined.php" class="ms-1 text-sm font-medium text-gray-700 hover:text-gray-900 md:ms-2">Declined</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                            </svg>
                            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Edit Booking</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <!-- Header Section with Icon -->
            <div class="flex items-center mt-6">
                <div class="bg-blue-100 p-3 rounded-lg mr-4">
                    <i class="fas fa-edit text-blue-600 text-xl"></i>
                </div>
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">Edit Declined Booking</h2>
                    <p class="text-sm text-gray-600 mt-1">Make changes to address the reason for decline</p>
                </div>
            </div>

            <!-- Booking Reference and Decline Info -->
            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-md font-semibold text-gray-800 mb-2">Booking Reference</h3>
                    <p class="text-sm text-gray-600"><?= htmlspecialchars($booking['referenceNum']); ?></p>
                </div>
                <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                    <h3 class="text-md font-semibold text-red-800 mb-2">Declined By</h3>
                    <p class="text-sm text-red-600"><?= htmlspecialchars($booking['declined_by']); ?></p>
                </div>
            </div>

            <!-- Edit Form -->
            <form action="inc/inc.booking.php" method="POST" class="mt-6">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                <input type="hidden" name="booking_id" value="<?= $bookingId; ?>">
                <input type="hidden" name="referenceNumber" value="<?= htmlspecialchars($booking['referenceNum']); ?>">

                <!-- Operator Selection Section -->
                <div class="mb-6">
                    <h3 class="text-md font-semibold text-gray-800 mb-3 pb-2 border-b border-gray-200">
                        <i class="fas fa-building mr-2 text-blue-500"></i>Operators
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Resort Operator (Editable if declined by resort) -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border <?= $canEditResort ? 'border-blue-200' : 'border-gray-100'; ?>">
                            <label for="resort" class="block mb-2 text-sm font-medium text-gray-900">Resort Operator</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-hotel text-blue-500"></i>
                                </div>
                                <?php if ($canEditResort): ?>
                                <select id="resort" name="resort" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                                    <?php foreach ($resorts as $resort): ?>
                                        <option value="<?= htmlspecialchars($resort['user_id']); ?>" <?= ($resort['user_id'] == $booking['resort_operator_id']) ? 'selected' : ''; ?>>
                                            <?= htmlspecialchars($resort['designation']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php else: ?>
                                <input type="text" value="<?= htmlspecialchars($currentResort['designation'] ?? 'N/A'); ?>" class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full pl-10 p-2.5" readonly>
                                <input type="hidden" name="resort" value="<?= $booking['resort_operator_id']; ?>">
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Boat Operator (Editable if declined by boat) -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border <?= $canEditBoat ? 'border-blue-200' : 'border-gray-100'; ?>">
                            <label for="boat" class="block mb-2 text-sm font-medium text-gray-900">Select Boat</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-ship text-blue-500"></i>
                                </div>
                                <?php if ($canEditBoat): ?>
                                <select id="boat" name="boat" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                                    <?php foreach ($boats as $boat): ?>
                                        <option value="<?= htmlspecialchars($boat['id']); ?>" <?= ($boat['id'] == $booking['boat_id']) ? 'selected' : ''; ?>>
                                            <?= htmlspecialchars($boat['designation'] . ' - ' . $boat['boatName'] . ' (Capacity: ' . $boat['capacity'] . ')'); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php else: ?>
                                <input type="text" value="<?= htmlspecialchars(($currentBoat['designation'] ?? '') . ' - ' . ($currentBoat['boatName'] ?? 'N/A')); ?>" class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full pl-10 p-2.5" readonly>
                                <input type="hidden" name="boat" value="<?= $booking['boat_id']; ?>">
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Port/Destination (Editable if declined by resort) -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border <?= $canEditPort ? 'border-blue-200' : 'border-gray-100'; ?>">
                            <label for="port" class="block mb-2 text-sm font-medium text-gray-900">Select Port</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-anchor text-blue-500"></i>
                                </div>
                                <?php if ($canEditPort): ?>
                                <select id="port" name="port" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                                    <?php
                                    // Debug port options
                                    error_log("Number of ports available: " . count($ports));
                                    foreach ($ports as $port):
                                        error_log("Port option: " . $port['id'] . " - " . $port['portName']);
                                    ?>
                                        <option value="<?= htmlspecialchars($port['id']); ?>" <?= ($port['id'] == $booking['port_id']) ? 'selected' : ''; ?>>
                                            <?= htmlspecialchars($port['portName'] . ' (Fee: ' . $port['fee'] . ')'); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php else: ?>
                                <input type="text" value="<?= htmlspecialchars($currentPort['portName'] ?? 'N/A'); ?>" class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full pl-10 p-2.5" readonly>
                                <input type="hidden" name="port" value="<?= $booking['port_id']; ?>">
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trip Details Section (Read-only) -->
                <div class="mb-6">
                    <h3 class="text-md font-semibold text-gray-800 mb-3 pb-2 border-b border-gray-200">
                        <i class="fas fa-calendar-alt mr-2 text-blue-500"></i>Trip Details
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Check-in Date -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                            <label class="block mb-2 text-sm font-medium text-gray-900">Check-in Date</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-calendar-check text-blue-500"></i>
                                </div>
                                <input type="text" value="<?= date('F d, Y', strtotime($booking['check_in_date'])); ?>" class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full pl-10 p-2.5" readonly>
                                <input type="hidden" name="checkin" value="<?= $booking['check_in_date']; ?>">
                            </div>
                        </div>

                        <!-- Check-out Date -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                            <label class="block mb-2 text-sm font-medium text-gray-900">Check-out Date</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-calendar-minus text-blue-500"></i>
                                </div>
                                <input type="text" value="<?= date('F d, Y', strtotime($booking['check_out_date'])); ?>" class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full pl-10 p-2.5" readonly>
                                <input type="hidden" name="checkout" value="<?= $booking['check_out_date']; ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex flex-col md:flex-row justify-end mt-8 space-y-4 md:space-y-0 md:space-x-4">
                    <a href="transaction-declined.php" class="text-gray-700 bg-gray-200 hover:bg-gray-300 focus:ring-4 focus:outline-none focus:ring-gray-300 font-medium rounded-lg text-sm px-6 py-3 text-center flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Cancel
                    </a>
                    <a href="add-passenger-info.php?id=<?= $bookingId; ?>" class="text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-6 py-3 text-center flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                        <i class="fas fa-users mr-2"></i>
                        Edit Passengers
                    </a>
                    <button type="submit" name="updateDeclinedBookingBtn" class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-6 py-3 text-center flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                        <i class="fas fa-check-circle mr-2"></i>
                        Update Booking
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
require '_footer.php';
?>
