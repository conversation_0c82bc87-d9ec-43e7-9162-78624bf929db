<?php
ob_start();
require '_header.php';

$getBookingId = isset($_GET['id']) ? (int) $_GET['id'] : 0;

$bookingDetails = getBookingDetails2($pdo, $getBookingId);

if (!empty($bookingDetails)) {
    $getId = $_SESSION['id'] ?? null; // Define $id properly

    if ($bookingDetails['mtho'] === "Waiting" || $bookingDetails['mtho'] === "Pending" || $bookingDetails['booking_status'] === "completed" || $bookingDetails['booking_staus'] === "archive") {
        header("Location: transaction-pending.php");
        exit;
    } else {
        $extname = $bookingDetails['extname'] ?? '';
        $middlename = $bookingDetails['middlename'] ?? '';
        $operatorName = trim($bookingDetails['firstname'] . ' ' . $middlename . ' ' . $bookingDetails['lastname'] . ' ' . $extname);
        $designationTour = $bookingDetails['tour_operator_designation'];
        $tourOperatorId = $bookingDetails['tour_operator_id'];
        $resortName = $bookingDetails['resort_operator_designation'];
        $referenceNumber = $bookingDetails['referenceNum'];
        $bookingStatus = $bookingDetails['booking_status'];
        $boatName = $bookingDetails['boatName'];
        $portName = $bookingDetails['portName'];
        $checkIn = $bookingDetails['check_in_date'];
        $checkout = $bookingDetails['check_out_date'];
        $adultCount = $bookingDetails['total_adults'];
        $childrenCount = $bookingDetails['total_children'];
        $crewCount = $bookingDetails['total_crew'];
        $orNum = $bookingDetails['or_num'];
        $receipt = $bookingDetails['receipt_image'];
        $treasurerStatus = $bookingDetails['treasurer'];
        $voucherVinzons = $bookingDetails['voucher_vinzons'];
        $voucherOthers = $bookingDetails['voucher_others'];
        $voucherUse = $bookingDetails['voucher_use'];

        if ($treasurerStatus == "Pending") {
            $textColor = "text-yellow-600";
        } elseif ($treasurerStatus == "Approved") {
            $textColor = "text-green-600";
        } else {
            $textColor = "text-red-600";
        }

        if (preg_match('/Vinzons/i', $portName)) {
            $voucher = $voucherVinzons;
        } else {
            $voucher = $voucherOthers;
        }
    }
}

$counts = getTouristAndCrewCounts($pdo, $getBookingId);
$disContactPassenger = getDistinctTourist($pdo, "contact_number", $getBookingId);
$disAddressPassenger = getDistinctTourist($pdo, "address", $getBookingId);
$actual_adultCount = $counts['adults'];
$actual_childrenCount = $counts['children'];
$actual_crewCount = $counts['crewTts'] + $counts['crewMbca'];
ob_end_flush();
?>

<div class="p-4 sm:ml-64">
    <div class="border p-4 rounded-lg shadow-lg bg-white mt-14">
        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Transactions</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Approved</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-green-100 p-3 rounded-lg mr-4">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Approved Bookings</h2>
                <p class="text-sm text-gray-600 mt-1">Bookings that have been approved and are ready for departure</p>
            </div>
        </div>

        <!-- Reference Number Display -->
        <div class="mt-4 bg-gray-50 p-3 rounded-lg border border-gray-200 inline-flex items-center">
            <span class="text-sm font-medium text-gray-600">Reference Number:</span>
            <span class="text-lg font-semibold text-blue-700 ml-2"><?= $referenceNumber; ?></span>
        </div>

        <!-- Booking Information Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <!-- Tour Operator Section -->
            <div class="bg-white border border-blue-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-semibold text-blue-700">Tour Operator</h3>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <i class="fas fa-user-tie text-blue-600"></i>
                    </div>
                </div>
                <div class="mt-3 space-y-2">
                    <div>
                        <p class="text-xs text-gray-500">Operator Name</p>
                        <p class="text-sm font-medium text-gray-800"><?= $operatorName; ?></p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">Designation</p>
                        <p class="text-sm font-medium text-gray-800"><?= $designationTour; ?></p>
                    </div>
                </div>
            </div>

            <!-- Destination Section -->
            <div class="bg-white border border-green-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-semibold text-green-700">Destination Details</h3>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-map-marker-alt text-green-600"></i>
                    </div>
                </div>
                <div class="mt-3 space-y-2">
                    <div>
                        <p class="text-xs text-gray-500">Resort Destination</p>
                        <p class="text-sm font-medium text-gray-800"><?= $resortName; ?></p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">Boat to be Used</p>
                        <p class="text-sm font-medium text-gray-800"><?= $boatName; ?></p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">Place of Departure</p>
                        <p class="text-sm font-medium text-gray-800"><?= $portName; ?></p>
                    </div>
                </div>
            </div>

            <!-- Trip Details Section -->
            <div class="bg-white border border-purple-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-semibold text-purple-700">Trip Details</h3>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <i class="fas fa-calendar-alt text-purple-600"></i>
                    </div>
                </div>
                <div class="mt-3 space-y-2">
                    <div>
                        <p class="text-xs text-gray-500">Check-in Date</p>
                        <p class="text-sm font-medium text-gray-800"><?= (new DateTime($checkIn))->format('F d, Y'); ?></p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">Check-out Date</p>
                        <p class="text-sm font-medium text-gray-800"><?= (new DateTime($checkout))->format('F d, Y'); ?></p>
                    </div>
                    <div class="grid grid-cols-3 gap-2 mt-2">
                        <div class="bg-blue-50 p-1 rounded text-center">
                            <p class="text-xs text-blue-700">Adults</p>
                            <p class="text-sm font-bold text-blue-800"><?= $adultCount; ?></p>
                        </div>
                        <div class="bg-green-50 p-1 rounded text-center">
                            <p class="text-xs text-green-700">Children</p>
                            <p class="text-sm font-bold text-green-800"><?= $childrenCount; ?></p>
                        </div>
                        <div class="bg-yellow-50 p-1 rounded text-center">
                            <p class="text-xs text-yellow-700">Crew</p>
                            <p class="text-sm font-bold text-yellow-800"><?= $crewCount; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($orNum)): ?>
        <div class="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-2 rounded-lg mr-3">
                        <i class="fas fa-file-invoice-dollar text-blue-600"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">O.R. Number</p>
                        <p class="text-base font-medium text-gray-800 mt-1"><?= htmlspecialchars($orNum); ?></p>
                    </div>
                </div>

                <?php if (!empty($receipt)): ?>
                <div class="mt-3 sm:mt-0">
                    <a href="../../operator/receipt/<?= $receipt; ?>" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium" target="_blank" rel="noopener noreferrer">
                        <i class="fas fa-receipt mr-2"></i> View Receipt
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <hr class="my-6">

        <!-- Voucher Information Section -->
        <div class="mt-6">
            <div class="flex items-center mb-4">
                <div class="bg-red-100 p-2 rounded-lg mr-3">
                    <i class="fas fa-ticket-alt text-red-600 text-lg"></i>
                </div>
                <h3 class="text-md font-semibold text-gray-700">Voucher Information</h3>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Available Voucher Card -->
                <div class="bg-white border border-red-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-semibold text-red-700">Available Voucher</h3>
                        </div>
                        <div class="bg-red-100 p-2 rounded-full">
                            <i class="fas fa-ticket-alt text-red-600"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="text-xs text-gray-500">Port Location</p>
                        <p class="text-sm font-medium text-gray-800"><?= $portName; ?></p>
                        <div class="mt-2 bg-red-50 p-2 rounded-md">
                            <p class="text-lg font-bold text-red-700 text-center"><?= $voucher; ?></p>
                        </div>
                    </div>
                </div>

                <!-- Used Voucher Card -->
                <div class="bg-white border border-red-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-semibold text-red-700">Used Voucher</h3>
                        </div>
                        <div class="bg-red-100 p-2 rounded-full">
                            <i class="fas fa-ticket-alt text-red-600"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="text-xs text-gray-500">Port Location</p>
                        <p class="text-sm font-medium text-gray-800"><?= $portName; ?></p>
                        <div class="mt-2 bg-red-50 p-2 rounded-md">
                            <p class="text-lg font-bold text-red-700 text-center"><?= $voucherUse; ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Passenger Count Cards -->
        <div class="mt-6">
            <div class="flex items-center mb-4">
                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                    <i class="fas fa-users text-blue-600 text-lg"></i>
                </div>
                <h3 class="text-md font-semibold text-gray-700">Passenger Counts</h3>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Adults Section -->
                <div class="bg-white border border-blue-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-semibold text-blue-700">Adults</h3>
                            <p class="text-xs text-gray-500 mt-1">Age 9 and above</p>
                        </div>
                        <div class="bg-blue-100 p-2 rounded-full">
                            <i class="fas fa-user text-blue-600"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="text-2xl font-bold text-blue-700"><?= $actual_adultCount; ?> <span class="text-sm text-gray-500">/ <?= $adultCount; ?></span></p>
                    </div>
                </div>

                <!-- Children Section -->
                <div class="bg-white border border-green-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-semibold text-green-700">Children</h3>
                            <p class="text-xs text-gray-500 mt-1">Below age 9</p>
                        </div>
                        <div class="bg-green-100 p-2 rounded-full">
                            <i class="fas fa-child text-green-600"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="text-2xl font-bold text-green-700"><?= $actual_childrenCount; ?> <span class="text-sm text-gray-500">/ <?= $childrenCount; ?></span></p>
                    </div>
                </div>

                <!-- Crew Section -->
                <div class="bg-white border border-yellow-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-semibold text-yellow-700">Crew</h3>
                            <p class="text-xs text-gray-500 mt-1">Boat personnel</p>
                        </div>
                        <div class="bg-yellow-100 p-2 rounded-full">
                            <i class="fas fa-anchor text-yellow-600"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <p class="text-2xl font-bold text-yellow-700"><?= $actual_crewCount; ?> <span class="text-sm text-gray-500">/ 8</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Passenger List Section -->
        <div class="mt-6">
            <div class="flex items-center mb-4">
                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                    <i class="fas fa-list-ul text-blue-600 text-lg"></i>
                </div>
                <h3 class="text-md font-semibold text-gray-700">Passenger List</h3>
            </div>

            <!-- Add Passenger Button -->
            <button data-modal-target="addPassenger-modal" data-modal-toggle="addPassenger-modal" class="inline-flex items-center text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 mb-4" type="button">
                <i class="fas fa-user-plus mr-2"></i> Add Passenger
            </button>

            <!-- Add Passenger Modal -->
            <div id="addPassenger-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                <div class="relative p-4 w-full max-w-7xl max-h-full">
                    <!-- Modal content -->
                    <div class="relative bg-white rounded-lg shadow-lg">
                        <!-- Modal header -->
                        <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t">
                            <div class="flex items-center">
                                <div class="bg-blue-100 text-blue-700 p-2 rounded-full mr-3">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900">
                                    Add New Passenger
                                </h3>
                            </div>
                            <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="addPassenger-modal">
                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                </svg>
                                <span class="sr-only">Close modal</span>
                            </button>
                        </div>

                        <form action="inc/inc.passenger.php" method="POST">
                            <div class="p-4 md:p-5">
                                <!-- Info Message -->
                                <div class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 border-l-4 border-blue-500" role="alert">
                                    <div class="flex items-center">
                                        <i class="fas fa-info-circle text-blue-600 mr-2 text-lg"></i>
                                        <div>
                                            <span class="font-medium">Information!</span>
                                            <p class="mt-1">Please fill in all required fields to add a new passenger to this booking.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Passenger Information Section -->
                                    <div class="bg-gray-50 rounded-lg p-5">
                                        <div class="flex items-center mb-4">
                                            <i class="fas fa-id-card text-blue-600 mr-2"></i>
                                            <h4 class="text-lg font-semibold text-gray-900">Personal Information</h4>
                                        </div>

                                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                        <input type="hidden" value="<?= $getBookingId; ?>" name="bookingId">
                                        <input type="hidden" value="<?= $tourOperatorId; ?>" name="tourOperatorId">
                                        <input type="hidden" value="<?= $voucher; ?>" name="voucher">
                                        <input type="hidden" value="<?= $portName; ?>" name="portName">
                                        <input type="hidden" value="<?= $voucherUse; ?>" name="voucherUse">

                                        <!-- Type of Passenger -->
                                        <input type="hidden" value="tourist" name="passengerType">

                                        <!-- Full Name -->
                                        <div class="mb-4">
                                            <label for="fullName" class="block mb-2 text-sm font-medium text-gray-900">
                                                Full Name <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                    <i class="fas fa-user text-gray-400"></i>
                                                </div>
                                                <input type="text" id="fullName" name="fullName" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" placeholder="Enter full name" required>
                                            </div>
                                        </div>

                                        <!-- Citizenship -->
                                        <div class="mb-4">
                                            <label for="citizenship" class="block mb-2 text-sm font-medium text-gray-900">
                                                Citizenship <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                    <i class="fas fa-flag text-gray-400"></i>
                                                </div>
                                                <select id="citizenship" name="citizenship" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" required>
                                                    <option value="" disabled selected>Select citizenship</option>
                                                    <option value="filipino">Filipino</option>
                                                    <option value="foreigner">Foreigner</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Conditional Dropdown: Region if Filipino -->
                                        <div id="filipinoDropdown" class="hidden mb-4">
                                            <label for="region-country-filipino" class="block mb-2 text-sm font-medium text-gray-900">
                                                Region <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                    <i class="fas fa-map-marker-alt text-gray-400"></i>
                                                </div>
                                                <select id="region-country-filipino" name="region-country" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5">
                                                    <option value="" disabled selected>Select region</option>
                                                    <option value="Camarines Norte">Camarines Norte</option>
                                                    <option value="Region-V">Region-V</option>
                                                    <option value="Philippines">Philippines</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Conditional Dropdown: Country if Foreigner -->
                                        <div id="foreignerDropdown" class="hidden mb-4">
                                            <label for="region-country-foreigner" class="block mb-2 text-sm font-medium text-gray-900">
                                                Country <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                    <i class="fas fa-globe text-gray-400"></i>
                                                </div>
                                                <select id="region-country-foreigner" name="region-country" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5">
                                                    <option value="" disabled selected>Select country</option>
                                                    <option value="North America">North America</option>
                                                    <option value="South America">South America</option>
                                                    <option value="United Kingdom">United Kingdom</option>
                                                    <option value="Asia">Asia</option>
                                                    <option value="Africa">Africa</option>
                                                    <option value="Australia">Australia</option>
                                                    <option value="Europe">Europe</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Address -->
                                        <div class="mb-4">
                                            <label for="address" class="block mb-2 text-sm font-medium text-gray-900">
                                                Address <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                    <i class="fas fa-home text-gray-400"></i>
                                                </div>
                                                <input type="text" id="address" name="address" list="addressPassenger" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" placeholder="Enter address" required>
                                                <datalist id="addressPassenger">
                                                    <?php foreach ($disAddressPassenger as $showAddressPassenger): ?>
                                                        <option value="<?= $showAddressPassenger['address']; ?>"><?= $showAddressPassenger['address']; ?></option>
                                                    <?php endforeach; ?>
                                                </datalist>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Additional Details Section -->
                                    <div class="bg-gray-50 rounded-lg p-5">
                                        <div class="flex items-center mb-4">
                                            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                            <h4 class="text-lg font-semibold text-gray-900">Additional Details</h4>
                                        </div>

                                        <!-- Gender -->
                                        <div class="mb-4">
                                            <label for="gender" class="block mb-2 text-sm font-medium text-gray-900">
                                                Gender <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                    <i class="fas fa-venus-mars text-gray-400"></i>
                                                </div>
                                                <select id="gender" name="gender" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" required>
                                                    <option value="" disabled selected>Select gender</option>
                                                    <option value="Male">Male</option>
                                                    <option value="Female">Female</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Age -->
                                        <div class="mb-4">
                                            <label for="age" class="block mb-2 text-sm font-medium text-gray-900">
                                                Age <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                    <i class="fas fa-birthday-cake text-gray-400"></i>
                                                </div>
                                                <input type="number" id="age" name="age" min="1" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" placeholder="Enter age" required>
                                            </div>
                                        </div>

                                        <!-- Contact Number -->
                                        <div class="mb-4">
                                            <label for="contactNumber" class="block mb-2 text-sm font-medium text-gray-900">
                                                Contact Number <span class="text-red-600">*</span>
                                            </label>
                                            <div class="relative">
                                                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                                    <i class="fas fa-phone text-gray-400"></i>
                                                </div>
                                                <input type="text" id="contactNumber" name="contactNumber" list="contactNumberPassenger" maxlength="11" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" placeholder="Enter contact number" required>
                                                <datalist id="contactNumberPassenger">
                                                    <?php foreach ($disContactPassenger as $showContactPassenger): ?>
                                                        <option value="<?= $showContactPassenger['contact_number']; ?>"><?= $showContactPassenger['contact_number']; ?></option>
                                                    <?php endforeach; ?>
                                                </datalist>
                                            </div>
                                            <p class="mt-2 text-xs text-gray-500">
                                                <i class="fas fa-info-circle mr-1"></i> Enter a valid contact number
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Buttons -->
                            <div class="flex justify-end items-center p-4 md:p-5 border-t border-gray-200 rounded-b">
                                <button type="button" data-modal-hide="addPassenger-modal" class="py-2.5 px-5 mr-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100">
                                    Cancel
                                </button>
                                <button type="submit" name="registerPassenger" class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                                    Register Passenger
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>


        <!-- Table Passenger -->
        <div class="overflow-x-auto mt-4 bg-white border border-gray-200 rounded-lg shadow-sm">
            <table id="search-table" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Age</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php
                    try {
                        // SQL query to fetch booking details
                        $sql = "SELECT *
                                FROM cb_tourists
                                WHERE booking_id = :booking_id
                                ORDER BY
                                    CASE
                                        WHEN info_type = 'crewTts' THEN 1
                                        WHEN info_type = 'crewMbca' THEN 2
                                        ELSE 3
                                    END ASC,
                                    full_name ASC";
                        // Prepare and execute the statement
                        $stmt = $pdo->prepare($sql);
                        $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_STR);
                        $stmt->execute();

                        // Fetch the results
                        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        if ($rows) {
                            foreach ($rows as $row) {

                                if ($row['info_type'] == "tourist") {
                                    $infoTypeBadge = '
                                    <span class="bg-green-100 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm">Tourist</span>
                                    ';
                                    $deleteBtn = '
                                     <button data-modal-target="delete-passenger-modal-' . $row['tourist_id'] . '" data-modal-toggle="delete-passenger-modal-' . $row['tourist_id'] . '" type="button" class="inline-flex items-center justify-center bg-red-600 hover:bg-red-700 text-white font-medium rounded-md text-xs px-3 py-1.5">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                                            </svg>
                                        </button>
                                    ';
                                } elseif ($row['info_type'] == "crewTts") {
                                    $infoTypeBadge = '
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm">Crew TTS</span>
                                    ';
                                    $deleteBtn = '';
                                } else {
                                    $infoTypeBadge = '
                                    <span class="bg-red-100 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded-sm">Crew MBCA</span>
                                    ';
                                    $deleteBtn = '';
                                }
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 text-sm font-medium text-gray-900"><?= $infoTypeBadge; ?></td>
                                    <td class="px-6 py-4 text-sm text-gray-500 text-center"><?= htmlspecialchars($row['full_name']); ?></td>
                                    <td class="px-6 py-4 text-sm text-gray-500 text-center"><?= htmlspecialchars($row['age']); ?></td>
                                    <td class="px-6 py-4 text-sm text-center">
                                        <button data-modal-target="view-passenger-modal-<?= $row['tourist_id']; ?>" data-modal-toggle="view-passenger-modal-<?= $row['tourist_id']; ?>" type="button" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md text-xs px-3 py-1.5">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                            </svg>
                                        </button>
                                        <?= $deleteBtn; ?>
                                    </td>
                                </tr>
                                <!-- View Passenger Modal -->
                                <div id="view-passenger-modal-<?= $row['tourist_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-md max-h-full">
                                        <!-- Modal content -->
                                        <div class="relative bg-white rounded-lg shadow-lg">
                                            <!-- Modal header -->
                                            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t">
                                                <?php
                                                $iconClass = "fas fa-user";
                                                $bgColor = "bg-green-100";
                                                $textColor = "text-green-700";
                                                $title = "Tourist";

                                                if ($row['info_type'] == "crewTts") {
                                                    $iconClass = "fas fa-user-tie";
                                                    $bgColor = "bg-blue-100";
                                                    $textColor = "text-blue-700";
                                                    $title = "Crew TTS";
                                                } elseif ($row['info_type'] == "crewMbca") {
                                                    $iconClass = "fas fa-user-shield";
                                                    $bgColor = "bg-red-100";
                                                    $textColor = "text-red-700";
                                                    $title = "Crew MBCA";
                                                }
                                                ?>
                                                <div class="flex items-center">
                                                    <div class="<?= $bgColor ?> <?= $textColor ?> p-2 rounded-full mr-3">
                                                        <i class="<?= $iconClass ?>"></i>
                                                    </div>
                                                    <h3 class="text-lg font-semibold text-gray-900">
                                                        <?= $title ?> Information
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 inline-flex justify-center items-center" data-modal-hide="view-passenger-modal-<?= $row['tourist_id']; ?>">
                                                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <!-- Modal Body -->
                                            <div class="p-4 md:p-5">
                                                <!-- Passenger Type Badge -->
                                                <div class="mb-4 flex justify-center">
                                                    <?php if ($row['info_type'] == "tourist"): ?>
                                                        <span class="inline-flex items-center bg-green-100 text-green-800 text-xs font-medium px-4 py-1.5 rounded-full">
                                                            <i class="fas fa-user mr-2"></i> Tourist
                                                        </span>
                                                    <?php elseif ($row['info_type'] == "crewTts"): ?>
                                                        <span class="inline-flex items-center bg-blue-100 text-blue-800 text-xs font-medium px-4 py-1.5 rounded-full">
                                                            <i class="fas fa-user-tie mr-2"></i> Crew TTS
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="inline-flex items-center bg-red-100 text-red-800 text-xs font-medium px-4 py-1.5 rounded-full">
                                                            <i class="fas fa-user-shield mr-2"></i> Crew MBCA
                                                        </span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Name and Basic Info -->
                                                <div class="text-center mb-4">
                                                    <h4 class="text-xl font-semibold text-gray-900"><?= htmlspecialchars($row['full_name']); ?></h4>
                                                    <div class="flex justify-center items-center mt-1 space-x-2">
                                                        <span class="inline-flex items-center text-sm">
                                                            <?php if (strtolower($row['gender']) == 'male'): ?>
                                                                <i class="fas fa-mars text-blue-500 mr-1"></i> Male
                                                            <?php else: ?>
                                                                <i class="fas fa-venus text-pink-500 mr-1"></i> Female
                                                            <?php endif; ?>
                                                        </span>
                                                        <span class="text-gray-500">•</span>
                                                        <span class="text-sm text-gray-600"><?= htmlspecialchars($row['age']); ?> years old</span>
                                                    </div>
                                                </div>

                                                <!-- Information Cards -->
                                                <div class="grid grid-cols-1 gap-3">
                                                    <!-- Contact Information -->
                                                    <div class="bg-gray-50 rounded-lg p-3">
                                                        <div class="flex items-center mb-2">
                                                            <i class="fas fa-address-card text-blue-600 mr-2"></i>
                                                            <h5 class="text-sm font-semibold text-gray-700">Contact Information</h5>
                                                        </div>
                                                        <div class="space-y-2 pl-6">
                                                            <div class="flex items-start">
                                                                <i class="fas fa-phone text-gray-500 mr-2 mt-0.5"></i>
                                                                <div>
                                                                    <p class="text-xs text-gray-500">Phone Number</p>
                                                                    <?php if (!empty($row['contact_number'])): ?>
                                                                        <a href="tel:<?= htmlspecialchars($row['contact_number']); ?>" class="text-sm text-blue-600 hover:underline">
                                                                            <?= htmlspecialchars($row['contact_number']); ?>
                                                                        </a>
                                                                    <?php else: ?>
                                                                        <p class="text-sm text-gray-500 italic">Not provided</p>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                            <div class="flex items-start">
                                                                <i class="fas fa-map-marker-alt text-gray-500 mr-2 mt-0.5"></i>
                                                                <div>
                                                                    <p class="text-xs text-gray-500">Address</p>
                                                                    <?php if (!empty($row['address'])): ?>
                                                                        <p class="text-sm text-gray-700">
                                                                            <?= htmlspecialchars($row['demographic'] . ', ' . $row['address']); ?>
                                                                        </p>
                                                                    <?php else: ?>
                                                                        <p class="text-sm text-gray-500 italic">Not provided</p>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Footer -->
                                                <div class="flex justify-end pt-4 mt-4 border-t border-gray-200">
                                                    <button data-modal-hide="view-passenger-modal-<?= $row['tourist_id']; ?>" type="button" class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                                                        Close
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete Modal -->
                                <div id="delete-passenger-modal-<?= $row['tourist_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-md max-h-full">
                                        <div class="relative bg-white rounded-lg shadow-lg">
                                            <!-- Modal header -->
                                            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t">
                                                <div class="flex items-center">
                                                    <div class="bg-red-100 text-red-700 p-2 rounded-full mr-3">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </div>
                                                    <h3 class="text-lg font-semibold text-gray-900">
                                                        Delete Passenger
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 inline-flex justify-center items-center" data-modal-hide="delete-passenger-modal-<?= $row['tourist_id']; ?>">
                                                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <div class="p-4 md:p-5">
                                                <form action="inc/inc.passenger.php" method="POST">
                                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                    <input type="hidden" value="<?= $row['tourist_id']; ?>" name="passengerId">
                                                    <input type="hidden" value="<?= $getBookingId; ?>" name="bookingId">
                                                    <input type="hidden" value="<?= $voucher ?>" name="voucher">
                                                    <input type="hidden" value="<?= $tourOperatorId ?>" name="tourOperatorId">
                                                    <input type="hidden" value="<?= $row['age'] ?>" name="age">
                                                    <input type="hidden" value="<?= $voucherUse; ?>" name="voucherUse">
                                                    <input type="hidden" value="<?= $portName; ?>" name="portName">

                                                    <!-- Warning Message -->
                                                    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 border-l-4 border-red-500" role="alert">
                                                        <div class="flex items-center">
                                                            <i class="fas fa-exclamation-circle text-red-600 mr-2 text-lg"></i>
                                                            <div>
                                                                <span class="font-medium">Warning!</span>
                                                                <p class="mt-1">This action will permanently remove this passenger from the booking.</p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Passenger Info Card -->
                                                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                                        <div class="flex items-center mb-3">
                                                            <i class="fas fa-user text-blue-600 mr-2"></i>
                                                            <h4 class="text-sm font-semibold text-gray-700">Passenger Details</h4>
                                                        </div>

                                                        <div class="space-y-3 pl-6">
                                                            <div class="flex items-start">
                                                                <i class="fas fa-user-tag text-gray-500 mr-2 mt-0.5 w-4 text-center"></i>
                                                                <div>
                                                                    <p class="text-xs text-gray-500">Name</p>
                                                                    <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($row['full_name']); ?></p>
                                                                </div>
                                                            </div>

                                                            <div class="flex items-start">
                                                                <i class="fas fa-id-card text-gray-500 mr-2 mt-0.5 w-4 text-center"></i>
                                                                <div>
                                                                    <p class="text-xs text-gray-500">Type</p>
                                                                    <p class="text-sm font-medium text-gray-800"><?= ucfirst($row['info_type']); ?></p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                                                        <button data-modal-hide="delete-passenger-modal-<?= $row['tourist_id']; ?>" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100">
                                                            Cancel
                                                        </button>
                                                        <button type="submit" name="deletePassenger" class="text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                                                            Delete Passenger
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="3" class="px-6 py-4 text-center text-sm text-red-600">
                                Error: <?= htmlspecialchars($e->getMessage()); ?>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>

        <hr class="my-6">

        <!-- Action Buttons -->
        <div class="flex flex-col md:flex-row justify-end mt-4 space-y-3 md:space-y-0 md:space-x-4">
            <button data-modal-target="decline-modal" data-modal-toggle="decline-modal" class="inline-flex items-center justify-center bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none focus:ring-4 focus:ring-red-300" type="button">
                <i class="fas fa-archive mr-2"></i> Archive
            </button>
            <button data-modal-target="approve-modal" data-modal-toggle="approve-modal" class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none focus:ring-4 focus:ring-green-300" type="button">
                <i class="fas fa-ship mr-2"></i> Depart
            </button>
        </div>

        <!-- Archive Modal -->
        <div id="decline-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-md max-h-full">
                <div class="relative bg-white rounded-lg shadow-md border border-gray-200">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between p-4 border-b rounded-t border-gray-200 bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-archive text-red-600 mr-2"></i>
                            Archive Booking
                        </h3>
                        <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="decline-modal">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>

                    <!-- Modal Body -->
                    <div class="p-6">
                        <form action="inc/inc.booking.php" method="POST">
                            <!-- Warning Message -->
                            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 border-l-4 border-red-500" role="alert">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-circle text-red-600 mr-2 text-lg"></i>
                                    <div>
                                        <span class="font-medium">Warning!</span>
                                        <p class="mt-1">This action will archive the booking and remove it from active transactions.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Transaction Info Card -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                <div class="flex items-center mb-3">
                                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                    <h4 class="text-sm font-semibold text-gray-700">Transaction Details</h4>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <p class="text-xs text-gray-500">Reference Number</p>
                                        <p class="text-sm font-medium text-gray-800"><?= $referenceNumber; ?></p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">Tour Operator</p>
                                        <p class="text-sm font-medium text-gray-800"><?= $operatorName; ?></p>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                            <input type="hidden" value="<?= $getBookingId; ?>" name="booking_id">
                            <input type="hidden" value="archive" name="booking_status">
                            <input type="hidden" value="<?= $referenceNumber; ?>" name="referenceNumber">

                            <!-- Form Buttons -->
                            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                                <button data-modal-hide="decline-modal" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100">
                                    Cancel
                                </button>
                                <button type="submit" name="departBookingBtn" class="text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                                    Archive Booking
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Depart Modal -->
        <div id="approve-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-md max-h-full">
                <div class="relative bg-white rounded-lg shadow-md border border-gray-200">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between p-4 border-b rounded-t border-gray-200 bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-ship text-green-600 mr-2"></i>
                            Depart Booking
                        </h3>
                        <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="approve-modal">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>

                    <!-- Modal Body -->
                    <div class="p-6">
                        <form action="inc/inc.booking.php" method="POST">
                            <!-- Success Message -->
                            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 border-l-4 border-green-500" role="alert">
                                <div class="flex items-center">
                                    <i class="fas fa-info-circle text-green-600 mr-2 text-lg"></i>
                                    <div>
                                        <span class="font-medium">Ready to depart!</span>
                                        <p class="mt-1">This will mark the booking as completed and finalize the transaction.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Transaction Info Card -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                <div class="flex items-center mb-3">
                                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                    <h4 class="text-sm font-semibold text-gray-700">Transaction Details</h4>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <p class="text-xs text-gray-500">Reference Number</p>
                                        <p class="text-sm font-medium text-gray-800"><?= $referenceNumber; ?></p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">Tour Operator</p>
                                        <p class="text-sm font-medium text-gray-800"><?= $operatorName; ?></p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">Departure Date</p>
                                        <p class="text-sm font-medium text-gray-800"><?= (new DateTime($checkIn))->format('F d, Y'); ?></p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">Total Passengers</p>
                                        <p class="text-sm font-medium text-gray-800"><?= $actual_adultCount + $actual_childrenCount + $actual_crewCount; ?></p>
                                    </div>
                                </div>
                            </div>

                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                            <input type="hidden" value="<?= $getBookingId; ?>" name="booking_id">
                            <input type="hidden" value="completed" name="booking_status">
                            <input type="hidden" value="<?= $referenceNumber; ?>" name="referenceNumber">

                            <!-- Form Buttons -->
                            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                                <button data-modal-hide="approve-modal" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100">
                                    Cancel
                                </button>
                                <button type="submit" name="departBookingBtn" class="text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                                    Confirm Departure
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- JavaScript to Toggle Conditional Dropdowns -->
<script>
    document.getElementById('citizenship').addEventListener('change', function() {
        var selected = this.value;
        var filipinoDropdown = document.getElementById('filipinoDropdown');
        var foreignerDropdown = document.getElementById('foreignerDropdown');

        if (selected === 'filipino') {
            filipinoDropdown.classList.remove('hidden');
            foreignerDropdown.classList.add('hidden');
        } else if (selected === 'foreigner') {
            foreignerDropdown.classList.remove('hidden');
            filipinoDropdown.classList.add('hidden');
        } else {
            filipinoDropdown.classList.add('hidden');
            foreignerDropdown.classList.add('hidden');
        }
    });
</script>

<?php
require '_footer.php';
?>