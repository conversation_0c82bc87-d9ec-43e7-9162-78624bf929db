<?php
/**
 * Database Connection
 *
 * This file establishes a PDO connection to the database
 * using configuration from config.php
 */

// Load configuration
require_once __DIR__ . "/../config/config.php";

try {
    $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_TIMEOUT => 5,
        PDO::MYSQL_ATTR_FOUND_ROWS => true,
        PDO::ATTR_PERSISTENT => false,
    ];

    $pdo = new PDO($dsn, DB_USER, DB_PASSWORD, $options);

    // Clean up variables for security
    unset($dsn);

} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    if (APP_DEBUG) {
        die("Database connection failed: " . $e->getMessage());
    } else {
        die("Database connection failed. Please try again later.");
    }
}
?>