<?php
if (!defined('ALLOW_ACCESS')) {
    header('location: register-other-details.php');
    exit();
}
// Check if designation is empty for the current user
$stmt = $pdo->prepare("SELECT designation FROM operator_info WHERE user_id = :user_id");
$stmt->execute(['user_id' => $id]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if ($result && empty($result['designation'])) {
    $show_business = '
        <div class="flex w-full items-center">
            <div class="relative flex-grow">
                <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                    <svg class="w-4 h-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008z" />
                    </svg>
                </div>
                <input type="text" name="designation" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5" maxlength="50" minlength="5" placeholder="Enter your resort name" required>
            </div>
            <button type="submit" name="businessUpdateBtn" class="py-2 px-4 ml-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 transition flex items-center">
                <svg class="w-5 h-5 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                </svg>
                Save
            </button>
        </div>
    ';
} else {
    $show_business = '
        <div class="flex w-full items-center">
            <div class="flex items-center p-4 w-full bg-green-50 border border-green-200 rounded-lg">
                <svg class="w-6 h-6 text-green-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21" />
                </svg>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-green-800">
                        ' . $result['designation'] . '
                    </p>
                    <p class="text-xs text-green-600">
                        Resort name saved
                    </p>
                </div>
                <button type="submit" name="businessDeleteBtn" class="inline-flex items-center p-2 text-red-600 bg-red-100 rounded-lg hover:bg-red-200 focus:ring-2 focus:ring-red-300">
                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                    </svg>
                </button>
            </div>
        </div>
    ';
}

// for Uploads button states
$stmt2 = $pdo->prepare("SELECT * FROM operator_credentials WHERE user_id = :user_id");
$stmt2->execute(['user_id' => $id]);
$result2 = $stmt2->fetch(PDO::FETCH_ASSOC);

if ($result2 && empty($result2['picture_id'])) {
    $show_id = '
        <div class="flex w-full items-center">
            <div class="w-full">
                <label for="uploadID" class="flex flex-col items-center justify-center w-full h-32 border-2 border-blue-300 border-dashed rounded-lg cursor-pointer bg-blue-50 hover:bg-blue-100">
                    <div class="flex flex-col items-center justify-center pt-5 pb-6">
                        <svg class="w-8 h-8 mb-3 text-blue-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                        </svg>
                        <p class="mb-1 text-sm text-blue-500"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                        <p class="text-xs text-blue-500">SVG, PNG, JPG or PDF (MAX. 2MB)</p>
                    </div>
                    <input id="uploadID" name="uploadID" type="file" class="hidden" accept=".jpg,.jpeg,.png,.svg,.pdf" required />
                </label>
                <div id="id-preview-container" class="mt-3"></div>
            </div>
            <button type="submit" name="idUploadBtn" class="py-2 px-4 ml-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 transition">
                <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                </svg>
            </button>
        </div>
    ';
} else {
    $show_id = '
        <div class="flex w-full items-center">
            <div class="flex items-center p-4 w-full bg-green-50 border border-green-200 rounded-lg">
                <svg class="w-6 h-6 text-green-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-green-800 truncate">
                        ' . $result2['picture_id'] . '
                    </p>
                    <p class="text-xs text-green-600">
                        File uploaded successfully
                    </p>
                </div>
                <button type="submit" name="idDeleteBtn" class="inline-flex items-center p-2 text-red-600 bg-red-100 rounded-lg hover:bg-red-200 focus:ring-2 focus:ring-red-300">
                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                    </svg>
                </button>
            </div>
        </div>
    ';
}

if ($result2 && empty($result2['picture_permit'])) {
    $show_permit = '
        <div class="flex w-full items-center">
            <div class="w-full">
                <label for="uploadPermit" class="flex flex-col items-center justify-center w-full h-32 border-2 border-blue-300 border-dashed rounded-lg cursor-pointer bg-blue-50 hover:bg-blue-100">
                    <div class="flex flex-col items-center justify-center pt-5 pb-6">
                        <svg class="w-8 h-8 mb-3 text-blue-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                        </svg>
                        <p class="mb-1 text-sm text-blue-500"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                        <p class="text-xs text-blue-500">SVG, PNG, JPG or PDF (MAX. 2MB)</p>
                    </div>
                    <input id="uploadPermit" name="uploadPermit" type="file" class="hidden" accept=".jpg,.jpeg,.png,.svg,.pdf" required />
                </label>
                <div id="permit-preview-container" class="mt-3"></div>
            </div>
            <button type="submit" name="permitUploadBtn" class="py-2 px-4 ml-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 transition">
                <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                </svg>
            </button>
        </div>
    ';
} else {
    $show_permit = '
        <div class="flex w-full items-center">
            <div class="flex items-center p-4 w-full bg-green-50 border border-green-200 rounded-lg">
                <svg class="w-6 h-6 text-green-500 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-green-800 truncate">
                        ' . $result2['picture_permit'] . '
                    </p>
                    <p class="text-xs text-green-600">
                        File uploaded successfully
                    </p>
                </div>
                <button type="submit" name="permitDeleteBtn" class="inline-flex items-center p-2 text-red-600 bg-red-100 rounded-lg hover:bg-red-200 focus:ring-2 focus:ring-red-300">
                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                    </svg>
                </button>
            </div>
        </div>
    ';
}

// for Uploads button states
$stmt3 = $pdo->prepare("SELECT * FROM resort_operator_roomlist WHERE user_id = :user_id");
$stmt3->execute(['user_id' => $id]);
$result3 = $stmt3->fetch(PDO::FETCH_ASSOC);

if (
    ($result && empty($result['designation'])) ||
    ($result2 && (empty($result2['picture_id']) || empty($result2['picture_permit']))) ||
    !$result3 // Check if no rows exist in resort_operator_roomlist
) {
    $cursor = "cursor-not-allowed";
    $submitBtn = "disabled";
} else {
    $cursor = "";
    $submitBtn = "";
}

?>

<div class="grid grid-cols-1 md:grid-cols-2 gap-8">

    <!-- Instructions Section -->
    <fieldset class="border p-5 rounded-lg shadow-sm bg-white">
        <legend class="text-lg font-bold text-blue-600 px-2 flex items-center space-x-2">
            <!-- Notification Bell Icon -->
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V4a2 2 0 10-4 0v1.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0a3 3 0 11-6 0m6 0H9" />
            </svg>
            <span id="reminder-title">Important Reminder</span>
        </legend>

        <div class="flex items-center justify-start mb-4">
            <span class="mr-2 text-sm font-medium text-gray-700">English</span>
            <label class="relative inline-flex items-center cursor-pointer">
                <input type="checkbox" id="language-switch" class="sr-only peer">
                <div class="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:bg-blue-600 peer-focus:ring-4 peer-focus:ring-blue-300"></div>
                <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform peer-checked:translate-x-5"></div>
            </label>
            <span class="ml-2 text-sm font-medium text-gray-700">Tagalog</span>
        </div>

        <div class="mt-4 text-sm text-gray-700" id="reminder-content">
            <ol class="list-decimal ml-6 space-y-3">
                <li>This is the final step in completing your registration. Please provide the required information below to proceed.</li>
                <li>If you close this window, you can log in to your account later to continue from where you left off.</li>
                <li>For the <span class="font-medium text-blue-600">Resort Name</span> field, please enter the name of your registered resort business.</li>
                <li>For the <span class="font-medium text-blue-600">Upload ID</span> section, please upload a valid identification document to verify your identity.</li>
                <li>For the <span class="font-medium text-blue-600">Upload Permit</span>, kindly upload your Mayor's Permit or Operational Permit issued to your business.</li>
                <li>Please make sure the uploaded pictures are clear and not blurred or distorted.</li>
                <li>We will notify you via email once your account has been verified and activated.</li>
            </ol>
        </div>

        <!-- Progress Tracker -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            <h4 class="text-sm font-semibold text-gray-700 mb-2">Registration Progress</h4>
            <div class="space-y-3">
                <!-- Business Name Status -->
                <div class="flex items-center">
                    <?php if ($result && !empty($result['designation'])): ?>
                        <svg class="w-5 h-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm text-gray-700">Resort Name: <span class="text-green-600 font-medium">Completed</span></span>
                    <?php else: ?>
                        <svg class="w-5 h-5 text-yellow-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm text-gray-700">Resort Name: <span class="text-yellow-600 font-medium">Pending</span></span>
                    <?php endif; ?>
                </div>

                <!-- ID Upload Status -->
                <div class="flex items-center">
                    <?php if ($result2 && !empty($result2['picture_id'])): ?>
                        <svg class="w-5 h-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm text-gray-700">ID Upload: <span class="text-green-600 font-medium">Completed</span></span>
                    <?php else: ?>
                        <svg class="w-5 h-5 text-yellow-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm text-gray-700">ID Upload: <span class="text-yellow-600 font-medium">Pending</span></span>
                    <?php endif; ?>
                </div>

                <!-- Permit Upload Status -->
                <div class="flex items-center">
                    <?php if ($result2 && !empty($result2['picture_permit'])): ?>
                        <svg class="w-5 h-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm text-gray-700">Permit Upload: <span class="text-green-600 font-medium">Completed</span></span>
                    <?php else: ?>
                        <svg class="w-5 h-5 text-yellow-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm text-gray-700">Permit Upload: <span class="text-yellow-600 font-medium">Pending</span></span>
                    <?php endif; ?>
                </div>

                <!-- Room List Status -->
                <div class="flex items-center">
                    <?php if ($result3): ?>
                        <svg class="w-5 h-5 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm text-gray-700">Room List: <span class="text-green-600 font-medium">Added</span></span>
                    <?php else: ?>
                        <svg class="w-5 h-5 text-yellow-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm text-gray-700">Room List: <span class="text-yellow-600 font-medium">Required</span></span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </fieldset>

    <!-- Credentials Details Section -->
    <fieldset class="border p-5 rounded-lg shadow-sm bg-white">
        <legend class="text-lg font-bold text-blue-600 px-2">Resort Credentials</legend>

        <!-- Business Name Form -->
        <div class="mb-6">
            <form method="POST" action="inc/inc.tour-resort.php" class="mb-4">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'] ?? '', ENT_QUOTES, 'UTF-8'); ?>">
                <label class="block mb-2 text-sm font-medium text-gray-700">
                    Resort Name <span class="text-red-600">*</span>
                </label>
                <div class="flex items-center">
                    <?= $show_business; ?>
                </div>
                <p class="mt-1 text-xs text-gray-500">Enter the registered name of your resort business</p>
            </form>
        </div>

        <!-- ID Upload Form -->
        <div class="mb-6">
            <form method="POST" action="inc/inc.tour-resort.php" enctype="multipart/form-data" class="mb-4" id="id-upload-form">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                <label class="block mb-2 text-sm font-medium text-gray-700">
                    Upload ID <span class="text-red-600">*</span>
                </label>
                <div class="flex items-center">
                    <?= $show_id; ?>
                </div>
                <div id="id-preview-container" class="mt-3"></div>
                <p class="mt-1 text-xs text-gray-500">Upload a valid government-issued ID (JPG, PNG, PDF max 2MB)</p>
            </form>
        </div>

        <!-- Permit Upload Form -->
        <div class="mb-6">
            <form method="POST" action="inc/inc.tour-resort.php" enctype="multipart/form-data" class="mb-4" id="permit-upload-form">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                <label class="block mb-2 text-sm font-medium text-gray-700">
                    Upload Permit <span class="text-red-600">*</span>
                </label>
                <div class="flex items-center">
                    <?= $show_permit; ?>
                </div>
                <div id="permit-preview-container" class="mt-3"></div>
                <p class="mt-1 text-xs text-gray-500">Upload your business permit or operational permit (JPG, PNG, PDF max 2MB)</p>
            </form>
        </div>
    </fieldset>
</div>


<div class="border p-4 rounded-md shadow-sm mt-6">
    <h2 class="text-lg font-bold text-gray-900">Room Lists</h2>
    <!-- Modal toggle -->
    <button data-modal-target="default-modal" data-modal-toggle="default-modal" class="mb-5 block text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center" type="button">
        Add Room
    </button>

    <!-- Main modal -->
    <div id="default-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative p-4 w-full max-w-2xl max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t border-gray-200">
                    <h3 class="text-xl font-semibold text-gray-900">
                        Room Information
                    </h3>
                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="default-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <form action="inc/inc.tour-resort.php" method="POST">
                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                    <!-- Modal body -->
                    <div class="p-4 md:p-5 space-y-4">
                        <!-- Resort Name -->
                        <label class="block">
                            <span class="text-sm font-medium">Resort Name</span>
                            <div class="flex items-center mt-1">
                                <input type="text" name="roomType" class="w-full p-2 border rounded" maxlength="50" minlength="5" placeholder="Enter resort name..." required>
                            </div>
                        </label>

                        <!-- Capacity and Quantity on one line -->
                        <div class="flex gap-4">
                            <label class="block flex-1">
                                <span class="text-sm font-medium">Capacity</span>
                                <div class="flex items-center mt-1">
                                    <input type="number" name="roomCap" class="w-full p-2 border rounded" placeholder="Enter capacity..." min="0" required>
                                </div>
                            </label>
                            <label class="block flex-1">
                                <span class="text-sm font-medium">Quantity</span>
                                <div class="flex items-center mt-1">
                                    <input type="number" name="roomQty" class="w-full p-2 border rounded" placeholder="Enter quantity..." min="0" required>
                                </div>
                            </label>
                        </div>


                        <!-- Comfort Room with Checkbox -->
                        <label class="block">
                            <span class="text-sm font-medium">Comfort Room</span>
                            <div class="flex items-center mt-1">
                                <!-- Hidden input to handle the unchecked state -->
                                <input type="hidden" name="comfort_room" value="0">
                                <!-- Checkbox input -->
                                <input type="checkbox" name="comfort_room" value="1" class="w-4 h-4 border rounded text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm">Available</span>
                            </div>
                        </label>

                    </div>

                    <!-- Modal footer -->
                    <div class="flex justify-end items-center p-4 md:p-5 border-t border-gray-200 rounded-b">
                        <button data-modal-hide="default-modal" type="button" class="py-2.5 px-5 mr-3 text-sm font-medium text-gray-900 focus:outline-none bg-gray-200 rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100">Decline</button>
                        <button data-modal-hide="default-modal" name="addRoomBtn" type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">I accept</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
        <table class="w-full text-sm text-left text-gray-700">
            <thead class="text-xs text-white uppercase bg-gray-700">
                <tr>
                    <th scope="col" class="px-6 py-3">
                        Type
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Capacity
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Quantity
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Comfort Room
                    </th>
                    <th scope="col" class="px-6 py-3">
                        Action
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php
                try {
                    // SQL query to fetch data
                    $sql = "SELECT * FROM resort_operator_roomlist WHERE user_id = :id";
                    $stmt = $pdo->prepare($sql);
                    $stmt->bindParam(':id', $id, PDO::PARAM_INT);
                    $stmt->execute();

                    // Fetch the data
                    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    // Check if there are rows
                    if ($rows) {
                        foreach ($rows as $row) {

                            if ($row['comRoom'] == 1) {
                                $cr = "with C.R.";
                            } else {
                                $cr = "without C.R.";
                            }
                ?>
                            <tr class="odd:bg-white even:bg-gray-200">
                                <td class="px-6 py-3"><?= htmlspecialchars($row['type']); ?></td>
                                <td class="px-6 py-3"><?= htmlspecialchars($row['capacity']); ?></td>
                                <td class="px-6 py-3"><?= htmlspecialchars($row['quantity']); ?></td>
                                <td class="px-6 py-3"><?= htmlspecialchars($cr); ?></td>
                                <td class="px-6 py-3">
                                    <form action="inc/inc.tour-resort.php" method="POST">
                                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                        <input type="hidden" name="tableId" value="<?= $row['id']; ?>">
                                        <button type="submit" name="deleteRoomBtn" class="text-red-600 hover:underline">Delete</button>
                                    </form>
                                </td>
                            </tr>
                        <?php
                        }
                    } else {
                        ?>
                        <tr class="odd:bg-white even:bg-gray-50">
                            <td colspan="5" class="px-6 py-3 text-center">No data available</td>
                        </tr>
                    <?php
                    }
                } catch (PDOException $e) {
                    ?>
                    <tr class="odd:bg-white even:bg-gray-50">
                        <td colspan="5" class="px-6 py-3 text-center">
                            Error: <?= htmlspecialchars($e->getMessage()); ?>
                        </td>
                    </tr>
                <?php
                }
                ?>
            </tbody>


        </table>
    </div>

</div>

<div class="mt-8 pt-4 border-t border-gray-200 flex justify-center">
    <form action="inc/inc.tour-resort.php" method="POST" class="w-full max-w-lg flex justify-center">
        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
        <button type="submit" name="submitBtn"
            class="inline-flex items-center justify-center px-5 py-3 text-base font-medium rounded-lg transition focus:outline-none focus:ring-4 focus:ring-blue-300 w-full sm:w-auto
            <?= $submitBtn === 'disabled' ? 'bg-blue-300 text-white cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700'; ?>"
            <?= $submitBtn; ?>>
            <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Complete Registration
        </button>
    </form>
</div>

<script>
    // Toggle Reminder Visibility
    document.getElementById('toggle-reminder').addEventListener('click', function() {
        const content = document.getElementById('reminder-content');
        if (content.classList.contains('hidden')) {
            content.classList.remove('hidden');
            this.textContent = 'Hide Details';
        } else {
            content.classList.add('hidden');
            this.textContent = 'Show Details';
        }
    });
</script>