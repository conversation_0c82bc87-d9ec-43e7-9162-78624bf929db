<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);
include '../../../../connection/dbconnect.php';
include 'inc.function.php';
if ($_SESSION['loginStatus'] != "isLogin") {
    header("Location: ../logout.php");
    exit;
} else {

    if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['registerPassenger'])) {
        try {
            if (
                !isset($_POST['csrf_token'], $_SESSION['csrf_token']) ||
                $_POST['csrf_token'] !== $_SESSION['csrf_token']
            ) {
                throw new Exception("Invalid CSRF token.");
            }
            // Optionally unset or regenerate the CSRF token after usage
            unset($_SESSION['csrf_token']);

            if (!isset($_SESSION['id'])) {
                throw new Exception("You must be logged in to perform this action.");
            }

            $bookingId = filter_input(INPUT_POST, 'bookingId', FILTER_VALIDATE_INT);
            if (!$bookingId) {
                throw new Exception("Invalid booking ID.");
            }

            // passengerType: strip tags, convert special chars, trim
            $passengerType = htmlspecialchars(trim($_POST['passengerType']), ENT_QUOTES, 'UTF-8');
            if (empty($passengerType)) {
                throw new Exception("Passenger type is required.");
            }

            // fullName: uppercase, sanitize
            $fullName = isset($_POST['fullName'])
                ? strtoupper(htmlspecialchars(trim($_POST['fullName']), ENT_QUOTES, 'UTF-8'))
                : null;
            if (empty($fullName)) {
                throw new Exception("Full name is required.");
            }

            // region-country
            $regCount = htmlspecialchars(trim($_POST['region-country'] ?? ''), ENT_QUOTES, 'UTF-8');
            if (empty($regCount)) {
                throw new Exception("Region/Country is required.");
            }

            // address
            $address = isset($_POST['address'])
                ? strtoupper(htmlspecialchars(trim($_POST['address']), ENT_QUOTES, 'UTF-8'))
                : null;
            if (empty($address)) {
                throw new Exception("Address is required.");
            }

            // gender
            $gender = htmlspecialchars(trim($_POST['gender'] ?? ''), ENT_QUOTES, 'UTF-8');
            if (empty($gender)) {
                throw new Exception("Gender is required.");
            }

            // age: must be a valid integer; returns int or false/null
            $age = filter_var($_POST['age'] ?? '', FILTER_VALIDATE_INT);
            if ($age === false || $age < 0) {
                throw new Exception("Invalid age provided.");
            }

            // contactNumber
            $contactNumber = htmlspecialchars(trim($_POST['contactNumber'] ?? ''), ENT_QUOTES, 'UTF-8');
            if (empty($contactNumber)) {
                throw new Exception("Contact number is required.");
            }

            $bookingDetails = getBookingDetails($pdo, $bookingId);
            if (!$bookingDetails) {
                throw new Exception("Booking not found or invalid.");
            }

            $counts = getTouristAndCrewCounts($pdo, $bookingId);

            $adultCount       = (int)$bookingDetails['total_adults'];
            $childrenCount    = (int)$bookingDetails['total_children'];
            $crewCount        = 4; // Hard-coded to 4 in your example
            $actual_adultCount    = (int)$counts['adults'];
            $actual_childrenCount = (int)$counts['children'];
            $actual_crewCount     = (int)$counts['crewTts'];

            if ($passengerType === 'tourist' && $age >= 9 && $actual_adultCount >= $adultCount) {
                throw new Exception("The number of adults exceeds the allowed total.");
            } elseif ($passengerType === 'tourist' && $age <= 8 && $actual_childrenCount >= $childrenCount) {
                throw new Exception("The number of children exceeds the allowed total.");
            } elseif ($passengerType === 'crewTts' && $actual_crewCount >= $crewCount) {
                throw new Exception("The number of crew exceeds the permitted limit.");
            }

            $pdo->beginTransaction();

            // Check if the tourist already exists in cb_tourists
            // Use case-insensitive check by forcing lower(full_name) = lower(:full_name)
            $stmt = $pdo->prepare("
            SELECT 1
            FROM cb_tourists
            WHERE booking_id = :booking_id
              AND LOWER(full_name) = LOWER(:full_name)
            LIMIT 1
        ");
            $stmt->execute([
                ':booking_id' => $bookingId,
                ':full_name'  => $fullName
            ]);

            if ($stmt->fetch()) {
                throw new Exception("Tourist already exists.");
            }

            // Insert new tourist record
            $stmt = $pdo->prepare("
            INSERT INTO cb_tourists
                (booking_id, full_name, demographic, address, gender, age, contact_number, info_type)
            VALUES
                (:bookingId, :fullName, :regCount, :address, :gender, :age, :contactNumber, :passengerType)
        ");
            $stmt->execute([
                ':bookingId'     => $bookingId,
                ':fullName'      => $fullName,
                ':regCount'      => $regCount,
                ':address'       => $address,
                ':gender'        => $gender,
                ':age'           => $age,
                ':contactNumber' => $contactNumber,
                ':passengerType' => $passengerType
            ]);

            // Check if this is a voucher booking and handle voucher logic
            if ($bookingDetails['booking_status'] === 'voucher' && $passengerType === 'tourist') {
                // Get port information to determine voucher type
                $portName = $bookingDetails['portName'];
                $tourOperatorId = $bookingDetails['tour_operator_id'];
                
                // Determine voucher type based on port
                $voucherType = ($portName === 'VINZONS PORT') ? 'voucher_vinzons' : 'voucher_others';
                
                // Update voucher_use in cb_payments (+1)
                $stmt = $pdo->prepare("
                    UPDATE cb_payments 
                    SET voucher_use = voucher_use + 1 
                    WHERE booking_id = :booking_id
                ");
                $stmt->execute([':booking_id' => $bookingId]);
                
                // Update voucher count in cb_vouchers (-1)
                $stmt = $pdo->prepare("
                    UPDATE cb_vouchers 
                    SET $voucherType = $voucherType - 1 
                    WHERE operator_id = :operator_id AND $voucherType > 0
                ");
                $stmt->execute([':operator_id' => $tourOperatorId]);
                
                // Check if the voucher update was successful
                if ($stmt->rowCount() === 0) {
                    throw new Exception("No vouchers available for this port or operator.");
                }
            }

            // Commit transaction
            $pdo->commit();

            // --------------------------------------------------------------------
            // 8. Success message and redirect
            // --------------------------------------------------------------------
            $_SESSION['success'] = "Passenger has been added successfully.";
            header("Location: ../add-passenger-info.php?id=" . urlencode($bookingId));
            exit();
        } catch (Exception $e) {
            // Rollback if in a transaction
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }

            // Optional: Log errors for debugging (do not show details to user)
            // error_log("Error adding passenger: " . $e->getMessage());

            $_SESSION['error'] = $e->getMessage();
            header("Location: ../add-passenger-info.php?id=" . urlencode($bookingId));
            exit();
        }
    }

    if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['deletePassenger'])) {
        try {
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                throw new Exception("Invalid CSRF token.");
            }

            $passengerId = $_POST['passengerId'];
            $bookingId = $_POST['bookingId'];

            $pdo->beginTransaction();

            $stmt = $pdo->prepare("DELETE FROM cb_tourists where tourist_id = :tourist_id");
            $stmt->execute([
                ':tourist_id' => $passengerId
            ]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to delete.');
            }

            $pdo->commit();

            $_SESSION['success'] = "Passenger info has been Deleted";
            header("Location: ../add-passenger-info.php?id=" . $bookingId); // Redirect back to the form
            exit();
        } catch (Exception $e) {
            $pdo->rollBack();
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../add-passenger-info.php?id=" . $bookingId); // Redirect back to the form
            exit();
        }
    }
}
