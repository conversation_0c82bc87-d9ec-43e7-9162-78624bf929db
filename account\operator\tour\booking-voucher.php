<?php
require '_header.php';

// Fetch all bookings for this tour operator
try {
    $sql = "SELECT
                bb.booking_id,
                bb.referenceNum,
                bb.booking_status,
                bb.check_in_date,
                bb.check_out_date,
                oi.designation AS resort_name,
                bob.boatName,
                pl.portName,
                COALESCE(cp.total_adults, 0) as total_adults,
                COALESCE(cp.total_children, 0) as total_children
            FROM cb_bookings bb
            LEFT JOIN operator_info oi ON bb.resort_operator_id = oi.user_id
            LEFT JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
            LEFT JOIN port_list pl ON bb.port_id = pl.id
            LEFT JOIN cb_payments cp ON bb.booking_id = cp.booking_id
            WHERE bb.tour_operator_id = :tour_operator_id
            ORDER BY bb.date_created DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':tour_operator_id', $id, PDO::PARAM_INT);
    $stmt->execute();
    $bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $bookings = [];
    $_SESSION['error'] = "Error fetching bookings: " . $e->getMessage();
}

// Fetch voucher information for this tour operator
try {
    $voucherSql = "SELECT voucher_vinzons, voucher_others FROM cb_vouchers WHERE operator_id = :operator_id";
    $voucherStmt = $pdo->prepare($voucherSql);
    $voucherStmt->bindParam(':operator_id', $id, PDO::PARAM_INT);
    $voucherStmt->execute();
    $voucherInfo = $voucherStmt->fetch(PDO::FETCH_ASSOC);

    // If no voucher record exists, create default values
    if (!$voucherInfo) {
        $vinzonsVouchers = 0;
        $otherVouchers = 0;
    } else {
        $vinzonsVouchers = (int)$voucherInfo['voucher_vinzons'];
        $otherVouchers = (int)$voucherInfo['voucher_others'];
    }
} catch (PDOException $e) {
    $vinzonsVouchers = 0;
    $otherVouchers = 0;
    $_SESSION['error'] = "Error fetching voucher information: " . $e->getMessage();
}

// Get booking statistics
$draftCount = 0;
$pendingCount = 0;
$approvedCount = 0;
$completedCount = 0;
$declinedCount = 0;

foreach ($bookings as $booking) {
    switch ($booking['booking_status']) {
        case 'draft':
            $draftCount++;
            break;
        case 'pending':
            $pendingCount++;
            break;
        case 'approved':
            $approvedCount++;
            break;
        case 'completed':
            $completedCount++;
            break;
        case 'declined':
            $declinedCount++;
            break;
    }
}

// Fetch data for Select Resort
try {
    $sqlResort = "SELECT oi.designation, oi.user_id
                  FROM operator_info oi
                  INNER JOIN operator_account oa ON oi.user_id = oa.id
                  WHERE oa.accountStatus = 'Activated' AND oi.operatorType = 'Resort operator'";
    $stmtResort = $pdo->query($sqlResort);
    $designations = $stmtResort->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Error fetching resort data: " . $e->getMessage());
}

// Fetch data for Select Boat
try {
    $sqlBoat = "SELECT *
                FROM boat_operator_boatlist
                WHERE boatStatus = 1";
    $stmtBoat = $pdo->query($sqlBoat);
    $boats = $stmtBoat->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Error fetching boat data: " . $e->getMessage());
}

// Fetch data for Select Port
try {
    $sqlPort = "SELECT *
                FROM port_list";
    $stmtPort = $pdo->query($sqlPort);
    $ports = $stmtPort->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Error fetching port data: " . $e->getMessage());
}
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Main Content Section -->
        <div class="border p-4 rounded-lg shadow-lg bg-white mb-6">
        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">Management</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">Booking</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-pink-100 p-2 rounded-lg mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            </div>
            <div>
                <h2 class="text-xl font-bold text-gray-900">Voucher Booking</h2>
                <p class="text-sm text-gray-600">Manage all your tour bookings in one place.</p>
            </div>
        </div>

        <!-- Form Section -->
        <div class="mt-6 bg-gray-50 p-5 rounded-lg border border-gray-200">
            <!-- Voucher Information -->
            <div class="mb-6 bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h3 class="text-md font-semibold text-blue-800 mb-2">
                    <i class="fas fa-ticket-alt mr-2"></i>Your Available Vouchers
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-white p-3 rounded-lg shadow-sm border border-blue-100 flex items-center">
                        <div class="bg-blue-100 p-2 rounded-full mr-3">
                            <i class="fas fa-ticket-alt text-blue-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-700">Vinzons Port Vouchers</p>
                            <p class="text-lg font-bold text-blue-600"><?= $vinzonsVouchers ?> available</p>
                        </div>
                    </div>
                    <div class="bg-white p-3 rounded-lg shadow-sm border border-blue-100 flex items-center">
                        <div class="bg-blue-100 p-2 rounded-full mr-3">
                            <i class="fas fa-ticket-alt text-blue-600"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-700">Other Ports Vouchers</p>
                            <p class="text-lg font-bold text-blue-600"><?= $otherVouchers ?> available</p>
                        </div>
                    </div>
                </div>
            </div>

            <form action="inc/inc.booking.php" method="POST">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                <input type="hidden" name="voucher_booking" value="1">

                <!-- Voucher Selection Section -->
                <div class="mb-6">
                    <h3 class="text-md font-semibold text-gray-800 mb-3 pb-2 border-b border-gray-200">
                        <i class="fas fa-ticket-alt mr-2 text-blue-500"></i>Select Voucher Type
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Vinzons Voucher Option -->
                        <div class="relative">
                            <input type="radio" id="vinzons_voucher" name="voucher_type" value="vinzons" class="hidden peer" <?= $vinzonsVouchers <= 0 ? 'disabled' : '' ?> required>
                            <label for="vinzons_voucher" class="inline-flex items-center justify-between w-full p-5 text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-50 <?= $vinzonsVouchers <= 0 ? 'opacity-50 cursor-not-allowed' : '' ?>">
                                <div class="block">
                                    <div class="w-full text-lg font-semibold">Vinzons Port Voucher</div>
                                    <div class="w-full text-sm">Use for bookings with Vinzons port</div>
                                    <div class="mt-2 text-xs text-blue-600 font-bold"><?= $vinzonsVouchers ?> vouchers available</div>
                                </div>
                                <i class="fas fa-ticket-alt ml-3 text-2xl"></i>
                            </label>
                        </div>

                        <!-- Other Voucher Option -->
                        <div class="relative">
                            <input type="radio" id="other_voucher" name="voucher_type" value="others" class="hidden peer" <?= $otherVouchers <= 0 ? 'disabled' : '' ?> required>
                            <label for="other_voucher" class="inline-flex items-center justify-between w-full p-5 text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-50 <?= $otherVouchers <= 0 ? 'opacity-50 cursor-not-allowed' : '' ?>">
                                <div class="block">
                                    <div class="w-full text-lg font-semibold">Other Ports Voucher</div>
                                    <div class="w-full text-sm">Use for bookings with any other port</div>
                                    <div class="mt-2 text-xs text-blue-600 font-bold"><?= $otherVouchers ?> vouchers available</div>
                                </div>
                                <i class="fas fa-ticket-alt ml-3 text-2xl"></i>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Operator Selection Section -->
                <div class="mb-6">
                    <h3 class="text-md font-semibold text-gray-800 mb-3 pb-2 border-b border-gray-200">
                        <i class="fas fa-building mr-2 text-blue-500"></i>Select Operators
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Select Resort Dropdown -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                            <label for="resort" class="block mb-2 text-sm font-medium text-gray-900">Select Resort</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-hotel text-blue-500"></i>
                                </div>
                                <select id="resort" name="resort" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                                    <option selected disabled value="">Choose a resort</option>
                                    <?php if (!empty($designations)): ?>
                                        <?php foreach ($designations as $designation): ?>
                                            <option value="<?= htmlspecialchars($designation['user_id']) ?>">
                                                <?= htmlspecialchars($designation['designation']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <option value="" disabled>No resorts available</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <!-- Select Boat Dropdown -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                            <label for="boat" class="block mb-2 text-sm font-medium text-gray-900">Select Boat</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-ship text-blue-500"></i>
                                </div>
                                <select id="boat" name="boat" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                                    <option selected disabled value="">Choose a boat</option>
                                    <?php if (!empty($boats)): ?>
                                        <?php foreach ($boats as $boat): ?>
                                            <option value="<?= htmlspecialchars($boat['id']) ?>">
                                                <?= htmlspecialchars($boat['boatName']) ?> (Capacity: <?= htmlspecialchars($boat['capacity']) ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <option value="" disabled>No boats available</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>

                        <!-- Port Selection -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                            <label class="block mb-2 text-sm font-medium text-gray-900">Port</label>

                            <!-- No voucher selected message -->
                            <div id="no-voucher-message" class="p-2.5 bg-gray-50 border border-gray-200 rounded-lg text-gray-500">
                                <i class="fas fa-info-circle mr-1"></i> Select a voucher type to determine available ports
                            </div>

                            <!-- Vinzons Port (Read-only) -->
                            <div id="vinzons-port-info" class="hidden">
                                <?php
                                // Find the first Vinzons port
                                $vinzonsPort = null;
                                foreach ($ports as $port) {
                                    if (stripos($port['portName'], 'vinzons') !== false) {
                                        $vinzonsPort = $port;
                                        break;
                                    }
                                }
                                if ($vinzonsPort): ?>
                                    <div class="p-2.5 bg-blue-50 border border-blue-200 rounded-lg">
                                        <i class="fas fa-anchor text-blue-500 mr-2"></i>
                                        <span class="font-medium">Vinzons Port</span>
                                        <span class="text-sm text-gray-500 ml-2">(Fee: <?= htmlspecialchars($vinzonsPort['fee']) ?>)</span>
                                        <input type="hidden" id="vinzons-port-id" value="<?= htmlspecialchars($vinzonsPort['id']) ?>">
                                    </div>
                                    <div class="mt-2 text-xs text-blue-600">
                                        <i class="fas fa-info-circle mr-1"></i> Vinzons vouchers can only be used with Vinzons port
                                    </div>
                                <?php else: ?>
                                    <div class="p-2.5 bg-red-50 border border-red-200 rounded-lg text-red-500">
                                        <i class="fas fa-exclamation-circle mr-1"></i> No Vinzons port found in the system
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Other Ports Dropdown -->
                            <div id="other-ports-dropdown" class="hidden">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                        <i class="fas fa-anchor text-blue-500"></i>
                                    </div>
                                    <select id="other-port-select" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5">
                                        <option selected disabled value="">Choose a port</option>
                                        <?php
                                        $hasOtherPorts = false;
                                        foreach ($ports as $port):
                                            if (stripos($port['portName'], 'vinzons') === false):
                                                $hasOtherPorts = true;
                                        ?>
                                            <option value="<?= htmlspecialchars($port['id']) ?>">
                                                <?= htmlspecialchars($port['portName']) ?> (Fee: <?= htmlspecialchars($port['fee']) ?>)
                                            </option>
                                        <?php
                                            endif;
                                        endforeach;
                                        ?>
                                    </select>
                                </div>
                                <?php if (!$hasOtherPorts): ?>
                                    <div class="mt-2 text-xs text-red-500">
                                        <i class="fas fa-exclamation-circle mr-1"></i> No other ports found in the system
                                    </div>
                                <?php else: ?>
                                    <div class="mt-2 text-xs text-blue-600">
                                        <i class="fas fa-info-circle mr-1"></i> Other vouchers can be used with any non-Vinzons port
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Hidden input to store the selected port ID -->
                            <input type="hidden" id="port" name="port" value="">
                        </div>
                    </div>
                </div>

                <!-- Trip Details Section -->
                <div class="mb-6">
                    <h3 class="text-md font-semibold text-gray-800 mb-3 pb-2 border-b border-gray-200">
                        <i class="fas fa-calendar-alt mr-2 text-blue-500"></i>Trip Details
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Check-in Date Picker -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                            <label for="checkin" class="block mb-2 text-sm font-medium text-gray-900">Check-in</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-calendar-check text-blue-500"></i>
                                </div>
                                <input type="date" id="checkin" name="checkin" min="<?= date("Y-m-d"); ?>" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                            </div>
                        </div>

                        <!-- Check-out Date Picker -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                            <label for="checkout" class="block mb-2 text-sm font-medium text-gray-900">Check-out</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-calendar-minus text-blue-500"></i>
                                </div>
                                <input type="date" id="checkout" name="checkout" min="<?= date("Y-m-d"); ?>" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" required>
                            </div>
                        </div>

                        <!-- Adult Passengers -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                            <label for="adult" class="block mb-2 text-sm font-medium text-gray-900">No. of Adult Passengers</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-user text-blue-500"></i>
                                </div>
                                <input type="number" id="adult" name="adult" min="1" max="1" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Age 9 above" required disabled>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Age 9 and above</p>
                            <p class="mt-1 text-xs text-blue-600" id="voucher-limit-text" style="display: none;">Maximum based on available vouchers</p>
                        </div>

                        <!-- Children Passengers -->
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                            <label for="children" class="block mb-2 text-sm font-medium text-gray-900">No. of Children Passengers</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-child text-blue-500"></i>
                                </div>
                                <input type="number" id="children" name="children" min="0" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="Age 8 below" required disabled>
                            </div>
                            <p class="mt-1 text-xs text-gray-500">Age 8 and below</p>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex flex-col md:flex-row justify-end mt-8 space-y-4 md:space-y-0 md:space-x-4">
                    <button type="submit" name="bookingBtn" class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-6 py-3 text-center flex items-center justify-center transition-all duration-300 shadow-md hover:shadow-lg">
                        <i class="fas fa-check-circle mr-2"></i>
                        Submit Booking
                    </button>
                </div>
            </form>
        </div>
        </div>
    </div>


<script>
    // Check date validation on form submission
    document.querySelector('form').addEventListener('submit', function(event) {
        const checkin = new Date(document.getElementById('checkin').value);
        const checkout = new Date(document.getElementById('checkout').value);

        if (checkin >= checkout) {
            event.preventDefault(); // Stop form submission

            Swal.fire({
                icon: 'error',
                title: 'Invalid Date Selection',
                text: 'Check-in date must be earlier than check-out date.',
                confirmButtonText: 'OK',
                confirmButtonColor: '#3085d6', // Set a visible button color
            });
            return;
        }

        // Check if at least one voucher type is selected
        const vinzonsVoucher = document.getElementById('vinzons_voucher');
        const otherVoucher = document.getElementById('other_voucher');

        if (!vinzonsVoucher.checked && !otherVoucher.checked) {
            event.preventDefault(); // Stop form submission

            Swal.fire({
                icon: 'error',
                title: 'Voucher Selection Required',
                text: 'Please select a voucher type to continue.',
                confirmButtonText: 'OK',
                confirmButtonColor: '#3085d6',
            });
            return;
        }

        // Check if a port has been selected
        const selectedPort = document.getElementById('port').value;
        if (!selectedPort) {
            event.preventDefault(); // Stop form submission

            Swal.fire({
                icon: 'error',
                title: 'Port Selection Required',
                text: 'Please select a port to continue.',
                confirmButtonText: 'OK',
                confirmButtonColor: '#3085d6',
            });
            return;
        }

        // Validate that the selected voucher type matches the available port
        if (vinzonsVoucher.checked) {
            const vinzonsPortId = document.getElementById('vinzons-port-id');
            if (!vinzonsPortId || !vinzonsPortId.value) {
                event.preventDefault(); // Stop form submission

                Swal.fire({
                    icon: 'error',
                    title: 'Port Not Available',
                    text: 'No Vinzons port is available in the system. Please contact an administrator.',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#3085d6',
                });
                return;
            }
        }

        if (otherVoucher.checked) {
            const otherPortSelect = document.getElementById('other-port-select');
            if (!otherPortSelect.value) {
                event.preventDefault(); // Stop form submission

                Swal.fire({
                    icon: 'error',
                    title: 'Port Selection Required',
                    text: 'Please select a port from the dropdown.',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#3085d6',
                });
                return;
            }
        }
    });

    // Handle port selection based on voucher type
    document.getElementById('vinzons_voucher').addEventListener('change', function(event) {
        if (event.target.checked) {
            selectPort('vinzons');
        } else {
            resetPortSelection();
        }
    });

    document.getElementById('other_voucher').addEventListener('change', function(event) {
        if (event.target.checked) {
            selectPort('others');
        } else {
            resetPortSelection();
        }
    });

    // Check if a voucher is already selected when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('vinzons_voucher').checked) {
            selectPort('vinzons');
        } else if (document.getElementById('other_voucher').checked) {
            selectPort('others');
        } else {
            resetPortSelection();
        }
    });

    // Reset port selection when no voucher is selected
    function resetPortSelection() {
        // Hide port info sections
        document.getElementById('vinzons-port-info').classList.add('hidden');
        document.getElementById('other-ports-dropdown').classList.add('hidden');

        // Show no voucher message
        document.getElementById('no-voucher-message').classList.remove('hidden');

        // Clear the port input value
        document.getElementById('port').value = '';
        
        // Disable passenger input fields
        disablePassengerInputs();
    }

    // Select port based on voucher type
    function selectPort(voucherType) {
        // Hide all port info sections first
        document.getElementById('no-voucher-message').classList.add('hidden');
        document.getElementById('vinzons-port-info').classList.add('hidden');
        document.getElementById('other-ports-dropdown').classList.add('hidden');

        if (voucherType === 'vinzons') {
            // Show Vinzons port info
            document.getElementById('vinzons-port-info').classList.remove('hidden');

            // Set the port input value to the Vinzons port ID
            const vinzonsPortId = document.getElementById('vinzons-port-id');
            if (vinzonsPortId) {
                document.getElementById('port').value = vinzonsPortId.value;
            }
            
            // Enable passenger inputs and set voucher limit
            enablePassengerInputs(<?= $vinzonsVouchers ?>);
        } else if (voucherType === 'others') {
            // Show Other ports dropdown
            document.getElementById('other-ports-dropdown').classList.remove('hidden');

            // Set up event listener for the other port select dropdown
            const otherPortSelect = document.getElementById('other-port-select');

            // Update the hidden port input when the dropdown changes
            otherPortSelect.addEventListener('change', function() {
                document.getElementById('port').value = this.value;
            });

            // If there's a value already selected, use it
            if (otherPortSelect.value) {
                document.getElementById('port').value = otherPortSelect.value;
            } else {
                // Try to select the first non-disabled option
                for (let i = 0; i < otherPortSelect.options.length; i++) {
                    if (!otherPortSelect.options[i].disabled) {
                        otherPortSelect.selectedIndex = i;
                        document.getElementById('port').value = otherPortSelect.value;
                        break;
                    }
                }
            }
            
            // Enable passenger inputs and set voucher limit
            enablePassengerInputs(<?= $otherVouchers ?>);
        }
    }
    
    // Function to disable passenger input fields
    function disablePassengerInputs() {
        const adultInput = document.getElementById('adult');
        const childrenInput = document.getElementById('children');
        const voucherLimitText = document.getElementById('voucher-limit-text');
        
        adultInput.disabled = true;
        childrenInput.disabled = true;
        adultInput.value = '';
        childrenInput.value = '';
        voucherLimitText.style.display = 'none';
    }
    
    // Function to enable passenger input fields and set voucher limit
    function enablePassengerInputs(voucherCount) {
        const adultInput = document.getElementById('adult');
        const childrenInput = document.getElementById('children');
        const voucherLimitText = document.getElementById('voucher-limit-text');
        
        adultInput.disabled = false;
        childrenInput.disabled = false;
        adultInput.max = voucherCount;
        
        if (voucherCount > 0) {
            voucherLimitText.textContent = `Maximum ${voucherCount} adult passengers based on available vouchers`;
            voucherLimitText.style.display = 'block';
        } else {
            voucherLimitText.style.display = 'none';
        }
    }
</script>


<?php
require '_footer.php';
?>
