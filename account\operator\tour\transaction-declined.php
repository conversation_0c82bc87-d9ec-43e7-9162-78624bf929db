<?php
require '_header.php';
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Main Content Section -->
        <div class="border p-4 rounded-lg shadow-lg bg-white mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Transactions</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Declined</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-red-100 p-3 rounded-lg mr-4">
                <i class="fas fa-times-circle text-red-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Declined Bookings</h2>
                <p class="text-sm text-gray-600 mt-1">Bookings that have been rejected</p>
            </div>
        </div>

        <!-- Table -->
        <div class="mt-6 overflow-hidden">
            <div class="overflow-x-auto rounded-lg">
                <table id="search-table" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-responsive">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Resort</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Boat</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Reason</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                        </tr>
                    </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php
                    try {
                        $booking_status = "declined";

                        $bookingDetails = getAllBookingDetails($pdo, $booking_status);

                        if ($bookingDetails && count($bookingDetails) > 0) {
                            foreach ($bookingDetails as $row) {
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($row['designation'] ?? 'N/A'); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500 text-center"><?= htmlspecialchars($row['boatName'] ?? 'N/A'); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-red-600 text-center">
                                        <span class="line-clamp-1">
                                            <?= !empty($row['decline_reason']) ? htmlspecialchars($row['decline_reason']) : 'No reason provided'; ?>
                                        </span>
                                    </td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-center">
                                        <div class="flex flex-col sm:flex-row justify-center sm:space-x-2 space-y-2 sm:space-y-0">
                                            <button data-modal-target="view-details-modal-<?= $row['booking_id']; ?>" data-modal-toggle="view-details-modal-<?= $row['booking_id']; ?>" type="button" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md text-xs px-2 sm:px-3 py-1.5 w-full sm:w-auto">
                                                <i class="fas fa-eye mr-1"></i> <span class="whitespace-nowrap">View</span>
                                            </button>
                                            <a href="booking-edit.php?id=<?= $row['booking_id']; ?>" class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-2 sm:px-3 py-1.5 w-full sm:w-auto">
                                                <i class="fas fa-edit mr-1"></i> <span class="whitespace-nowrap">Edit</span>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <!-- View Details Modal -->
                                <div id="view-details-modal-<?= $row['booking_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative w-full max-w-md max-h-full">
                                        <!-- Modal Container -->
                                        <div class="relative bg-white rounded-lg shadow-lg">
                                            <!-- Modal Header -->
                                            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-red-600 to-red-500 rounded-t-lg">
                                                <h3 class="text-xl font-semibold text-white">
                                                    Declined Booking Details
                                                </h3>
                                                <button type="button" class="text-white hover:text-gray-200 rounded-full w-6 h-6 flex justify-center items-center transition-colors duration-200" data-modal-hide="view-details-modal-<?= $row['booking_id']; ?>">
                                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>
                                            <!-- Modal Body -->
                                            <div class="p-4 bg-white rounded-lg shadow space-y-4">
                                                <!-- Booking Reference -->
                                                <div class="bg-gray-50 p-3 rounded-lg">
                                                    <p class="text-xs text-gray-500 uppercase">Reference Number</p>
                                                    <p class="text-sm font-semibold text-gray-800"><?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?></p>
                                                </div>

                                                <!-- Decline Reason -->
                                                <div class="bg-red-50 p-3 rounded-lg border border-red-100">
                                                    <p class="text-xs text-red-500 uppercase font-medium">Reason for Decline</p>
                                                    <p class="text-sm text-red-700 mt-1">
                                                        <?= !empty($row['decline_reason']) ? htmlspecialchars($row['decline_reason']) : 'No reason provided'; ?>
                                                    </p>
                                                </div>

                                                <!-- Declined By -->
                                                <div class="bg-gray-50 p-3 rounded-lg">
                                                    <p class="text-xs text-gray-500 uppercase">Declined By</p>
                                                    <p class="text-sm font-semibold text-gray-800"><?= htmlspecialchars($row['declined_by'] ?? 'System'); ?></p>
                                                </div>

                                                <!-- Declined Date -->
                                                <div class="bg-gray-50 p-3 rounded-lg">
                                                    <p class="text-xs text-gray-500 uppercase">Declined On</p>
                                                    <p class="text-sm font-semibold text-gray-800">
                                                        <?= !empty($row['declined_date']) ? date('F d, Y h:i A', strtotime($row['declined_date'])) : 'N/A'; ?>
                                                    </p>
                                                </div>
                                            </div>

                                            <!-- Modal Footer -->
                                            <div class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                                                <a href="booking-edit.php?id=<?= $row['booking_id']; ?>" class="py-2 px-4 mr-2 text-sm font-semibold text-white bg-green-500 rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-200">
                                                    <i class="fas fa-edit mr-1"></i> Edit Booking
                                                </a>
                                                <button data-modal-hide="view-details-modal-<?= $row['booking_id']; ?>" type="button" class="py-2 px-4 text-sm font-semibold text-white bg-red-500 rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200">
                                                    Close
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        } else {
                        ?>
                            <tr>
                                <td colspan="4" class="px-4 sm:px-6 py-6 sm:py-8 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <div class="bg-red-100 p-3 rounded-full mb-3">
                                            <i class="fas fa-info-circle text-red-500 text-xl"></i>
                                        </div>
                                        <p class="text-gray-500 text-sm font-medium">No declined bookings found</p>
                                        <p class="text-gray-400 text-xs mt-1 max-w-xs mx-auto">When bookings are declined, they will appear here</p>
                                    </div>
                                </td>
                            </tr>
                        <?php
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="4" class="px-4 sm:px-6 py-4 text-center">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-3 inline-block mx-auto">
                                    <p class="text-sm text-red-600 font-medium">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </p>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
        </div>
    </div>


<?php
require '_footer.php';
?>
