<?php
// Start session with secure cookie parameters (if not set globally)
session_start([
    'cookie_secure'   => true,    // Only send cookie over HTTPS
    'cookie_httponly' => true,    // J<PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict' // Only send cookie in first-party contexts
]);

include 'connection/dbconnect.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Clear any previous errors
    unset($_SESSION['error']);

    try {
        // CSRF token validation using constant-time comparison
        if (
            !isset($_POST['csrf_token'], $_SESSION['csrf_token']) ||
            !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])
        ) {
            throw new Exception("Invalid CSRF token.");
        }

        // Sanitize and prepare inputs (use null coalescing operator to avoid undefined indexes)
        $firstname        = strtoupper(trim($_POST['firstname'] ?? ''));
        $middlename       = strtoupper(trim($_POST['middlename'] ?? ''));
        $lastname         = strtoupper(trim($_POST['lastname'] ?? ''));
        $extname          = strtoupper(trim($_POST['extname'] ?? ''));
        $address          = strtoupper(trim($_POST['address'] ?? ''));
        $contact          = trim($_POST['contact'] ?? '');
        $operatorType     = trim($_POST['operatorType'] ?? '');
        $username         = trim($_POST['username'] ?? '');
        $password         = trim($_POST['password'] ?? '');
        $confirm_password = trim($_POST['confirm_password'] ?? '');
        $email            = trim($_POST['email'] ?? '');

        // Ensure required fields are not empty
        if (
            !$firstname || !$lastname || !$address || !$contact ||
            !$operatorType || !$username || !$password || !$confirm_password || !$email
        ) {
            throw new Exception("Please fill in all required fields.");
        }

        // Validate email format
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Invalid email address.");
        }

        // Password complexity check (minimum 8 characters, you can add more rules if needed)
        if (strlen($password) < 8) {
            throw new Exception("Password must be at least 8 characters long.");
        }

        // Ensure passwords match
        if ($password !== $confirm_password) {
            throw new Exception("Passwords do not match.");
        }

        // Begin a database transaction
        $pdo->beginTransaction();

        // Check if username already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM operator_account WHERE username = :username");
        $stmt->execute([':username' => $username]);
        if ($stmt->fetchColumn() > 0) {
            throw new Exception("Username already exists.");
        }

        // Check if full name and operatorType combination exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM operator_info 
            WHERE firstname = :firstname 
              AND middlename = :middlename 
              AND lastname = :lastname 
              AND extname = :extname 
              AND operatorType = :operatorType
        ");
        $stmt->execute([
            ':firstname'    => $firstname,
            ':middlename'   => $middlename,
            ':lastname'     => $lastname,
            ':extname'      => $extname,
            ':operatorType' => $operatorType
        ]);
        if ($stmt->fetchColumn() > 0) {
            throw new Exception("Full name with the same operator type already exists.");
        }

        // Check if email and operatorType combination exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM operator_account a 
            JOIN operator_info i ON a.id = i.user_id 
            WHERE a.email = :email 
              AND i.operatorType = :operatorType
        ");
        $stmt->execute([':email' => $email, ':operatorType' => $operatorType]);
        if ($stmt->fetchColumn() > 0) {
            throw new Exception("Email with the same operator type already exists.");
        }

        // Generate a secure remember token using random bytes and the CSRF token
        $randomBytes   = bin2hex(random_bytes(18));
        $csrfToken     = $_POST['csrf_token'];
        $rememberToken = hash('sha256', $randomBytes . $csrfToken);

        $accountCreated = date("Y-m-d");
        $accountExpired = date("Y") . "-12-31";
        $userType       = "user";

        // Insert into operator_account
        $hashed_password = password_hash($password, PASSWORD_BCRYPT);
        $stmt = $pdo->prepare("
            INSERT INTO operator_account 
                (username, password, email, accountCreated, accountExpired, rememberToken, accType, accountStatus) 
            VALUES 
                (:username, :password, :email, :accountCreated, :accountExpired, :rememberToken, :accType, :accountStatus)
        ");
        $stmt->execute([
            ':username'       => $username,
            ':password'       => $hashed_password,
            ':email'          => $email,
            ':accountCreated' => $accountCreated,
            ':accountExpired' => $accountExpired,
            ':rememberToken'  => $rememberToken,
            ':accType'        => $userType,
            ':accountStatus'  => 'Incomplete'
        ]);

        $user_id = $pdo->lastInsertId();

        // Insert into operator_info
        $stmt = $pdo->prepare("
            INSERT INTO operator_info 
                (user_id, firstname, middlename, lastname, extname, address, contact, operatorType, designation) 
            VALUES 
                (:user_id, :firstname, :middlename, :lastname, :extname, :address, :contact, :operatorType, :designation)
        ");
        $stmt->execute([
            ':user_id'      => $user_id,
            ':firstname'    => $firstname,
            ':middlename'   => $middlename,
            ':lastname'     => $lastname,
            ':extname'      => $extname,
            ':address'      => $address,
            ':contact'      => $contact,
            ':operatorType' => $operatorType,
            ':designation'  => null
        ]);

        // Insert into operator_credentials
        $stmt = $pdo->prepare("
            INSERT INTO operator_credentials (user_id, picture_id, picture_permit) 
            VALUES (:user_id, :pid, :ppermit)
        ");
        $stmt->execute([
            ':user_id' => $user_id,
            ':pid'     => null,
            ':ppermit' => null
        ]);

        // Log the registration action for auditing
        $type = "Register - Account";
        $description = "New account registered: " . $username . " Operator Type: " . $operatorType;
        $stmt = $pdo->prepare("
            INSERT INTO system_logs (type, description) 
            VALUES (:type, :description)
        ");
        $stmt->execute([
            ':type'        => $type,
            ':description' => $description
        ]);

        // Set session variables and commit transaction
        $_SESSION['operator'] = $operatorType;
        $_SESSION['id']       = $user_id;

        $pdo->commit();

        header('Location: auth/register-other-details.php');
        exit();
    } catch (Exception $e) {
        // Roll back transaction on error
        $pdo->rollBack();
        $_SESSION['error'] = $e->getMessage();
        header("Location: register.php");
        exit();
    }
}
