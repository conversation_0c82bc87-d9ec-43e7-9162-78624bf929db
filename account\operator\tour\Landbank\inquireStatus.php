<?php
/**
 * Simplified Landbank Status Inquiry
 */

// Security check
define('LANDBANK_ACCESS', true);

// Include required files
require_once '../../../connection/dbconnect.php';
require_once 'config.php';

/**
 * Check transaction status with Landbank
 */
function checkTransactionStatus($transactionId, $pdo) {
    try {
        $params = [
            'merchantcode' => LANDBANK_MERCHANT_CODE,
            'refnum' => '',
            'trandetail1' => $transactionId,
            'username' => LANDBANK_USERNAME,
            'password' => LANDBANK_PASSWORD
        ];

        // Generate checksum for inquiry
        $data = $params['merchantcode'] . $params['refnum'] . $params['trandetail1'] .
                $params['username'] . $params['password'] . LANDBANK_SECRET_KEY;
        $params['checksum'] = strtoupper(hash('sha256', $data));

        // Send inquiry to Landbank
        $ch = curl_init(LANDBANK_API_URL . '/inquirestatus');
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($params),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("HTTP Error: $httpCode");
        }

        // Log the inquiry
        logTransaction($pdo, $transactionId, null, 'status_checked', $response);

        return [
            'success' => true,
            'response' => $response,
            'status' => str_starts_with($response, '00|') ? 'completed' : 'pending'
        ];

    } catch (Exception $e) {
        logTransaction($pdo, $transactionId, null, 'check_failed', null, $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Handle status check requests
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['transaction_id'])) {
    $transactionId = filter_input(INPUT_GET, 'transaction_id', FILTER_SANITIZE_STRING);

    if (empty($transactionId)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid transaction ID']);
        exit;
    }

    $result = checkTransactionStatus($transactionId, $pdo);

    header('Content-Type: application/json');
    echo json_encode($result);
    exit;
}

// Simple status check form for manual testing
if ($_SERVER['REQUEST_METHOD'] === 'GET' && !isset($_GET['transaction_id'])) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Landbank Status Check</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .form-group { margin: 10px 0; }
            input[type="text"] { padding: 8px; width: 300px; }
            button { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
            .result { margin: 20px 0; padding: 10px; border: 1px solid #ddd; }
        </style>
    </head>
    <body>
        <h2>Landbank Transaction Status Check</h2>
        <form method="GET">
            <div class="form-group">
                <label>Transaction ID:</label><br>
                <input type="text" name="transaction_id" placeholder="Enter transaction ID" required>
            </div>
            <button type="submit">Check Status</button>
        </form>
        <p><a href="../payment-tdf.php">← Back to Payment System</a></p>
    </body>
    </html>
    <?php
    exit;
}
