<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);

include 'connection/dbconnect.php';

// Redirect if already logged in
if (isset($_SESSION['id'], $_SESSION['accountType'])) {
    $accountType = $_SESSION['accountType'];
    $operator = $_SESSION['operator'] ?? null;

    if ($accountType === 'admin') {
        header('Location: account/employee/admin/home.php');
        exit();
    } elseif ($accountType === 'treasurer') {
        header('Location: account/employee/treasurer/home.php');
        exit();
    } elseif ($accountType === 'user') {
        if ($operator === "Tour operator") {
            header('Location: account/operator/tour/home.php');
            exit();
        } elseif ($operator === "Resort operator") {
            header('Location: account/operator/resort/home.php');
            exit();
        } elseif ($operator === "Boat operator") {
            header('Location: account/operator/boat/home.php');
            exit();
        }
    }
}

// Generate a CSRF token if it doesn't exist or has expired
if (empty($_SESSION['csrf_token']) || time() > ($_SESSION['csrf_token_expiration'] ?? 0)) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    $_SESSION['csrf_token_expiration'] = time() + 3600; // 1-hour validity
}

// Check system maintenance status
$setNotice = '';
try {
    $sql = 'SELECT status_maintenance FROM system LIMIT 1';
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$row) {
        $setNotice = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <strong>Error:</strong> System configuration error. Please try again later.
                      </div>';
    } elseif ($row['status_maintenance'] == 1) {
        $setNotice = '<div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
                        <strong>Notice:</strong> System is currently under maintenance. Some features may be unavailable.
                      </div>';
    }
} catch (PDOException $e) {
    $setNotice = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <strong>Error:</strong> Unable to connect to the system. Please try again later.
                  </div>';
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calaguas Booking | Forgot Password</title>
    <link rel="icon" type="image/x-icon" href="public/img/Logo.png" />
    <script src="https://kit.fontawesome.com/1ee5dd1d52.js" crossorigin="anonymous"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
</head>

<body class="bg-gray-50 min-h-screen flex flex-col">
    <div class="flex flex-col items-center justify-center flex-grow py-4">
        <div class="w-full max-w-sm p-6 bg-white rounded-lg shadow-lg">
            <div class="text-center mb-6">
                <img src="components/img/Logo.png" alt="VNLogo" class="w-32 mx-auto mb-4" draggable="false" />
                <h2 class="text-xl font-bold text-gray-800">Forgot Password?</h2>
                <p class="text-gray-600 mt-1 text-sm">Enter your email address and account type to receive a password reset link</p>
            </div>

            <?= $setNotice; ?>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <strong>Success:</strong> <?= htmlspecialchars($_SESSION['success'], ENT_QUOTES, 'UTF-8'); ?>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <strong>Error:</strong> <?= htmlspecialchars($_SESSION['error'], ENT_QUOTES, 'UTF-8'); ?>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <form action="inc.forgotpassword.php" method="POST" class="space-y-4">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">

                <div>
                    <label for="email" class="block mb-2 text-sm font-medium text-gray-900">Email Address</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        <input
                            type="email"
                            name="email"
                            id="email"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-9 p-2"
                            placeholder="Enter your email address"
                            required
                            maxlength="50"
                        />
                    </div>
                </div>

                <div>
                    <label for="operatorType" class="block mb-2 text-sm font-medium text-gray-900">Account Type</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                            <i class="fas fa-user-tag text-gray-400"></i>
                        </div>
                        <select
                            name="operatorType"
                            id="operatorType"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-9 p-2"
                            required
                        >
                            <option value="">Select your account type</option>
                            <option value="Tour operator">Tour Operator</option>
                            <option value="Resort operator">Resort Operator</option>
                            <option value="Boat operator">Boat Operator</option>
                        </select>
                    </div>
                </div>

                <button
                    type="submit"
                    class="w-full text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-200"
                >
                    <i class="fas fa-paper-plane mr-2"></i>Send Reset Link
                </button>
            </form>

            <div class="text-center mt-6">
                <p class="text-sm text-gray-600">
                    Remember your password?
                    <a href="login.php" class="font-medium text-blue-600 hover:underline">Back to Login</a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.bg-green-100, .bg-red-100, .bg-yellow-100');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>

</html>
