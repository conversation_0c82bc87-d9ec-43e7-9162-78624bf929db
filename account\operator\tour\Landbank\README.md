# Simplified Landbank Payment Integration System

A simple, secure, and efficient payment processing system for Landbank's Link.Biz payment gateway, integrated with the Calaguas Tourism booking system.

## 🚀 Features

### Core Functionality
- **Secure Payment Processing**: SHA-256 checksum validation and encrypted communications
- **Database Integration**: Simple transaction logging and status tracking
- **Status Checking**: Manual and API-based transaction status verification
- **Callback Handling**: Secure webhook processing for payment confirmations
- **Error Handling**: Comprehensive error logging and recovery

### Security Features
- CSRF protection for all forms
- Input validation and sanitization
- Secure configuration management
- SSL/TLS verification for API calls
- Transaction logging and audit trails

### Monitoring & Management
- Treasurer dashboard for transaction monitoring
- Manual status checking interface
- Transaction statistics and reporting
- Simple database table structure

## 📁 Simplified File Structure

```
Landbank/
├── config.php               # Simple configuration and shared functions
├── bankTransfer.php         # Main payment processing
├── callback.php             # Payment callback handler
├── inquireStatus.php        # Status inquiry system
├── create_table.php         # One-time table creation script
└── README.md                # This documentation

Treasurer Dashboard:
└── account/employee/treasurer/landbank-payments.php  # Monitoring interface
```

## 🔧 Installation & Setup

### 1. Database Setup
**Run the table creation script once:**

```bash
# Navigate to your browser and run:
http://yourdomain.com/account/operator/tour/Landbank/create_table.php
```

This creates the `landbank_transactions` table with:
- Transaction tracking with unique IDs
- Payment status management
- Callback data storage
- Simple logging structure

**Important:** Delete `create_table.php` after running it for security.

### 2. Configuration
Edit `config.php` to set your Landbank credentials:

```php
// Update these values in config.php
define('LANDBANK_MERCHANT_CODE', 'your_merchant_code');
define('LANDBANK_USERNAME', 'your_username');
define('LANDBANK_PASSWORD', 'your_password');
define('LANDBANK_SECRET_KEY', 'your_secret_key');
define('LANDBANK_CALLBACK_URL', 'https://yourdomain.com/path/to/callback.php');
```

### 3. Test the Integration
- Use the payment button in `payment-tdf.php`
- Check transaction logs in the treasurer dashboard
- Test status checking via `inquireStatus.php`

## 💻 Usage

### Processing Payments

#### From Booking System
```php
// Direct payment processing
$result = processPaymentFromBooking($bookingId, $pdo);

if ($result['success']) {
    // Redirect to Landbank payment portal
    header('Location: ' . $result['redirect_url']);
} else {
    // Handle error
    echo $result['error'];
}
```

#### AJAX Payment Request
```javascript
// POST request to bankTransfer.php
fetch('Landbank/bankTransfer.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
        'booking_id': bookingId,
        'csrf_token': csrfToken
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        window.location.href = data.redirect_url;
    } else {
        alert('Payment error: ' + data.error);
    }
});
```

### Status Checking

#### Manual Status Check
```php
// Check specific transaction
$result = inquireTransactionStatus($params, $secretKey, $pdo, $transactionId);

if ($result['success']) {
    $status = processStatusResponse($result['response'], $transactionId, $pdo);
    echo "Payment status: " . $status['status'];
}
```

#### Automated Checking
The cron job automatically checks all pending transactions and updates their status.

### Callback Processing
The callback handler automatically processes payment confirmations from Landbank and updates the database accordingly.

## 🔍 Monitoring

### Treasurer Dashboard
Access `account/employee/treasurer/landbank-payments.php` to:
- View transaction statistics
- Monitor payment statuses
- Filter transactions by date/status
- Manually check transaction status
- Approve or mark failed transactions
- View customer and booking information

### Log Files
- `logs/status_check.log` - Automated status check logs
- PHP error logs for detailed error tracking
- Security event logs for audit trails

## 🛡️ Security Features

### Data Protection
- Sensitive data encryption in configuration
- Password and secret key redaction in logs
- Secure token generation for CSRF protection
- Input validation and sanitization

### API Security
- SSL/TLS verification for all API calls
- Rate limiting to prevent abuse
- Comprehensive error handling
- Timeout protection

### Audit Trail
- Complete transaction logging
- Security event tracking
- Error and exception logging
- Performance monitoring

## 🔄 Integration Points

### Database Tables
- `cb_bookings` - Booking information
- `cb_payments` - Payment records
- `cb_booking_approvals` - Approval workflow
- `landbank_transactions` - Landbank-specific transaction data

### System Integration
- Seamless integration with existing booking system
- Automatic payment status updates
- Booking approval workflow integration
- Tourist information synchronization

## 🚨 Error Handling

### Common Issues
1. **Connection Timeouts**: Automatic retry with exponential backoff
2. **Invalid Checksums**: Comprehensive validation and logging
3. **Database Errors**: Transaction rollback and error recovery
4. **API Rate Limits**: Built-in rate limiting and delay mechanisms

### Troubleshooting
- Check log files for detailed error information
- Verify configuration settings
- Test API connectivity with echo test
- Monitor transaction status in admin dashboard

## 📊 Performance

### Optimization Features
- Connection pooling for database operations
- Efficient query design with proper indexing
- Minimal memory footprint
- Optimized cURL settings

### Monitoring
- Transaction processing time tracking
- API response time monitoring
- Database query performance
- Memory usage optimization

## 🔮 Future Enhancements

### Planned Features
- Real-time payment notifications
- Advanced reporting and analytics
- Multi-currency support
- Enhanced fraud detection
- Mobile-optimized interfaces

### Scalability
- Horizontal scaling support
- Load balancing compatibility
- Microservice architecture readiness
- Cloud deployment optimization

## 📞 Support

For technical support or questions:
1. Check the log files for error details
2. Review the admin dashboard for transaction status
3. Test API connectivity using the echo test function
4. Verify configuration settings

## 📄 License

This system is part of the Calaguas Tourism Management System and follows the same licensing terms.
