<?php
session_start();
require '../../../../connection/dbconnect.php';

// Check if the user is logged in and has the correct account type
if (!isset($_SESSION['id']) || !isset($_SESSION['accountType']) || $_SESSION['accountType'] !== 'admin') {
    header("Location: ../../../../login.php");
    exit;
}

// Verify CSRF token
if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
    $_SESSION['error'] = "Invalid request. Please try again.";
    header("Location: ../voucher.php");
    exit;
}

// Add voucher
if (isset($_POST['addVoucherBtn'])) {
    try {
        // Validate inputs
        $operatorId = filter_input(INPUT_POST, 'operatorId', FILTER_SANITIZE_NUMBER_INT);
        $voucherType = filter_input(INPUT_POST, 'voucherType', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        $voucherCount = filter_input(INPUT_POST, 'voucherCount', FILTER_SANITIZE_NUMBER_INT);

        if (empty($operatorId) || empty($voucherType) || empty($voucherCount) || $voucherCount < 1) {
            throw new Exception("All fields are required and voucher count must be at least 1.");
        }

        // Check if operator exists in cb_vouchers table
        $checkStmt = $pdo->prepare("SELECT * FROM cb_vouchers WHERE operator_id = :operator_id");
        $checkStmt->execute([':operator_id' => $operatorId]);
        $existingVoucher = $checkStmt->fetch(PDO::FETCH_ASSOC);

        // Begin transaction
        $pdo->beginTransaction();

        if ($existingVoucher) {
            // Update existing voucher record
            if ($voucherType === 'vinzons') {
                $currentCount = (int)$existingVoucher['voucher_vinzons'];
                $newCount = $currentCount + (int)$voucherCount;
                
                $updateStmt = $pdo->prepare("UPDATE cb_vouchers SET voucher_vinzons = :voucher_count WHERE operator_id = :operator_id");
                $updateStmt->execute([
                    ':voucher_count' => $newCount,
                    ':operator_id' => $operatorId
                ]);
            } else {
                $currentCount = (int)$existingVoucher['voucher_others'];
                $newCount = $currentCount + (int)$voucherCount;
                
                $updateStmt = $pdo->prepare("UPDATE cb_vouchers SET voucher_others = :voucher_count WHERE operator_id = :operator_id");
                $updateStmt->execute([
                    ':voucher_count' => $newCount,
                    ':operator_id' => $operatorId
                ]);
            }
        } else {
            // Create new voucher record
            if ($voucherType === 'vinzons') {
                $insertStmt = $pdo->prepare("INSERT INTO cb_vouchers (operator_id, voucher_vinzons, voucher_others) VALUES (:operator_id, :voucher_vinzons, 0)");
                $insertStmt->execute([
                    ':operator_id' => $operatorId,
                    ':voucher_vinzons' => $voucherCount
                ]);
            } else {
                $insertStmt = $pdo->prepare("INSERT INTO cb_vouchers (operator_id, voucher_vinzons, voucher_others) VALUES (:operator_id, 0, :voucher_others)");
                $insertStmt->execute([
                    ':operator_id' => $operatorId,
                    ':voucher_others' => $voucherCount
                ]);
            }
        }

        // Commit transaction
        $pdo->commit();

        $_SESSION['success'] = "Vouchers added successfully.";
    } catch (Exception $e) {
        // Rollback transaction on error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $_SESSION['error'] = "Error: " . $e->getMessage();
    }

    header("Location: ../voucher.php");
    exit;
}

// Delete voucher
if (isset($_POST['deleteVoucher'])) {
    try {
        $rowId = filter_input(INPUT_POST, 'rowId', FILTER_SANITIZE_NUMBER_INT);
        
        if (empty($rowId)) {
            throw new Exception("Invalid voucher ID.");
        }

        $stmt = $pdo->prepare("DELETE FROM cb_vouchers WHERE operator_id = :id");
        $stmt->execute([':id' => $rowId]);

        if ($stmt->rowCount() > 0) {
            $_SESSION['success'] = "Voucher deleted successfully.";
        } else {
            $_SESSION['error'] = "No voucher found with that ID.";
        }
    } catch (Exception $e) {
        $_SESSION['error'] = "Error: " . $e->getMessage();
    }

    header("Location: ../voucher.php");
    exit;
}

// Redirect back if no action was taken
header("Location: ../voucher.php");
exit;
