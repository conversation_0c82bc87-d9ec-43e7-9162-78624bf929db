<?php
/**
 * Simple Landbank Configuration
 */

// Prevent direct access
if (!defined('LANDBANK_ACCESS')) {
    die('Direct access not allowed');
}

// Landbank API Configuration
define('LANDBANK_API_URL', 'https://lbp-eps.landbank.com/LBP-LinkBiz-RS/rs');
define('LANDBANK_MERCHANT_CODE', '1381');
define('LANDBANK_USERNAME', 'username');
define('LANDBANK_PASSWORD', 'password');
define('LANDBANK_SECRET_KEY', 'N\\HWJUKFHQX');
define('LANDBANK_CALLBACK_URL', 'https://code-garden.io/callback.php');

/**
 * Generate SHA-256 checksum for payment
 */
function generatePaymentChecksum($params, $secretKey) {
    $data = $params['trxnamt'] . $params['merchantcode'] . $params['trxndetails'];

    // Add transaction details
    for ($i = 1; $i <= 20; $i++) {
        $data .= $params['trandetail' . $i] ?? '';
    }

    $data .= $params['username'] . $params['password'] . $secretKey;
    return strtoupper(hash('sha256', $data));
}

/**
 * Generate SHA-256 checksum for callback verification
 */
function generateCallbackChecksum($params, $secretKey) {
    $data = $params['LBPRefNum'] . $params['MerchantRefNum'] .
            $params['TrxnAmount'] . $params['LBPConfNum'] .
            $params['LBPConfDate'] . $secretKey;
    return strtoupper(hash('sha256', $data));
}

/**
 * Log transaction to database
 * Note: Run create_table.php first to create the required table
 */
function logTransaction($pdo, $transactionId, $bookingId, $status, $data = null, $error = null) {
    try {
        $sql = "INSERT INTO landbank_transactions
                (transaction_id, booking_id, status, request_data, response_data, error_message)
                VALUES (:transaction_id, :booking_id, :status, :request_data, :response_data, :error_message)
                ON DUPLICATE KEY UPDATE
                status = VALUES(status),
                response_data = VALUES(response_data),
                error_message = VALUES(error_message),
                updated_at = CURRENT_TIMESTAMP";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':transaction_id' => $transactionId,
            ':booking_id' => $bookingId,
            ':status' => $status,
            ':request_data' => is_array($data) ? json_encode($data) : null,
            ':response_data' => is_string($data) ? $data : null,
            ':error_message' => $error
        ]);

        return true;
    } catch (Exception $e) {
        error_log("Failed to log transaction: " . $e->getMessage());
        return false;
    }
}

/**
 * Get booking details for payment
 */
function getBookingForPayment($pdo, $bookingId) {
    try {
        $sql = "SELECT
                    bb.booking_id,
                    bb.referenceNum,
                    bb.check_in_date,
                    bb.check_out_date,
                    cp.total_adults,
                    cp.total_amount,
                    cp.payment_status,
                    pl.portName,
                    CONCAT(oi.firstname, ' ', COALESCE(oi.middlename, ''), ' ', oi.lastname) as customer_name,
                    oi.contact_number,
                    oi.email
                FROM cb_bookings bb
                LEFT JOIN cb_payments cp ON bb.booking_id = cp.booking_id
                LEFT JOIN port_list pl ON bb.port_id = pl.id
                LEFT JOIN operator_info oi ON bb.tour_operator_id = oi.user_id
                WHERE bb.booking_id = :booking_id";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([':booking_id' => $bookingId]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Failed to get booking details: " . $e->getMessage());
        return false;
    }
}

/**
 * Update payment status
 */
function updatePaymentStatus($pdo, $bookingId, $status) {
    try {
        $sql = "UPDATE cb_payments SET
                payment_status = :status,
                payment_method = 'Bank_Transfer'
                WHERE booking_id = :booking_id";

        $stmt = $pdo->prepare($sql);
        return $stmt->execute([
            ':status' => $status,
            ':booking_id' => $bookingId
        ]);
    } catch (Exception $e) {
        error_log("Failed to update payment status: " . $e->getMessage());
        return false;
    }
}

/**
 * Get transaction statistics for treasurer dashboard
 */
function getTransactionStats($pdo, $dateFrom = null, $dateTo = null) {
    try {
        $sql = "SELECT
                    status,
                    COUNT(*) as count,
                    SUM(amount) as total_amount
                FROM landbank_transactions";
        $params = [];
        $conditions = [];

        if ($dateFrom) {
            $conditions[] = "DATE(created_at) >= :date_from";
            $params[':date_from'] = $dateFrom;
        }

        if ($dateTo) {
            $conditions[] = "DATE(created_at) <= :date_to";
            $params[':date_to'] = $dateTo;
        }

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }

        $sql .= " GROUP BY status";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Failed to get transaction stats: " . $e->getMessage());
        return [];
    }
}
