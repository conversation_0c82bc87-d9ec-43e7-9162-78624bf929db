<?php
require '_header.php';
?>

<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">

<style>
/* Custom DataTables styling */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    @apply px-3 py-2 text-sm text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700 rounded-md;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    @apply bg-blue-600 text-white border-blue-600 hover:bg-blue-700;
}

.dataTables_wrapper .dataTables_info {
    @apply text-sm text-gray-700 mb-4;
}

.dataTables_wrapper .dataTables_paginate {
    @apply flex justify-center items-center space-x-1 mt-4;
}

/* Hide default search and length controls */
.dataTables_filter,
.dataTables_length {
    display: none !important;
}

/* Custom responsive design */
@media (max-width: 1023px) {
    .desktop-table {
        display: none !important;
    }
    .mobile-cards {
        display: block !important;
    }
}

@media (min-width: 1024px) {
    .desktop-table {
        display: block !important;
    }
    .mobile-cards {
        display: none !important;
    }
}
</style>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Main Content Section -->
        <div class="border p-4 rounded-lg shadow-lg bg-white mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Transactions</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Pending</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-yellow-100 p-3 rounded-lg mr-4">
                <i class="fas fa-clock text-yellow-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Pending Bookings</h2>
                <p class="text-sm text-gray-600 mt-1">Bookings awaiting approval from operators</p>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="mt-6 mb-4 flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div class="flex-1 max-w-md">
                <label for="search" class="sr-only">Search bookings</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="search" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="Search by reference, resort, or boat...">
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <label for="entries" class="text-sm text-gray-700">Show:</label>
                <select id="entries" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span class="text-sm text-gray-700">entries</span>
            </div>
        </div>

        <!-- Desktop Table View -->
        <div class="mt-6 desktop-table">
            <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                <table id="bookingsTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resort</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Boat</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" data-orderable="false">Action</th>
                        </tr>
                    </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php
                    try {
                        $booking_status = "pending";

                        $bookingDetails = getAllBookingDetails($pdo, $booking_status);

                        if ($bookingDetails && count($bookingDetails) > 0) {
                            foreach ($bookingDetails as $row) {
                                $setColorResort = ($row['resort'] === "Approved") ? "green" : "yellow";
                                $setColorBoat   = ($row['boat'] === "Approved")   ? "green" : "yellow";
                                $setColorTreasurer = ($row['treasurer'] === "Approved") ? "green" : "yellow";
                                $setColorMtho = ($row['mtho'] === "Approved") ? "green" : "yellow";

                                // Check if both resort and boat have approved
                                if($row['mtho'] === "Waiting"){
                                    $bothApproved = ($row['resort'] === "Approved" && $row['boat'] === "Approved");
                                }else{
                                    $bothApproved = "";
                                }
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600"><?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?= htmlspecialchars($row['designation']); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700"><?= htmlspecialchars($row['boatName']); ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div class="flex space-x-1">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?= $row['resort'] === 'Approved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' ?>">
                                                Resort: <?= $row['resort'] ?>
                                            </span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?= $row['boat'] === 'Approved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' ?>">
                                                Boat: <?= $row['boat'] ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <div class="relative inline-block text-left">
                                            <button type="button" class="inline-flex items-center justify-center w-8 h-8 text-gray-400 bg-white rounded-full hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 border border-gray-300" id="desktop-menu-button-<?= $row['booking_id']; ?>" onclick="toggleDesktopDropdown('desktop-dropdown-<?= $row['booking_id']; ?>')">
                                                <span class="sr-only">Open options</span>
                                                <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                                                </svg>
                                            </button>

                                            <div class="hidden absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50" id="desktop-dropdown-<?= $row['booking_id']; ?>" style="position: absolute; top: 100%; right: 0;">
                                                <div class="py-1">
                                                    <button
                                                        data-modal-target="view-approvals-modal-<?= $row['booking_id']; ?>"
                                                        data-modal-toggle="view-approvals-modal-<?= $row['booking_id']; ?>"
                                                        type="button"
                                                        class="group flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                        onclick="closeDesktopDropdown('desktop-dropdown-<?= $row['booking_id']; ?>')"
                                                    >
                                                        <i class="fas fa-clipboard-check mr-3 text-blue-500"></i>
                                                        View Details
                                                    </button>

                                                    <?php if($bothApproved): ?>
                                                    <a
                                                        href="payment-tdf.php?id=<?= $row['booking_id']; ?>"
                                                        class="group flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                    >
                                                        <i class="fas fa-credit-card mr-3 text-green-500"></i>
                                                        Proceed to Payment
                                                    </a>
                                                    <?php endif; ?>

                                                    <button
                                                        data-modal-target="cancel-booking-modal-<?= $row['booking_id']; ?>"
                                                        data-modal-toggle="cancel-booking-modal-<?= $row['booking_id']; ?>"
                                                        type="button"
                                                        class="group flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                        onclick="closeDesktopDropdown('desktop-dropdown-<?= $row['booking_id']; ?>')"
                                                    >
                                                        <i class="fas fa-times mr-3 text-red-500"></i>
                                                        Request Cancellation
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <!-- View Passenger Modal -->
                                <div id="view-approvals-modal-<?= $row['booking_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative w-full max-w-md max-h-full"> <!-- Increased max-width to max-w-md for better readability -->
                                        <!-- Modal Container -->
                                        <div class="relative bg-white rounded-lg shadow-lg">
                                            <!-- Modal Header -->
                                            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-600 to-blue-700 rounded-t-lg">
                                                <div class="flex items-center">
                                                    <i class="fas fa-clipboard-check text-white mr-2"></i>
                                                    <h3 class="text-xl font-semibold text-white">
                                                        Approval Status
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-blue-800 hover:bg-blue-900" data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>">
                                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <!-- Modal Body -->
                                            <div class="p-5 bg-white rounded-lg space-y-5">
                                                <!-- Booking Reference -->
                                                <div class="bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4">
                                                    <p class="text-xs text-blue-500 uppercase font-medium">Reference Number</p>
                                                    <p class="text-sm font-semibold text-blue-800 mt-1"><?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?></p>
                                                </div>

                                                <!-- Status Cards -->
                                                <div class="grid grid-cols-1 gap-3">
                                                    <!-- Resort -->
                                                    <div class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                                <i class="fas fa-hotel text-blue-600"></i>
                                                            </div>
                                                            <span class="text-sm font-medium text-gray-700">Resort</span>
                                                        </div>
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if($row['resort'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif($row['resort'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                            <?php if($row['resort'] === 'Approved'): ?>
                                                                <i class="fas fa-check-circle mr-1"></i>
                                                            <?php elseif($row['resort'] === 'Pending'): ?>
                                                                <i class="fas fa-clock mr-1"></i>
                                                            <?php else: ?>
                                                                <i class="fas fa-hourglass-half mr-1"></i>
                                                            <?php endif; ?>
                                                            <?= $row['resort']; ?>
                                                        </span>
                                                    </div>

                                                    <!-- Boat -->
                                                    <div class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                                <i class="fas fa-ship text-blue-600"></i>
                                                            </div>
                                                            <span class="text-sm font-medium text-gray-700">Boat</span>
                                                        </div>
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if($row['boat'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif($row['boat'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                            <?php if($row['boat'] === 'Approved'): ?>
                                                                <i class="fas fa-check-circle mr-1"></i>
                                                            <?php elseif($row['boat'] === 'Pending'): ?>
                                                                <i class="fas fa-clock mr-1"></i>
                                                            <?php else: ?>
                                                                <i class="fas fa-hourglass-half mr-1"></i>
                                                            <?php endif; ?>
                                                            <?= $row['boat']; ?>
                                                        </span>
                                                    </div>

                                                    <!-- Treasurer -->
                                                    <div class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                                <i class="fas fa-money-bill-wave text-blue-600"></i>
                                                            </div>
                                                            <span class="text-sm font-medium text-gray-700">Treasurer</span>
                                                        </div>
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if($row['treasurer'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif($row['treasurer'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                            <?php if($row['treasurer'] === 'Approved'): ?>
                                                                <i class="fas fa-check-circle mr-1"></i>
                                                            <?php elseif($row['treasurer'] === 'Pending'): ?>
                                                                <i class="fas fa-clock mr-1"></i>
                                                            <?php else: ?>
                                                                <i class="fas fa-hourglass-half mr-1"></i>
                                                            <?php endif; ?>
                                                            <?= $row['treasurer']; ?>
                                                        </span>
                                                    </div>

                                                    <!-- MTHO -->
                                                    <div class="bg-white border border-gray-200 rounded-lg p-3 shadow-sm flex items-center justify-between">
                                                        <div class="flex items-center">
                                                            <div class="bg-blue-100 p-2 rounded-full mr-3">
                                                                <i class="fas fa-building text-blue-600"></i>
                                                            </div>
                                                            <span class="text-sm font-medium text-gray-700">MTHO</span>
                                                        </div>
                                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                            <?php if($row['mtho'] === 'Approved'): ?>
                                                                bg-green-100 text-green-800
                                                            <?php elseif($row['mtho'] === 'Pending'): ?>
                                                                bg-yellow-100 text-yellow-800
                                                            <?php else: ?>
                                                                bg-gray-100 text-gray-800
                                                            <?php endif; ?>">
                                                            <?php if($row['mtho'] === 'Approved'): ?>
                                                                <i class="fas fa-check-circle mr-1"></i>
                                                            <?php elseif($row['mtho'] === 'Pending'): ?>
                                                                <i class="fas fa-clock mr-1"></i>
                                                            <?php else: ?>
                                                                <i class="fas fa-hourglass-half mr-1"></i>
                                                            <?php endif; ?>
                                                            <?= $row['mtho']; ?>
                                                        </span>
                                                    </div>
                                                </div>

                                                <?php if($row['resort'] === 'Approved' && $row['boat'] === 'Approved' && $row['mtho'] === 'Waiting'): ?>
                                                <!-- Approval Complete Message -->
                                                <div class="flex p-4 mt-2 text-green-800 rounded-lg bg-green-50 border border-green-200" role="alert">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-check-circle text-green-600"></i>
                                                    </div>
                                                    <div class="ml-3 text-sm font-medium text-green-800">
                                                        Both Resort and Boat operators have approved this booking. You can now proceed to payment.
                                                    </div>
                                                </div>
                                                <?php else: ?>
                                                <!-- Waiting for Approval Message -->
                                                <div class="flex p-4 mt-2 text-yellow-800 rounded-lg bg-yellow-50 border border-yellow-200" role="alert">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-exclamation-circle text-yellow-600"></i>
                                                    </div>
                                                    <div class="ml-3 text-sm font-medium text-yellow-800">
                                                        Waiting for approval from all required operators before proceeding to payment.
                                                    </div>
                                                </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Modal Footer -->
                                            <div class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                                                <?php if($row['resort'] === 'Approved' && $row['boat'] === 'Approved' && $row['mtho'] === 'Waiting'): ?>
                                                <a href="payment-tdf.php?id=<?= $row['booking_id']; ?>" class="py-2 px-4 mr-3 text-sm font-semibold text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all duration-200">
                                                    <i class="fas fa-credit-card mr-1"></i> Proceed to Payment
                                                </a>
                                                <?php endif; ?>
                                                <button data-modal-hide="view-approvals-modal-<?= $row['booking_id']; ?>" type="button" class="py-2 px-4 text-sm font-semibold text-white bg-red-500 rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200">
                                                    <i class="fas fa-times mr-1"></i> Close
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cancel Booking Modal -->
                                <div id="cancel-booking-modal-<?= $row['booking_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative w-full max-w-md max-h-full">
                                        <!-- Modal Container -->
                                        <div class="relative bg-white rounded-lg shadow-lg">
                                            <!-- Modal Header -->
                                            <div class="flex items-center justify-between p-4 bg-gradient-to-r from-red-600 to-red-700 rounded-t-lg">
                                                <div class="flex items-center">
                                                    <i class="fas fa-exclamation-triangle text-white mr-2"></i>
                                                    <h3 class="text-xl font-semibold text-white">
                                                        Request Cancellation
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-white hover:text-gray-200 rounded-full w-8 h-8 flex justify-center items-center transition-colors duration-200 bg-red-800 hover:bg-red-900" data-modal-hide="cancel-booking-modal-<?= $row['booking_id']; ?>">
                                                    <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <!-- Modal Body -->
                                            <div class="p-5 bg-white rounded-lg space-y-4">
                                                <!-- Warning Message -->
                                                <div class="flex p-4 text-red-800 rounded-lg bg-red-50 border border-red-200" role="alert">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                                                    </div>
                                                    <div class="ml-3 text-sm font-medium text-red-800">
                                                        <strong>Warning:</strong> This will submit a cancellation request. Are you sure you want to request cancellation for this booking?
                                                    </div>
                                                </div>

                                                <!-- Booking Reference -->
                                                <div class="bg-gray-50 p-3 rounded-lg border border-gray-100">
                                                    <p class="text-xs text-gray-500 uppercase font-medium">Reference Number</p>
                                                    <p class="text-sm font-semibold text-gray-800 mt-1"><?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?></p>
                                                </div>

                                                <!-- Booking Details -->
                                                <div class="space-y-2">
                                                    <div class="flex justify-between">
                                                        <span class="text-sm text-gray-600">Resort:</span>
                                                        <span class="text-sm font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span class="text-sm text-gray-600">Boat:</span>
                                                        <span class="text-sm font-medium text-gray-900"><?= htmlspecialchars($row['boatName']); ?></span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Modal Footer -->
                                            <div class="flex justify-end items-center p-4 bg-gray-50 border-t border-gray-100 rounded-b-lg space-x-3">
                                                <button data-modal-hide="cancel-booking-modal-<?= $row['booking_id']; ?>" type="button" class="py-2 px-4 text-sm font-semibold text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                    <i class="fas fa-times mr-1"></i> No, Keep Booking
                                                </button>
                                                <form action="inc/inc.booking.php" method="POST" class="inline">
                                                    <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token']; ?>">
                                                    <input type="hidden" name="booking_id" value="<?= $row['booking_id']; ?>">
                                                    <input type="hidden" name="referenceNumber" value="<?= htmlspecialchars($row['referenceNum']); ?>">
                                                    <button type="submit" name="cancelBooking" class="py-2 px-4 text-sm font-semibold text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-400 transition-all duration-200">
                                                        <i class="fas fa-paper-plane mr-1"></i> Yes, Submit Request
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        } else {
                            // No pending bookings found
                            ?>
                            <tr>
                                <td colspan="5" class="px-6 py-8 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <div class="bg-yellow-100 p-3 rounded-full mb-3">
                                            <i class="fas fa-inbox text-yellow-500 text-xl"></i>
                                        </div>
                                        <p class="text-gray-500 text-sm font-medium">No pending bookings found</p>
                                        <p class="text-gray-400 text-xs mt-1 max-w-xs mx-auto">When bookings are pending approval, they will appear here</p>
                                    </div>
                                </td>
                            </tr>
                            <?php
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-3 inline-block mx-auto">
                                    <p class="text-sm text-red-600 font-medium">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </p>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
            </div>
        </div>

        <!-- Mobile Card View -->
        <div class="mt-6 mobile-cards hidden">
            <div id="mobileCardContainer" class="space-y-4">
                <!-- Cards will be populated by JavaScript -->
            </div>

            <!-- Mobile Pagination -->
            <div class="mt-6 flex flex-col items-center space-y-4">
                <div id="mobileInfo" class="text-sm text-gray-700"></div>
                <div id="mobilePagination" class="flex justify-center items-center space-x-1"></div>
            </div>
        </div>

    </div>
</div>

<!-- DataTables JS -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script>
// Store all booking data for mobile view
let allBookings = [];
let filteredBookings = [];
let currentPage = 1;
let itemsPerPage = 10;

$(document).ready(function() {
    // Extract booking data from table for mobile view
    extractBookingData();

    // Initialize DataTable for desktop
    var table = $('#bookingsTable').DataTable({
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        order: [[0, 'desc']], // Sort by reference number descending
        columnDefs: [
            {
                targets: -1, // Last column (Action)
                orderable: false,
                searchable: false,
                className: 'text-center'
            },
            {
                targets: 3, // Status column
                orderable: false
            }
        ],
        language: {
            search: "",
            searchPlaceholder: "Search bookings...",
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ bookings",
            infoEmpty: "No bookings found",
            infoFiltered: "(filtered from _MAX_ total bookings)",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            },
            emptyTable: "No pending bookings found"
        },
        initComplete: function() {
            // Hide the default search and length controls since we have custom ones
            $('.dataTables_filter').hide();
            $('.dataTables_length').hide();

            // Connect custom search for both desktop and mobile
            $('#search').on('keyup', function() {
                const searchTerm = this.value;
                table.search(searchTerm).draw();
                filterMobileCards(searchTerm);
            });

            // Connect custom length selector for both desktop and mobile
            $('#entries').on('change', function() {
                const newLength = parseInt(this.value);
                table.page.len(newLength).draw();
                itemsPerPage = newLength;
                currentPage = 1;
                renderMobileCards();
            });

            // Initial mobile render
            renderMobileCards();
        }
    });
});

function extractBookingData() {
    $('#bookingsTable tbody tr').each(function() {
        const $row = $(this);
        if ($row.find('td').length > 1) { // Skip empty state rows
            const booking = {
                reference: $row.find('td:eq(0)').text().trim(),
                resort: $row.find('td:eq(1)').text().trim(),
                boat: $row.find('td:eq(2)').text().trim(),
                status: $row.find('td:eq(3)').html(),
                action: $row.find('td:eq(4)').html()
            };
            allBookings.push(booking);
        }
    });
    filteredBookings = [...allBookings];
}

function filterMobileCards(searchTerm) {
    if (!searchTerm) {
        filteredBookings = [...allBookings];
    } else {
        filteredBookings = allBookings.filter(booking =>
            booking.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
            booking.resort.toLowerCase().includes(searchTerm.toLowerCase()) ||
            booking.boat.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }
    currentPage = 1;
    renderMobileCards();
}

function renderMobileCards() {
    const container = $('#mobileCardContainer');
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const pageBookings = filteredBookings.slice(start, end);

    if (pageBookings.length === 0) {
        container.html(`
            <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-8 text-center">
                <div class="bg-yellow-100 p-3 rounded-full mb-3 inline-block">
                    <i class="fas fa-inbox text-yellow-500 text-xl"></i>
                </div>
                <p class="text-gray-500 text-sm font-medium">No pending bookings found</p>
                <p class="text-gray-400 text-xs mt-1">When bookings are pending approval, they will appear here</p>
            </div>
        `);
    } else {
        let cardsHtml = '';
        pageBookings.forEach(booking => {
            cardsHtml += `
                <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-4">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h3 class="text-sm font-medium text-blue-600">${booking.reference}</h3>
                            <p class="text-xs text-gray-500 mt-1">Reference Number</p>
                        </div>
                        <div class="relative">
                            ${booking.action}
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Resort:</span>
                            <span class="text-sm font-medium text-gray-900">${booking.resort}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Boat:</span>
                            <span class="text-sm font-medium text-gray-900">${booking.boat}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Status:</span>
                            <div class="flex space-x-1">${booking.status}</div>
                        </div>
                    </div>
                </div>
            `;
        });
        container.html(cardsHtml);
    }

    updateMobilePagination();
}

function updateMobilePagination() {
    const totalPages = Math.ceil(filteredBookings.length / itemsPerPage);
    const start = (currentPage - 1) * itemsPerPage + 1;
    const end = Math.min(currentPage * itemsPerPage, filteredBookings.length);

    // Update info
    $('#mobileInfo').text(`Showing ${start} to ${end} of ${filteredBookings.length} bookings`);

    // Update pagination
    let paginationHtml = '';

    if (totalPages > 1) {
        // Previous button
        paginationHtml += `
            <button onclick="changeMobilePage(${currentPage - 1})"
                    class="px-3 py-2 text-sm text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700 rounded-md ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}"
                    ${currentPage === 1 ? 'disabled' : ''}>
                Previous
            </button>
        `;

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage) {
                paginationHtml += `
                    <button class="px-3 py-2 text-sm bg-blue-600 text-white border-blue-600 rounded-md">
                        ${i}
                    </button>
                `;
            } else {
                paginationHtml += `
                    <button onclick="changeMobilePage(${i})"
                            class="px-3 py-2 text-sm text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700 rounded-md">
                        ${i}
                    </button>
                `;
            }
        }

        // Next button
        paginationHtml += `
            <button onclick="changeMobilePage(${currentPage + 1})"
                    class="px-3 py-2 text-sm text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700 rounded-md ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}"
                    ${currentPage === totalPages ? 'disabled' : ''}>
                Next
            </button>
        `;
    }

    $('#mobilePagination').html(paginationHtml);
}

function changeMobilePage(page) {
    const totalPages = Math.ceil(filteredBookings.length / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        renderMobileCards();
    }
}

// Dropdown functions
function toggleDesktopDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    const isHidden = dropdown.classList.contains('hidden');

    // Close all other dropdowns first
    document.querySelectorAll('[id^="desktop-dropdown-"]').forEach(function(otherDropdown) {
        if (otherDropdown.id !== dropdownId) {
            otherDropdown.classList.add('hidden');
        }
    });

    // Toggle current dropdown
    if (isHidden) {
        dropdown.classList.remove('hidden');
    } else {
        dropdown.classList.add('hidden');
    }
}

function closeDesktopDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    dropdown.classList.add('hidden');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const isDropdownButton = event.target.closest('[id^="desktop-menu-button-"]');
    const isDropdownContent = event.target.closest('[id^="desktop-dropdown-"]');

    if (!isDropdownButton && !isDropdownContent) {
        document.querySelectorAll('[id^="desktop-dropdown-"]').forEach(function(dropdown) {
            dropdown.classList.add('hidden');
        });
    }
});
</script>

<?php
require '_footer.php';
?>