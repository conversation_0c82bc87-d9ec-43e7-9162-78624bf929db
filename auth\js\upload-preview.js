/**
 * File Upload Preview Script
 * Enhances the file upload experience with previews and validation
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Upload preview script loaded');

    // File input elements - use querySelectorAll to find all file inputs
    const fileInputs = document.querySelectorAll('input[type="file"]');

    console.log('Found ' + fileInputs.length + ' file inputs');

    // Add event listeners to all file inputs
    fileInputs.forEach(function(input) {
        console.log('Adding listener to file input: ' + input.id);

        // Determine the type based on the input ID or name
        let type = 'unknown';
        if (input.id.includes('ID') || input.id.includes('id') || input.name.includes('ID') || input.name.includes('id')) {
            type = 'id';
        } else if (input.id.includes('Permit') || input.id.includes('permit') || input.name.includes('Permit') || input.name.includes('permit')) {
            type = 'permit';
        }

        // Add change event listener
        input.addEventListener('change', function(e) {
            console.log('File selected for ' + type + ': ' + this.value);

            // Get the file
            const file = this.files[0];
            if (file) {
                console.log('File details: ' + file.name + ' (' + file.size + ' bytes)');

                // Create a preview immediately
                createPreview(file, type, this);

                // Prevent the form from submitting automatically
                const form = this.closest('form');
                if (form) {
                    // Store the original submit handler
                    const originalSubmit = form.onsubmit;

                    // Override the submit handler
                    form.onsubmit = function(e) {
                        // Allow the user to see the preview first
                        if (!confirm('Upload this file: ' + file.name + '?')) {
                            e.preventDefault();
                            return false;
                        }

                        // Call the original handler if it exists
                        if (originalSubmit) {
                            return originalSubmit.call(this, e);
                        }

                        return true;
                    };
                }
            }
        });
    });

    /**
     * Create a preview for the selected file
     * @param {File} file - The selected file
     * @param {string} type - The type of file (id or permit)
     * @param {HTMLInputElement} input - The file input element
     */
    function createPreview(file, type, input) {
        console.log('Creating preview for ' + file.name);

        const parentForm = input.closest('form');
        const parentLabel = input.closest('label');

        // Create a unique preview container ID if it doesn't exist
        let previewContainerId = type === 'id' ? 'id-preview-container' : 'permit-preview-container';
        let previewContainer = document.getElementById(previewContainerId);

        // If the container doesn't exist, create it
        if (!previewContainer) {
            console.log('Creating new preview container: ' + previewContainerId);
            previewContainer = document.createElement('div');
            previewContainer.id = previewContainerId;
            previewContainer.className = 'mt-3';

            // Insert after the label
            if (parentLabel) {
                parentLabel.parentNode.insertBefore(previewContainer, parentLabel.nextSibling);
            } else if (parentForm) {
                // If no label, insert before the submit button
                const submitButton = parentForm.querySelector('button[type="submit"]');
                if (submitButton) {
                    parentForm.insertBefore(previewContainer, submitButton);
                } else {
                    // Append to the form if no submit button
                    parentForm.appendChild(previewContainer);
                }
            }
        }

        // Clear any existing content
        previewContainer.innerHTML = '';

        // Validate file size (max 2MB)
        const maxSize = 2 * 1024 * 1024; // 2MB
        if (file.size > maxSize) {
            showError('File size exceeds 2MB limit', previewContainer);
            if (parentLabel) {
                parentLabel.classList.remove('border-blue-300', 'border-green-500');
                parentLabel.classList.add('border-red-500');
            }
            input.value = ''; // Clear the input
            return;
        }

        // Validate file type
        const validTypes = ['image/jpeg', 'image/png', 'image/svg+xml', 'application/pdf'];
        if (!validTypes.includes(file.type)) {
            showError('Invalid file type. Please upload JPG, PNG, SVG, or PDF', previewContainer);
            if (parentLabel) {
                parentLabel.classList.remove('border-blue-300', 'border-green-500');
                parentLabel.classList.add('border-red-500');
            }
            input.value = ''; // Clear the input
            return;
        }

        // File is valid, update UI
        if (parentLabel) {
            parentLabel.classList.remove('border-blue-300', 'border-red-500');
            parentLabel.classList.add('border-green-500');
        }

        // Create preview element
        const previewElement = document.createElement('div');
        previewElement.className = 'file-preview p-3 bg-green-50 border border-green-200 rounded-lg flex items-center';

        // Create file icon/preview
        if (file.type === 'application/pdf') {
            // PDF icon
            previewElement.innerHTML = `
                <svg class="w-8 h-8 text-green-600 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                </svg>
                <div>
                    <p class="text-sm font-medium text-green-800">${file.name}</p>
                    <p class="text-xs text-green-600">PDF Document (${formatFileSize(file.size)})</p>
                </div>
                <button type="button" class="ml-auto bg-green-100 text-green-700 p-2 rounded-full hover:bg-green-200" onclick="uploadFile(this)">
                    <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                    </svg>
                </button>
            `;

            // Add the preview to the container
            previewContainer.appendChild(previewElement);
        } else {
            // For images, create a preview
            const reader = new FileReader();
            reader.onload = function(e) {
                previewElement.innerHTML = `
                    <img src="${e.target.result}" class="w-12 h-12 object-cover rounded mr-3" alt="Preview">
                    <div>
                        <p class="text-sm font-medium text-green-800">${file.name}</p>
                        <p class="text-xs text-green-600">Image (${formatFileSize(file.size)})</p>
                    </div>
                    <button type="button" class="ml-auto bg-green-100 text-green-700 p-2 rounded-full hover:bg-green-200" onclick="uploadFile(this)">
                        <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
                        </svg>
                    </button>
                `;

                // Add the preview to the container
                previewContainer.appendChild(previewElement);
            };
            reader.readAsDataURL(file);
        }

        console.log('Preview created successfully');
    }

    /**
     * Show error message
     * @param {string} message - The error message to display
     * @param {HTMLElement} container - The container to add the error to
     */
    function showError(message, container) {
        console.log('Showing error: ' + message);

        const errorDiv = document.createElement('div');
        errorDiv.className = 'file-error p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-600 flex items-center';
        errorDiv.innerHTML = `
            <svg class="w-5 h-5 mr-2 flex-shrink-0 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
            </svg>
            ${message}
        `;

        // Clear the container and add the error
        if (container) {
            container.innerHTML = '';
            container.appendChild(errorDiv);
        }
    }

    /**
     * Format file size in human-readable format
     * @param {number} bytes - File size in bytes
     * @returns {string} - Formatted file size
     */
    function formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' bytes';
        else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
        else return (bytes / 1048576).toFixed(1) + ' MB';
    }
});

// Make uploadFile function available globally
window.uploadFile = function(button) {
    const previewElement = button.closest('.file-preview');
    const previewContainer = previewElement.parentNode;
    const form = previewContainer.closest('form');

    if (form) {
        console.log('Submitting form');
        form.submit();
    }
};
