# Deny access to all files in this directory
<FilesMatch ".*">
    Order Allow,<PERSON><PERSON>
    <PERSON> from all
</FilesMatch>

# Specifically deny access to sensitive configuration files
<FilesMatch "^(env\.php|config\.php|security\.php|email\.php)$">
    Order Allow,<PERSON>y
    <PERSON> from all
</FilesMatch>

# Prevent viewing of environment and configuration files
<FilesMatch "\.(env|ini|conf)$">
    Order Allow,<PERSON>y
    <PERSON><PERSON> from all
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Disable PHP execution if this directory is somehow accessed
<FilesMatch "\.php$">
    SetHandler default-handler
</FilesMatch>

# Additional security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
