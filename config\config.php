<?php
/**
 * Application Configuration
 *
 * This file loads configuration from environment variables
 * and provides a centralized configuration array.
 */

// Load environment configuration
require_once __DIR__ . '/env.php';

// Database Configuration
define('DB_HOST', env('DB_HOST', 'localhost'));
define('DB_PORT', env('DB_PORT', '3306'));
define('DB_NAME', env('DB_NAME', 'prod_calaguas'));
define('DB_USER', env('DB_USER', 'root'));
define('DB_PASSWORD', env('DB_PASSWORD', ''));
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATION', 'utf8mb4_unicode_ci');

// Application Configuration
define('APP_ENV', env('APP_ENV', 'development'));
define('APP_DEBUG', env('APP_DEBUG', false));

// Email Configuration
define('EMAIL_FROM_ADDRESS', env('EMAIL_FROM_ADDRESS', '<EMAIL>'));
define('EMAIL_FROM_NAME', env('EMAIL_FROM_NAME', 'Calaguas Booking System'));

// SMTP Configuration
define('SMTP_HOST', env('SMTP_HOST', 'smtp.hostinger.com'));
define('SMTP_PORT', env('SMTP_PORT', '465'));
define('SMTP_USERNAME', env('SMTP_USERNAME', '<EMAIL>'));
define('SMTP_PASSWORD', env('SMTP_PASSWORD', ''));
define('SMTP_ENCRYPTION', env('SMTP_ENCRYPTION', 'ssl'));

// Security Configuration
define('CSRF_TOKEN_EXPIRY', env('CSRF_TOKEN_EXPIRY', 3600));

// Return configuration array for backward compatibility
return [
    'DB_HOST' => DB_HOST,
    'DB_PORT' => DB_PORT,
    'DB_NAME' => DB_NAME,
    'DB_USER' => DB_USER,
    'DB_PASSWORD' => DB_PASSWORD,
    'DB_CHARSET' => DB_CHARSET,
    'DB_COLLATION' => DB_COLLATION,
    'APP_ENV' => APP_ENV,
    'APP_DEBUG' => APP_DEBUG,
    'EMAIL_FROM_ADDRESS' => EMAIL_FROM_ADDRESS,
    'EMAIL_FROM_NAME' => EMAIL_FROM_NAME,
    'SMTP_HOST' => SMTP_HOST,
    'SMTP_PORT' => SMTP_PORT,
    'SMTP_USERNAME' => SMTP_USERNAME,
    'SMTP_PASSWORD' => SMTP_PASSWORD,
    'SMTP_ENCRYPTION' => SMTP_ENCRYPTION,
    'CSRF_TOKEN_EXPIRY' => CSRF_TOKEN_EXPIRY,
];
