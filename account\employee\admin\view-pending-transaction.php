<?php
ob_start();
require '_header.php';

$getBookingId = isset($_GET['id']) ? (int) $_GET['id'] : 0;

$bookingDetails = getBookingDetails2($pdo, $getBookingId);

if (!empty($bookingDetails)) {
    $getId = $_SESSION['id'] ?? null; // Define $id properly

    if ($bookingDetails['mtho'] === "Waiting" || $bookingDetails['mtho'] === "Approved") {
        header("Location: transaction-pending.php");
        exit;
    } else {
        $extname = $bookingDetails['extname'] ?? '';
        $middlename = $bookingDetails['middlename'] ?? '';
        $operatorName = trim($bookingDetails['firstname'] . ' ' . $middlename . ' ' . $bookingDetails['lastname'] . ' ' . $extname);
        $designationTour = $bookingDetails['tour_operator_designation'];
        $resortName = $bookingDetails['resort_operator_designation'];
        $referenceNumber = $bookingDetails['referenceNum'];
        $boatName = $bookingDetails['boatName'];
        $portName = $bookingDetails['portName'];
        $checkIn = $bookingDetails['check_in_date'];
        $checkout = $bookingDetails['check_out_date'];
        $adultCount = $bookingDetails['total_adults'];
        $childrenCount = $bookingDetails['total_children'];
        $crewCount = $bookingDetails['total_crew'];
        $orNum = $bookingDetails['or_num'];
        $receipt = $bookingDetails['receipt_image'];
        $voucherUse = $bookingDetails['voucher_use'];
        $treasurer = $bookingDetails['treasurer'] ?? '';
        $mtho = $bookingDetails['mtho'] ?? '';

        // Format dates for display
        $formattedCheckIn = (new DateTime($checkIn))->format('F d, Y');
        $formattedCheckout = (new DateTime($checkout))->format('F d, Y');

        // Create voucher badge
        if ($voucherUse === "Yes") {
            $textColor = "text-green-600";
            $voucherBadge = '<span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">Yes</span>';
        } else {
            $textColor = "text-red-600";
            $voucherBadge = '<span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">No</span>';
        }
    }
}

ob_end_flush();
?>

<div class="p-4 sm:ml-64">
    <div class="border p-4 rounded-lg shadow-lg bg-white mt-14">
        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <a href="transaction-pending.php" class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2">Pending Transactions</a>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Transaction Details</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-blue-100 p-2 rounded-lg mr-3">
                <i class="fas fa-file-invoice text-blue-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-xl font-bold text-gray-900">Transaction Details</h2>
                <p class="text-sm text-gray-600">Review booking information before approval</p>
            </div>
        </div>

        <!-- Reference Number Display -->
        <div class="mt-4 bg-gray-50 p-3 rounded-lg border border-gray-200 inline-flex items-center">
            <span class="text-sm font-medium text-gray-600">Reference Number:</span>
            <span class="text-lg font-semibold text-blue-700 ml-2"><?= $referenceNumber; ?></span>
        </div>
        <!-- Booking Information Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <!-- Tour Operator Section -->
            <div class="bg-white border border-blue-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-semibold text-blue-700">Tour Operator</h3>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <i class="fas fa-user-tie text-blue-600"></i>
                    </div>
                </div>
                <div class="mt-3 space-y-2">
                    <div>
                        <p class="text-xs text-gray-500">Name</p>
                        <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($operatorName); ?></p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">Designation</p>
                        <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($designationTour); ?></p>
                    </div>
                </div>
            </div>

            <!-- Resort & Boat Section -->
            <div class="bg-white border border-green-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-semibold text-green-700">Destination</h3>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-hotel text-green-600"></i>
                    </div>
                </div>
                <div class="mt-3 space-y-2">
                    <div>
                        <p class="text-xs text-gray-500">Resort</p>
                        <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($resortName); ?></p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">Boat</p>
                        <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($boatName); ?></p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">Port</p>
                        <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($portName); ?></p>
                    </div>
                </div>
            </div>

            <!-- Trip Details Section -->
            <div class="bg-white border border-purple-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-semibold text-purple-700">Trip Details</h3>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <i class="fas fa-calendar-alt text-purple-600"></i>
                    </div>
                </div>
                <div class="mt-3 space-y-2">
                    <div>
                        <p class="text-xs text-gray-500">Check-in Date</p>
                        <p class="text-sm font-medium text-gray-800"><?= $formattedCheckIn; ?></p>
                    </div>
                    <div>
                        <p class="text-xs text-gray-500">Check-out Date</p>
                        <p class="text-sm font-medium text-gray-800"><?= $formattedCheckout; ?></p>
                    </div>
                    <div class="flex items-center">
                        <p class="text-xs text-gray-500 mr-2">Voucher Use:</p>
                        <?= $voucherBadge; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Passenger Count Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <!-- Adults Section -->
            <div class="bg-white border border-blue-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-semibold text-blue-700">Adults</h3>
                        <p class="text-xs text-gray-500 mt-1">Age 9 and above</p>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <i class="fas fa-user text-blue-600"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <p class="text-2xl font-bold text-blue-700"><?= $adultCount; ?></p>
                </div>
            </div>

            <!-- Children Section -->
            <div class="bg-white border border-green-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-semibold text-green-700">Children</h3>
                        <p class="text-xs text-gray-500 mt-1">Below age 9</p>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-child text-green-600"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <p class="text-2xl font-bold text-green-700"><?= $childrenCount; ?></p>
                </div>
            </div>

            <!-- Crew Section -->
            <div class="bg-white border border-purple-200 rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-semibold text-purple-700">Crew</h3>
                        <p class="text-xs text-gray-500 mt-1">Boat crew members</p>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <i class="fas fa-ship text-purple-600"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <p class="text-2xl font-bold text-purple-700"><?= $crewCount; ?></p>
                </div>
            </div>
        </div>

        <?php if (!empty($orNum)): ?>
        <div class="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center justify-between">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-2 rounded-lg mr-3">
                        <i class="fas fa-file-invoice-dollar text-blue-600"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">O.R. Number</p>
                        <p class="text-base font-medium text-gray-800 mt-1"><?= htmlspecialchars($orNum); ?></p>
                    </div>
                </div>

                <?php if (!empty($receipt)): ?>
                <div class="mt-3 sm:mt-0">
                    <a href="../../operator/receipt/<?= $receipt; ?>" class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium" target="_blank" rel="noopener noreferrer">
                        <i class="fas fa-receipt mr-2"></i> View Receipt
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
        </div>

        <hr class="my-6">

        <!-- Passenger List Section -->
        <div class="mt-6">
            <div class="flex items-center mb-4">
                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                    <i class="fas fa-list-ul text-blue-600 text-lg"></i>
                </div>
                <h3 class="text-md font-semibold text-gray-700">Passenger List</h3>
            </div>

            <!-- Enhanced Passenger Table -->
            <div class="overflow-x-auto mt-4">
                <table id="search-table" class="min-w-full bg-white border border-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php
                    try {
                        // SQL query to fetch booking details
                        $sql = "SELECT *
                                FROM cb_tourists
                                WHERE booking_id = :booking_id
                                ORDER BY
                                    CASE
                                        WHEN info_type = 'crewTts' THEN 1
                                        WHEN info_type = 'crewMbca' THEN 2
                                        ELSE 3
                                    END ASC,
                                    full_name ASC";
                        // Prepare and execute the statement
                        $stmt = $pdo->prepare($sql);
                        $stmt->bindParam(':booking_id', $getBookingId, PDO::PARAM_STR);
                        $stmt->execute();

                        // Fetch the results
                        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        if ($rows) {
                            foreach ($rows as $row) {

                                if ($row['info_type'] == "tourist") {
                                    $infoTypeBadge = '
                                    <span class="bg-gradient-to-r from-green-100 to-green-50 text-green-800 text-xs font-medium me-2 px-3 py-1 rounded-full border border-green-200 shadow-sm">Tourist</span>
                                    ';
                                } elseif ($row['info_type'] == "crewTts") {
                                    $infoTypeBadge = '
                                    <span class="bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 text-xs font-medium me-2 px-3 py-1 rounded-full border border-blue-200 shadow-sm">Crew TTS</span>
                                    ';
                                } else {
                                    $infoTypeBadge = '
                                    <span class="bg-gradient-to-r from-red-100 to-red-50 text-red-800 text-xs font-medium me-2 px-3 py-1 rounded-full border border-red-200 shadow-sm">Crew MBCA</span>
                                    ';
                                }
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 text-sm font-medium text-gray-900"><?= $infoTypeBadge; ?></td>
                                    <td class="px-6 py-4 text-sm text-gray-500"><?= htmlspecialchars($row['full_name']); ?></td>
                                    <td class="px-6 py-4 text-sm text-center">
                                        <button data-modal-target="view-passenger-modal-<?= $row['tourist_id']; ?>" data-modal-toggle="view-passenger-modal-<?= $row['tourist_id']; ?>" type="button" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg text-xs px-3 py-1.5 focus:outline-none focus:ring-4 focus:ring-blue-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                                <!-- View Passenger Modal -->
                                <div id="view-passenger-modal-<?= $row['tourist_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-md max-h-full">
                                        <!-- Modal content -->
                                        <div class="relative bg-white rounded-lg shadow-lg">
                                            <!-- Modal header -->
                                            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t">
                                                <?php
                                                $iconClass = "fas fa-user";
                                                $bgColor = "bg-green-100";
                                                $textColor = "text-green-700";
                                                $title = "Tourist";

                                                if ($row['info_type'] == "crewTts") {
                                                    $iconClass = "fas fa-user-tie";
                                                    $bgColor = "bg-blue-100";
                                                    $textColor = "text-blue-700";
                                                    $title = "Crew TTS";
                                                } elseif ($row['info_type'] == "crewMbca") {
                                                    $iconClass = "fas fa-user-shield";
                                                    $bgColor = "bg-red-100";
                                                    $textColor = "text-red-700";
                                                    $title = "Crew MBCA";
                                                }
                                                ?>
                                                <div class="flex items-center">
                                                    <div class="<?= $bgColor ?> <?= $textColor ?> p-2 rounded-full mr-3">
                                                        <i class="<?= $iconClass ?>"></i>
                                                    </div>
                                                    <h3 class="text-lg font-semibold text-gray-900">
                                                        <?= $title ?> Information
                                                    </h3>
                                                </div>
                                                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 inline-flex justify-center items-center" data-modal-hide="view-passenger-modal-<?= $row['tourist_id']; ?>">
                                                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                    </svg>
                                                    <span class="sr-only">Close modal</span>
                                                </button>
                                            </div>

                                            <!-- Modal Body -->
                                            <div class="p-4 md:p-5">
                                                <!-- Passenger Type Badge -->
                                                <div class="mb-4 flex justify-center">
                                                    <?php if ($row['info_type'] == "tourist"): ?>
                                                        <span class="inline-flex items-center bg-green-100 text-green-800 text-xs font-medium px-4 py-1.5 rounded-full">
                                                            <i class="fas fa-user mr-2"></i> Tourist
                                                        </span>
                                                    <?php elseif ($row['info_type'] == "crewTts"): ?>
                                                        <span class="inline-flex items-center bg-blue-100 text-blue-800 text-xs font-medium px-4 py-1.5 rounded-full">
                                                            <i class="fas fa-user-tie mr-2"></i> Crew TTS
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="inline-flex items-center bg-red-100 text-red-800 text-xs font-medium px-4 py-1.5 rounded-full">
                                                            <i class="fas fa-user-shield mr-2"></i> Crew MBCA
                                                        </span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Name and Basic Info -->
                                                <div class="text-center mb-4">
                                                    <h4 class="text-xl font-semibold text-gray-900"><?= htmlspecialchars($row['full_name']); ?></h4>
                                                    <div class="flex justify-center items-center mt-1 space-x-2">
                                                        <span class="inline-flex items-center text-sm">
                                                            <?php if (strtolower($row['gender']) == 'male'): ?>
                                                                <i class="fas fa-mars text-blue-500 mr-1"></i> Male
                                                            <?php else: ?>
                                                                <i class="fas fa-venus text-pink-500 mr-1"></i> Female
                                                            <?php endif; ?>
                                                        </span>
                                                        <span class="text-gray-500">•</span>
                                                        <span class="text-sm text-gray-600"><?= htmlspecialchars($row['age']); ?> years old</span>
                                                    </div>
                                                </div>

                                                <!-- Information Cards -->
                                                <div class="grid grid-cols-1 gap-3">
                                                    <!-- Contact Information -->
                                                    <div class="bg-gray-50 rounded-lg p-3">
                                                        <div class="flex items-center mb-2">
                                                            <i class="fas fa-address-card text-blue-600 mr-2"></i>
                                                            <h5 class="text-sm font-semibold text-gray-700">Contact Information</h5>
                                                        </div>
                                                        <div class="space-y-2 pl-6">
                                                            <div class="flex items-start">
                                                                <i class="fas fa-phone text-gray-500 mr-2 mt-0.5"></i>
                                                                <div>
                                                                    <p class="text-xs text-gray-500">Phone Number</p>
                                                                    <?php if (!empty($row['contact_number'])): ?>
                                                                        <a href="tel:<?= htmlspecialchars($row['contact_number']); ?>" class="text-sm text-blue-600 hover:underline">
                                                                            <?= htmlspecialchars($row['contact_number']); ?>
                                                                        </a>
                                                                    <?php else: ?>
                                                                        <p class="text-sm text-gray-500 italic">Not provided</p>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                            <div class="flex items-start">
                                                                <i class="fas fa-map-marker-alt text-gray-500 mr-2 mt-0.5"></i>
                                                                <div>
                                                                    <p class="text-xs text-gray-500">Address</p>
                                                                    <?php if (!empty($row['address'])): ?>
                                                                        <p class="text-sm text-gray-700">
                                                                            <?= htmlspecialchars($row['demographic'] . ', ' . $row['address']); ?>
                                                                        </p>
                                                                    <?php else: ?>
                                                                        <p class="text-sm text-gray-500 italic">Not provided</p>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Footer -->
                                                <div class="flex justify-end pt-4 mt-4 border-t border-gray-200">
                                                    <button data-modal-hide="view-passenger-modal-<?= $row['tourist_id']; ?>" type="button" class="text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                                                        Close
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="3" class="px-6 py-4 text-center text-sm text-red-600">
                                Error: <?= htmlspecialchars($e->getMessage()); ?>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
        <hr class="my-6">
        <!-- Action Buttons -->
        <div class="mt-6 flex justify-end items-center">
            <button data-modal-target="decline-modal" data-modal-toggle="decline-modal" class="inline-flex items-center justify-center space-x-2 px-5 py-2.5 mr-3 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-4 focus:ring-red-300 shadow-sm hover:shadow-md transition-all duration-300" type="button">
                <i class="fas fa-times-circle mr-2"></i>
                <span>Decline Transaction</span>
            </button>
            <button data-modal-target="approve-modal" data-modal-toggle="approve-modal" class="inline-flex items-center justify-center space-x-2 px-5 py-2.5 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-300 shadow-sm hover:shadow-md transition-all duration-300" type="button">
                <i class="fas fa-check-circle mr-2"></i>
                <span>Approve Transaction</span>
            </button>
        </div>

        <!-- Decline Modal -->
        <div id="decline-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-md max-h-full">
                <!-- Modal content -->
                <div class="relative bg-white rounded-lg shadow-lg">
                    <!-- Modal header -->
                    <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t">
                        <div class="flex items-center">
                            <div class="bg-red-100 text-red-700 p-2 rounded-full mr-3">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">
                                Decline Transaction
                            </h3>
                        </div>
                        <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 inline-flex justify-center items-center" data-modal-hide="decline-modal">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>

                    <form action="inc/inc.booking.php" method="POST">
                        <div class="p-4 md:p-5">
                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                            <input type="hidden" value="<?= $getBookingId; ?>" name="booking_id">
                            <input type="hidden" value="Declined" name="mthoResponse">
                            <input type="hidden" value="<?= $referenceNumber; ?>" name="referenceNumber">
                            <input type="hidden" value="0" name="ctrlNum">

                            <!-- Warning Message -->
                            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 border-l-4 border-red-500" role="alert">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-circle text-red-600 mr-2 text-lg"></i>
                                    <div>
                                        <span class="font-medium">Warning!</span>
                                        <p class="mt-1">Are you sure you want to decline this transaction? This action cannot be undone.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Transaction Info Card -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                <div class="flex items-center mb-3">
                                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                    <h4 class="text-sm font-semibold text-gray-700">Transaction Details</h4>
                                </div>

                                <div class="space-y-3 pl-6">
                                    <div class="flex items-start">
                                        <i class="fas fa-hashtag text-gray-500 mr-2 mt-0.5 w-4 text-center"></i>
                                        <div>
                                            <p class="text-xs text-gray-500">Reference Number</p>
                                            <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($referenceNumber); ?></p>
                                        </div>
                                    </div>

                                    <div class="flex items-start">
                                        <i class="fas fa-user-tie text-gray-500 mr-2 mt-0.5 w-4 text-center"></i>
                                        <div>
                                            <p class="text-xs text-gray-500">Tour Operator</p>
                                            <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($operatorName); ?></p>
                                        </div>
                                    </div>

                                    <div class="flex items-start">
                                        <i class="fas fa-hotel text-gray-500 mr-2 mt-0.5 w-4 text-center"></i>
                                        <div>
                                            <p class="text-xs text-gray-500">Resort</p>
                                            <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($resortName); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Buttons -->
                            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                                <button data-modal-hide="decline-modal" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100">
                                    Cancel
                                </button>
                                <button type="submit" name="mthoResponseBtn" class="text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                                    Decline Transaction
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- Approve Modal -->
        <div id="approve-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-md max-h-full">
                <!-- Modal content -->
                <div class="relative bg-white rounded-lg shadow-lg">
                    <!-- Modal header -->
                    <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t">
                        <div class="flex items-center">
                            <div class="bg-green-100 text-green-700 p-2 rounded-full mr-3">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">
                                Approve Transaction
                            </h3>
                        </div>
                        <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 inline-flex justify-center items-center" data-modal-hide="approve-modal">
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>

                    <form action="inc/inc.booking.php" method="POST">
                        <div class="p-4 md:p-5">
                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                            <input type="hidden" value="<?= $getBookingId; ?>" name="booking_id">
                            <input type="hidden" value="Approved" name="mthoResponse">
                            <input type="hidden" value="<?= $referenceNumber; ?>" name="referenceNumber">

                            <!-- Success Message -->
                            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 border-l-4 border-green-500" role="alert">
                                <div class="flex items-center">
                                    <i class="fas fa-info-circle text-green-600 mr-2 text-lg"></i>
                                    <div>
                                        <span class="font-medium">Ready to approve!</span>
                                        <p class="mt-1">Please enter a control number to proceed with this transaction approval.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Transaction Info Card -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                <div class="flex items-center mb-3">
                                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                    <h4 class="text-sm font-semibold text-gray-700">Transaction Details</h4>
                                </div>

                                <div class="space-y-3 pl-6">
                                    <div class="flex items-start">
                                        <i class="fas fa-hashtag text-gray-500 mr-2 mt-0.5 w-4 text-center"></i>
                                        <div>
                                            <p class="text-xs text-gray-500">Reference Number</p>
                                            <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($referenceNumber); ?></p>
                                        </div>
                                    </div>

                                    <div class="flex items-start">
                                        <i class="fas fa-user-tie text-gray-500 mr-2 mt-0.5 w-4 text-center"></i>
                                        <div>
                                            <p class="text-xs text-gray-500">Tour Operator</p>
                                            <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($operatorName); ?></p>
                                        </div>
                                    </div>

                                    <div class="flex items-start">
                                        <i class="fas fa-hotel text-gray-500 mr-2 mt-0.5 w-4 text-center"></i>
                                        <div>
                                            <p class="text-xs text-gray-500">Resort</p>
                                            <p class="text-sm font-medium text-gray-800"><?= htmlspecialchars($resortName); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Control Number Input -->
                            <div class="mb-4">
                                <label for="ctrlNum" class="block mb-2 text-sm font-medium text-gray-900">Control Number</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none">
                                        <i class="fas fa-hashtag text-gray-400"></i>
                                    </div>
                                    <input type="text" id="ctrlNum" name="ctrlNum" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-green-500 focus:border-green-500 block w-full ps-10 p-2.5" placeholder="Enter control number" required>
                                </div>
                                <p class="mt-2 text-xs text-gray-500">
                                    <i class="fas fa-info-circle mr-1"></i> This control number will be used for tracking purposes
                                </p>
                            </div>

                            <!-- Form Buttons -->
                            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                                <button data-modal-hide="approve-modal" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:ring-4 focus:ring-gray-100">
                                    Cancel
                                </button>
                                <button type="submit" name="mthoResponseBtn" class="text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                                    Approve Transaction
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


<?php
require '_footer.php';
?>