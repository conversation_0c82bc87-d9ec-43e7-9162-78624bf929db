<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);
include '../../../../connection/dbconnect.php';
if ($_SESSION['loginStatus'] != "isLogin") {
    header("Location: ../logout.php");
    exit;
} else {

    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (isset($_POST['boatResponseBtn'])) {
            try {

                if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                    throw new Exception("Invalid CSRF token.");
                }
                if (!isset($_SESSION['id'])) {
                    throw new Exception("User not authenticated");
                }

                // Validate Booking ID
                $getBookingId = $_POST['booking_id'] ?? null;
                if (!$getBookingId) {
                    throw new Exception("Invalid booking ID.");
                }

                $boatResponse = trim($_POST['boatResponse']);
                $referenceNumber = trim($_POST['referenceNumber']);

                if ($boatResponse === "Approved") {
                    header("Location: ../add-passenger.php?id=" . $getBookingId);
                    exit();
                } else {
                    // Start transaction
                    $pdo->beginTransaction();


                    $stmt = $pdo->prepare("UPDATE cb_booking_approvals SET boat = :boatResponse WHERE booking_id = :booking_id");
                    $stmt->execute([
                        ':boatResponse' => $boatResponse,
                        ':booking_id' => $getBookingId
                    ]);

                    // Insert log entry
                    $type = "Booking - " . $boatResponse;
                    $description = "Boat operator: " . $_SESSION['username'] . " Declined a booking: " . $referenceNumber  . ". Date Created: " . date("Y-m-d");
                    $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
                    $stmt->execute([
                        ':type' => $type,
                        ':description' => $description
                    ]);


                    // Commit transaction
                    $pdo->commit();

                    $_SESSION['success'] = $boatResponse . " successfully";
                    header("Location: ../transaction-approved.php");
                    exit();
                }
            } catch (Exception $e) {
                $pdo->rollBack();
                $_SESSION['error'] = $e->getMessage();
                header("Location: ../transaction-pending.php");
                exit();
            }
        }
    }
}
