<?php
include '../../../../connection/dbconnect.php';
include '../../../assets/dompdf/autoload.inc.php';
include '../inc/inc.function.php';


use Dompdf\Dompdf;
use Dompdf\Options;

$options = new Options();
$options->set('chroot', realpath('img/'));

$dompdf = new Dompdf($options);

$getBookingId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
$statusBooking = "approved";
$dateCreated = date("F j, Y", strtotime(date("Y-m-d")));
$yearNow = date("Y");

if (empty($getBookingId)) {
    header("Location: transaction-approved.php");
    exit;
}

$bookingDetails = getBookingDetails($pdo, $getBookingId);
if ($bookingDetails['booking_status'] != $statusBooking) {
    header("Location: ../transaction-approved.php");
    exit;
} else {
    if ($bookingDetails !== null) {
        $extname = $bookingDetails['extname'] ?? ''; // Use null coalescing operator for defaults
        $middlename = $bookingDetails['middlename'] ?? '';
        $operatorName = trim($bookingDetails['firstname'] . ' ' . $middlename . ' ' . $bookingDetails['lastname'] . ' ' . $extname);
        $designationTour = $bookingDetails['tour_operator_designation'];
        $resortName = $bookingDetails['resort_operator_designation'];
        $boatName = $bookingDetails['boatName'];
        $referenceNumber = $bookingDetails['referenceNum'];
        $controlNumber = $bookingDetails['control_number'];
        $orNumber = $bookingDetails['or_num'];
        $checkin = date("M. j, Y", strtotime($bookingDetails['check_in_date']));
        $checkout = date("M. j, Y", strtotime($bookingDetails['check_out_date']));

        // Pax Count
        $paxCount = $bookingDetails['total_adults'] + $bookingDetails['total_children'];
        $loginStatus = $bookingDetails['login_status'];


        if ($loginStatus != "isLogin") {
            header("Location: ../logout.php");
            exit;
        }
    }
}


$html = '
<div style="background-image: url(img/watermark3.jpg);">
    <table style="width:100%;">
        <tr>
            <td style="text-align: center;">
                <img src="img/Logo.png" class="logo" style="height: 75px;">
            </td>
            <td style="text-align: center;">
                <p>
                    Republic of the Philippines <br>
                    Region V - Bicol <br>
                    Province of Camarines Norte <br>
                    Municipality of Vinzons
                </p>
            </td>
            <td style="text-align: center;">
                <img src="img/Logo2.png" class="logo2" style="height: 75px;">
            </td>
        </tr>
        <tr>
            <td colspan="3" style="text-align: center;">
                <b>PASIYO SA VINZONS - MUNICIPAL TOURISM AND HERITAGE OPERATIONS</b>
            </td>
        </tr>
        <tr>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="3" style="text-align: center;">
                <div style="background-color: red; padding:10px 0 10px 0;">
                    <b style="font-size: 30px; color:white;">VINZONS TOURISM PASS ' . $yearNow . '</b>
                </div>
            </td>
        </tr>
    </table>
    <br>
    <table style="width:100%;">
        <tr>
            <td>Date Issued:</td>
            <td style="color:blue;"><b>' . $checkin . '</b></td>
            <td>Control No:</td>
            <td style="color:blue;"><b>' . $controlNumber . '</b></td>
        </tr>
        <tr>
            <td>Valid Until:</td>
            <td style="color:blue;"><b>' . $checkout . '</b></td>
            <td>Accomodation:</td>
            <td style="color:blue;"><b>' . $resortName . '</b></td>
        </tr>
        <tr>
            <td>Boat Name:</td>
            <td style="color:blue;"><b>' . $boatName . '</b></td>
            <td>TDF OR No.:</td>
            <td style="color:blue;"><b>' . $orNumber . '</b></td>
        </tr>
        <tr>
            <td>Tour Guide:</td>
            <td style="color:blue;"><b>' . $operatorName . '</b></td>
            <td>Travel Agency:</td>
            <td style="color:blue;"><b>' . $designationTour . '</b></td>
        </tr>
    </table>
    <div style="text-indent: 40px; text-align: justify; font-size:18px;">
        <p>The &nbsp;&nbsp;<b style="color:blue;"><u>&nbsp;&nbsp;&nbsp;' . $paxCount . '&nbsp;&nbsp;&nbsp;</u></b>&nbsp;&nbsp; persons indicated in the boat manifest attached to this have coordinated with the <b>Local
                Government Unit of Vinzons </b> and have complied with all the requirements to access Mahabang Buhangin tourism site.</p>
        </p>
        <p>This Travel Pass shall not exempt the tourists from the observance of the <b>STANDARD HEALTH PROTOCOLS</b> under the new normal.</p>
        <p>This shall also serve as an undertaking to only transact with <b>DOT Accredited Tourism Establishment/Enterprises.</b></p>
    </div>
    <br>
    <p>Checked, Attested and Approved by:</p>
    <br>
    <table style="width:100%;">
        <tr>
            <td style="text-align: center;">
                <img src="img/approved.png" alt="" style="height: 30px;">
            </td>
            <td style="text-align: center;">
                <img src="img/approved.png" alt="" style="height: 30px;">
            </td>
        </tr>
        <tr>
            <td style="text-align: center;">
                _____________________________________
            </td>
            <td style="text-align: center;">
                ___________________________________
            </td>
        </tr>
        <tr>
            <td style="text-align: center;">
                Municipal Treasurer`s Office
            </td>
            <td style="text-align: center;">
                Municipal Tourism and Heritage Operations
            </td>
        </tr>
        <tr>
            <td colspan="2" style="text-align: center;"></td>
        </tr>
    </table>
    <br>
    <br>
    <table style="width:100%;">
        <tr>
            <td style="text-align: center;">
                __________________________
            </td>
        </tr>
        <tr>
            <td style="text-align: center;">
                Philippine Coast Guard
            </td>
        </tr>
    </table>
    <p style="font-size:8px;">
        This a computer generated document. <br>
        No signature required for MTO and MTHO. <br>
        <b>DO NOT COPY</b><br>
        Date Created:' . $dateCreated . '
    </p>
</div>';

$dompdf->loadHtml($html);

$dompdf->setPaper('A4', 'portrait');

$dompdf->render();

$filename = $controlNumber . '-tourism-pass';
$dompdf->stream($filename, array("Attachment" => true));
