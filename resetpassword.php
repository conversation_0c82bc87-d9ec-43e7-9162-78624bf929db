<?php
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);

include 'connection/dbconnect.php';

// Redirect if already logged in
if (isset($_SESSION['id'], $_SESSION['accountType'])) {
    $accountType = $_SESSION['accountType'];
    $operator = $_SESSION['operator'] ?? null;
    
    if ($accountType === 'admin') {
        header('Location: account/employee/admin/home.php');
        exit();
    } elseif ($accountType === 'treasurer') {
        header('Location: account/employee/treasurer/home.php');
        exit();
    } elseif ($accountType === 'user') {
        if ($operator === "Tour operator") {
            header('Location: account/operator/tour/home.php');
            exit();
        } elseif ($operator === "Resort operator") {
            header('Location: account/operator/resort/home.php');
            exit();
        } elseif ($operator === "Boat operator") {
            header('Location: account/operator/boat/home.php');
            exit();
        }
    }
}

// Get token from URL
$token = $_GET['token'] ?? '';
$validToken = false;
$user = null;

if ($token) {
    try {
        // Hash the token to compare with database
        $hashedToken = hash('sha256', $token);
        
        // Check if token exists and is not expired
        $stmt = $pdo->prepare("SELECT id, username, email, accountExpired FROM operator_account WHERE rememberToken = :token AND accountStatus = 'Activated'");
        $stmt->execute([':token' => $hashedToken]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            // Check if token has expired
            $expiry = strtotime($user['accountExpired']);
            if ($expiry && $expiry > time()) {
                $validToken = true;
            }
        }
    } catch (PDOException $e) {
        error_log("Database error in reset password: " . $e->getMessage());
    }
}

// Generate a CSRF token if it doesn't exist or has expired
if (empty($_SESSION['csrf_token']) || time() > ($_SESSION['csrf_token_expiration'] ?? 0)) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    $_SESSION['csrf_token_expiration'] = time() + 3600; // 1-hour validity
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calaguas Booking | Reset Password</title>
    <link rel="icon" type="image/x-icon" href="public/img/Logo.png" />
    <script src="https://kit.fontawesome.com/1ee5dd1d52.js" crossorigin="anonymous"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
</head>

<body class="bg-gray-50 min-h-screen flex flex-col">
    <div class="flex flex-col items-center justify-center flex-grow py-4">
        <div class="w-full max-w-sm p-6 bg-white rounded-lg shadow-lg">
            <div class="text-center mb-6">
                <img src="components/img/Logo.png" alt="VNLogo" class="w-32 mx-auto mb-4" draggable="false" />
                <h2 class="text-xl font-bold text-gray-800">Reset Password</h2>
                <?php if ($validToken): ?>
                    <p class="text-gray-600 mt-1 text-sm">Enter your new password for <strong><?= htmlspecialchars($user['username'], ENT_QUOTES, 'UTF-8'); ?></strong></p>
                <?php else: ?>
                    <p class="text-gray-600 mt-1 text-sm">Invalid or expired reset link</p>
                <?php endif; ?>
            </div>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <strong>Success:</strong> <?= htmlspecialchars($_SESSION['success'], ENT_QUOTES, 'UTF-8'); ?>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <strong>Error:</strong> <?= htmlspecialchars($_SESSION['error'], ENT_QUOTES, 'UTF-8'); ?>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <?php if ($validToken): ?>
                <form action="inc.resetpassword.php" method="POST" class="space-y-4">
                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                    <input type="hidden" name="token" value="<?= htmlspecialchars($token, ENT_QUOTES, 'UTF-8'); ?>">
                    
                    <div>
                        <label for="password" class="block mb-2 text-sm font-medium text-gray-900">New Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input
                                type="password"
                                name="password"
                                id="password"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-9 p-2"
                                placeholder="Enter new password"
                                required
                                minlength="8"
                                maxlength="200"
                            />
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Password must be at least 8 characters long</p>
                    </div>

                    <div>
                        <label for="confirm_password" class="block mb-2 text-sm font-medium text-gray-900">Confirm New Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input
                                type="password"
                                name="confirm_password"
                                id="confirm_password"
                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-9 p-2"
                                placeholder="Confirm new password"
                                required
                                minlength="8"
                                maxlength="200"
                            />
                        </div>
                    </div>

                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            id="showPassword"
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label for="showPassword" class="ms-2 text-sm font-medium text-gray-700">Show Passwords</label>
                    </div>

                    <button
                        type="submit"
                        class="w-full text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center transition-colors duration-200"
                    >
                        <i class="fas fa-save mr-2"></i>Update Password
                    </button>
                </form>
            <?php else: ?>
                <div class="text-center">
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Invalid Link:</strong> This password reset link is invalid or has expired.
                    </div>
                    <p class="text-sm text-gray-600 mb-4">
                        Password reset links expire after 1 hour for security reasons.
                    </p>
                    <a href="forgotpassword.php" class="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-redo mr-2"></i>Request New Reset Link
                    </a>
                </div>
            <?php endif; ?>

            <div class="text-center mt-6">
                <p class="text-sm text-gray-600">
                    Remember your password? 
                    <a href="login.php" class="font-medium text-blue-600 hover:underline">Back to Login</a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    
    <script>
        // Show/hide password functionality
        document.getElementById('showPassword').addEventListener('change', function() {
            const passwordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('confirm_password');
            
            if (this.checked) {
                passwordField.type = 'text';
                confirmPasswordField.type = 'text';
            } else {
                passwordField.type = 'password';
                confirmPasswordField.type = 'password';
            }
        });

        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.bg-green-100, .bg-red-100');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>

</html>
