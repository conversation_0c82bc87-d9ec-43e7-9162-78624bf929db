<?php
ob_start();
require '_header.php';
if ($accType === "mtho staff") {
    header("Location: home.php");
    exit;
}
ob_end_flush();
?>
<div class="p-4 sm:ml-64">
    <div class="border p-4 rounded-lg shadow-lg bg-white mt-14">
        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">User Account</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Operator</span>
                    </div>
                </li>
            </ol>
        </nav>

        <h2 class="text-2xl font-bold text-gray-900 mt-6">Operator Accounts</h2>
        <p class="text-sm text-gray-600 mt-2">Oversee and manage operator information and roles efficiently</p>

        <!-- Tabs -->
        <div class="mb-4 border-b border-gray-200 mt-4">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="default-styled-tab" data-tabs-toggle="#default-styled-tab-content" data-tabs-active-classes="text-blue-600 hover:text-blue-600 border-blue-600 font-semibold" data-tabs-inactive-classes="text-gray-500 hover:text-gray-600 border-gray-100 hover:border-gray-300" role="tablist">
                <li class="me-2" role="presentation">
                    <button class="inline-flex items-center p-4 border-b-2 rounded-t-lg" id="profile-styled-tab" data-tabs-target="#styled-profile" type="button" role="tab" aria-controls="profile" aria-selected="false">
                        <i class="fas fa-user-clock mr-2"></i>
                        Unregistered
                    </button>
                </li>
                <li class="me-2" role="presentation">
                    <button class="inline-flex items-center p-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300" id="dashboard-styled-tab" data-tabs-target="#styled-dashboard" type="button" role="tab" aria-controls="dashboard" aria-selected="false">
                        <i class="fas fa-user-check mr-2"></i>
                        Registered
                    </button>
                </li>
            </ul>
        </div>

        <!-- Tab Content -->
        <div id="default-styled-tab-content">
            <!-- Unregistered Tab -->
            <div class="hidden p-4 rounded-lg" id="styled-profile" role="tabpanel" aria-labelledby="profile-tab">
                <div class="overflow-x-auto mt-4 rounded-lg">
                    <table id="unregistered-table" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-responsive">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Username</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Full Name</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Email</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Operator Type</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Status</th>
                                <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php
                            try {
                                $fetchAccountType = "user";
                                $fetchAccountStatus = "pending";
                                $sql = "SELECT
                                        i.firstname, i.middlename, i.lastname, i.extname, i.operatorType,
                                        a.accType, a.accountStatus, a.email, a.username, a.id, a.rememberToken
                                    FROM
                                        operator_info i
                                    INNER JOIN
                                        operator_account a
                                    ON
                                        i.user_id = a.id
                                    WHERE
                                        a.accType = :accType
                                    AND
                                        a.accountStatus = :accStatus";

                                $stmt = $pdo->prepare($sql);
                                $stmt->bindParam(':accType', $fetchAccountType, PDO::PARAM_STR);
                                $stmt->bindParam(':accStatus', $fetchAccountStatus, PDO::PARAM_STR);
                                $stmt->execute();

                                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                if ($rows) {
                                    foreach ($rows as $row) {
                                        $fullname = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                            ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($row['username']); ?></td>
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500"><?= htmlspecialchars($fullname); ?></td>
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500"><?= htmlspecialchars($row['email']); ?></td>
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500"><?= htmlspecialchars($row['operatorType']); ?></td>
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500">
                                                <span class="inline-flex items-center bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                                    <span class="w-2 h-2 me-1 bg-yellow-500 rounded-full"></span>
                                                    Pending
                                                </span>
                                            </td>
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-center">
                                                <a href="pending-operator.php?token=<?= $row['rememberToken']; ?>"
                                                    class="inline-flex items-center justify-center bg-yellow-600 hover:bg-yellow-700 text-white font-medium rounded-md text-xs px-2 sm:px-3 py-1.5 w-full sm:w-auto transition-colors duration-200">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                                    </svg>
                                                    <span class="whitespace-nowrap">View Details</span>
                                                </a>
                                            </td>
                                        </tr>
                                <?php
                                    }
                                }
                            } catch (PDOException $e) {
                                ?>
                                <tr>
                                    <td colspan="6" class="px-4 sm:px-6 py-4 text-center">
                                        <div class="bg-red-50 border border-red-200 rounded-lg p-3 inline-block mx-auto">
                                            <p class="text-sm text-red-600 font-medium">
                                                <i class="fas fa-exclamation-circle mr-1"></i> Error: <?= htmlspecialchars($e->getMessage()); ?>
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                            <?php
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Registered Tab -->
            <div class="hidden p-4 rounded-lg" id="styled-dashboard" role="tabpanel" aria-labelledby="dashboard-tab">
                <div class="overflow-x-auto mt-4 rounded-lg">
                    <table id="registered-table" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-responsive">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Username</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Full Name</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Email</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Operator Type</th>
                                <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Status</th>
                                <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php
                            try {
                                $fetchAccountType = "user";
                                $fetchAccountStatus = "Activated";
                                $fetAccountStatus2 = "Deactivated";
                                $fetAccountStatus3 = "ready";
                                $sql = "SELECT
                                        i.firstname, i.middlename, i.lastname, i.extname, i.operatorType,
                                        a.accType, a.accountStatus, a.email, a.username, a.id, a.rememberToken
                                    FROM
                                        operator_info i
                                    INNER JOIN
                                        operator_account a
                                    ON
                                        i.user_id = a.id
                                    WHERE
                                        a.accType = :accType
                                    AND
                                        (a.accountStatus = :accStatus OR a.accountStatus = :accStatus2 OR a.accountStatus = :accStatus3)";

                                $stmt = $pdo->prepare($sql);
                                $stmt->bindParam(':accType', $fetchAccountType, PDO::PARAM_STR);
                                $stmt->bindParam(':accStatus', $fetchAccountStatus, PDO::PARAM_STR);
                                $stmt->bindParam(':accStatus2', $fetAccountStatus2, PDO::PARAM_STR);
                                $stmt->bindParam(':accStatus3', $fetAccountStatus3, PDO::PARAM_STR);
                                $stmt->execute();

                                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                if ($rows) {
                                    foreach ($rows as $row) {
                                        $fullname = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                                        $badgeStatus = $row['accountStatus'] === "Activated"
                                            ? '<span class="inline-flex items-center bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full"><span class="w-2 h-2 me-1 bg-green-500 rounded-full"></span>Active</span>'
                                            : ($row['accountStatus'] === "Deactivated"
                                                ? '<span class="inline-flex items-center bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full"><span class="w-2 h-2 me-1 bg-red-500 rounded-full"></span>Deactivated</span>'
                                                : '<span class="inline-flex items-center bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full"><span class="w-2 h-2 me-1 bg-yellow-500 rounded-full"></span>Ready</span>');
                            ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($row['username']); ?></td>
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500"><?= htmlspecialchars($fullname); ?></td>
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500"><?= htmlspecialchars($row['email']); ?></td>
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500"><?= htmlspecialchars($row['operatorType']); ?></td>
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500"><?= $badgeStatus; ?></td>
                                            <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-center">
                                                <div class="flex items-center justify-center space-x-2">
                                                    <button data-modal-target="change-status-modal-<?= $row['id']; ?>" data-modal-toggle="change-status-modal-<?= $row['id']; ?>" class="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md text-xs px-2 py-1.5 transition-colors duration-200">
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
                                                        </svg>
                                                    </button>
                                                    <button data-modal-target="reset-password-modal-<?= $row['id']; ?>" data-modal-toggle="reset-password-modal-<?= $row['id']; ?>" class="inline-flex items-center justify-center bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md text-xs px-2 py-1.5 transition-colors duration-200">
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5V6.75a4.5 4.5 0 1 1 9 0v3.75M3.75 21.75h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H3.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>

                                        <!-- Change Status Modal -->
                                        <div id="change-status-modal-<?= $row['id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                            <div class="relative p-4 w-full max-w-md max-h-full">
                                                <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                                    <!-- Modal Header -->
                                                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-600 to-blue-800 rounded-t-lg">
                                                        <div class="flex items-center">
                                                            <i class="fas fa-exchange-alt text-white mr-2"></i>
                                                            <h3 class="text-lg font-semibold text-white">
                                                                Change Account Status
                                                            </h3>
                                                        </div>
                                                        <button type="button" class="text-white hover:text-gray-200 rounded-full w-7 h-7 flex justify-center items-center transition-colors duration-200 bg-blue-800 hover:bg-blue-900" data-modal-hide="change-status-modal-<?= $row['id']; ?>">
                                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                            </svg>
                                                            <span class="sr-only">Close modal</span>
                                                        </button>
                                                    </div>

                                                    <div class="p-4 md:p-5 text-center">
                                                        <form action="inc/inc.personnel.php" method="POST">
                                                            <div class="bg-yellow-50 p-4 rounded-lg mb-4">
                                                                <div class="flex justify-center mb-3">
                                                                    <div class="bg-yellow-100 rounded-full p-2">
                                                                        <svg class="w-6 h-6 text-yellow-600" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                                        </svg>
                                                                    </div>
                                                                </div>
                                                                <h3 class="text-lg font-medium text-gray-800 mb-2">
                                                                    Confirmation
                                                                </h3>
                                                                <p class="text-sm text-gray-600 mb-3">
                                                                    Are you sure you want to <span class="text-red-600 font-bold">change the current status</span> of this account?
                                                                </p>
                                                            </div>

                                                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                            <input type="hidden" value="<?= $row['id']; ?>" name="rowId">
                                                            <input type="hidden" value="<?= $rowStatus; ?>" name="rowStatus">
                                                            <input type="hidden" name="id" value="<?= $row['id']; ?>">

                                                            <div class="flex justify-center space-x-3">
                                                                <button data-modal-hide="change-status-modal-<?= $row['id']; ?>" type="button" class="flex items-center py-2 px-4 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                                    <i class="fas fa-times-circle mr-1.5"></i> Cancel
                                                                </button>
                                                                <button type="submit" name="changeStatusBtn" class="flex items-center py-2 px-4 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 transition-all duration-200">
                                                                    <i class="fas fa-check-circle mr-1.5"></i> Confirm
                                                                </button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Reset Password Modal -->
                                        <div id="reset-password-modal-<?= $row['id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                            <div class="relative p-4 w-full max-w-md max-h-full">
                                                <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                                                    <!-- Modal Header -->
                                                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-600 to-gray-800 rounded-t-lg">
                                                        <div class="flex items-center">
                                                            <i class="fas fa-key text-white mr-2"></i>
                                                            <h3 class="text-lg font-semibold text-white">
                                                                Reset Password
                                                            </h3>
                                                        </div>
                                                        <button type="button" class="text-white hover:text-gray-200 rounded-full w-7 h-7 flex justify-center items-center transition-colors duration-200 bg-gray-700 hover:bg-gray-900" data-modal-hide="reset-password-modal-<?= $row['id']; ?>">
                                                            <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                            </svg>
                                                            <span class="sr-only">Close modal</span>
                                                        </button>
                                                    </div>

                                                    <div class="p-4 md:p-5 text-center">
                                                        <form action="inc/inc.personnel.php" method="POST">
                                                            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                                                                <div class="flex justify-center mb-3">
                                                                    <div class="bg-gray-100 rounded-full p-2">
                                                                        <i class="fas fa-lock text-gray-600 text-xl"></i>
                                                                    </div>
                                                                </div>
                                                                <h3 class="text-lg font-medium text-gray-800 mb-2">
                                                                    Password Reset Confirmation
                                                                </h3>
                                                                <p class="text-sm text-gray-600 mb-3">
                                                                    Are you sure you want to <span class="text-red-600 font-bold">reset the password</span> of this account?
                                                                </p>
                                                                <p class="text-xs text-gray-500 italic">
                                                                    This action will reset the password to the default value.
                                                                </p>
                                                            </div>

                                                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                            <input type="hidden" value="<?= $row['id']; ?>" name="rowId">
                                                            <input type="hidden" value="<?= $row['username']; ?>" name="rowUsername">
                                                            <input type="hidden" name="id" value="<?= $row['id']; ?>">

                                                            <div class="flex justify-center space-x-3">
                                                                <button data-modal-hide="reset-password-modal-<?= $row['id']; ?>" type="button" class="flex items-center py-2 px-4 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                                    <i class="fas fa-times-circle mr-1.5"></i> Cancel
                                                                </button>
                                                                <button type="submit" name="resetPasswordBtn" class="flex items-center py-2 px-4 text-sm font-medium text-white bg-gray-600 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-400 transition-all duration-200">
                                                                    <i class="fas fa-check-circle mr-1.5"></i> Confirm Reset
                                                                </button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                <?php
                                    }
                                }
                            } catch (PDOException $e) {
                                ?>
                                <tr>
                                    <td colspan="6" class="px-4 sm:px-6 py-4 text-center">
                                        <div class="bg-red-50 border border-red-200 rounded-lg p-3 inline-block mx-auto">
                                            <p class="text-sm text-red-600 font-medium">
                                                <i class="fas fa-exclamation-circle mr-1"></i> Error: <?= htmlspecialchars($e->getMessage()); ?>
                                            </p>
                                        </div>
                                    </td>
                                </tr>
                            <?php
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require '_footer.php';
?>