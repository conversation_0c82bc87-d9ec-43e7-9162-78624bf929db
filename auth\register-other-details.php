<?php
session_start();
define('ALLOW_ACCESS', true);
include '../connection/dbconnect.php';

// Redirect to login.php if session variables are not set
if (empty($_SESSION['operator']) || empty($_SESSION['id'])) {
    header("Location: ../login.php");
    exit();
}

// Generate a CSRF token if it doesn't exist or has expired
if (empty($_SESSION['csrf_token']) || time() > $_SESSION['csrf_token_expiration']) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    $_SESSION['csrf_token_expiration'] = time() + 3600; // 1-hour validity
}

$op = $_SESSION['operator'];
$id = $_SESSION['id'];

// Combined query to fetch operator info and account status
$sql = 'SELECT
            i.firstname, i.middlename, i.lastname, i.extname,
            a.accountStatus
        FROM
            operator_info i
        INNER JOIN
            operator_account a
        ON
            i.user_id = a.id
        WHERE
            i.user_id = :id';

$stmt = $pdo->prepare($sql);
$stmt->bindParam(':id', $id, PDO::PARAM_INT);
if (!$stmt->execute()) {
    error_log("Error executing combined query: " . implode(", ", $stmt->errorInfo()));
    die("An error occurred. Please try again later.");
}

$row = $stmt->fetch(PDO::FETCH_ASSOC);
if (!$row) {
    die("Error: User not found.");
}

// Check account status
$accountStatus = $row['accountStatus'];
if ($accountStatus === "pending") {
    header('Location: registration-complete.php');
    exit();
} elseif ($accountStatus === "Incomplete") {
    $name = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
} else {
    header('Location: ../login.php');
    exit();
}
?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.css" rel="stylesheet" />
    <title>Complete Registration - <?= $op; ?></title>
</head>

<body class="bg-gray-50">
    <header class="bg-blue-600 text-white p-6 shadow-md">
        <div class="container mx-auto flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold uppercase">Hello, <?= $name; ?></h1>
                <p class="text-sm">Account Type: <?= $op; ?></p>
            </div>
            <a href="Logout.php" class="bg-white text-blue-600 px-4 py-2 rounded-md font-semibold hover:bg-blue-100 transition">
                Logout
            </a>
        </div>
    </header>

    <div class="container mx-auto p-6 bg-white rounded-lg shadow-md my-6 max-w-6xl">
        <!-- Progress Steps -->
        <div class="mb-8">
            <ol class="flex items-center w-full">
                <li class="flex w-full items-center text-blue-600 dark:text-blue-500 after:content-[''] after:w-full after:h-1 after:border-b after:border-blue-100 after:border-4 after:inline-block dark:after:border-blue-800">
                    <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full lg:h-12 lg:w-12 dark:bg-blue-800 shrink-0">
                        <svg class="w-4 h-4 text-blue-600 lg:w-6 lg:h-6 dark:text-blue-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                        </svg>
                    </div>
                    <span class="ml-2 text-sm font-medium">Personal Information</span>
                </li>
                <li class="flex items-center text-blue-600">
                    <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full lg:h-12 lg:w-12 shrink-0">
                        <svg class="w-4 h-4 text-blue-600 lg:w-6 lg:h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M18 7.5h-.423l-.452-1.09.5-.444a1.5 1.5 0 0 0 0-2.121L16.56 2.78a1.5 1.5 0 0 0-2.12 0l-.445.5L12.904.827A1.5 1.5 0 0 0 11.5.5h-3a1.5 1.5 0 0 0-1.404.827L6.644 2.78l-.444-.5a1.5 1.5 0 0 0-2.121 0L3.015 3.345a1.5 1.5 0 0 0 0 2.121l.5.444-.452 1.09H2.5A1.5 1.5 0 0 0 1 8.5v3A1.5 1.5 0 0 0 2.5 13h.423l.452 1.09-.5.444a1.5 1.5 0 0 0 0 2.121l1.065 1.065a1.5 1.5 0 0 0 2.12 0l.445-.5 1.09.452V18A1.5 1.5 0 0 0 8.5 19h3a1.5 1.5 0 0 0 1.404-.827l.452-1.09.444.5a1.5 1.5 0 0 0 2.121 0l1.065-1.065a1.5 1.5 0 0 0 0-2.121l-.5-.444.452-1.09H18a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 18 7.5ZM10 13a3 3 0 1 1 0-6 3 3 0 0 1 0 6Z"/>
                        </svg>
                    </div>
                    <span class="ml-2 text-sm font-medium">Operator Details</span>
                </li>
            </ol>
        </div>

        <h2 class="text-3xl mb-6 text-gray-900 font-bold">Complete Your Registration</h2>
        <p class="text-gray-600 mb-8">Please provide the additional information required for your <?= $op; ?> account.</p>

        <main>
            <?php
            // Convert operator type to lowercase for case-insensitive comparison
            $opLower = strtolower($op);

            if (strpos($opLower, 'tour operator') !== false) {
                include 'tour_operator.php';
            } elseif (strpos($opLower, 'resort operator') !== false) {
                include 'resort_operator.php';
            } elseif (strpos($opLower, 'boat operator') !== false) {
                include 'boat_operator.php';
            } else {
                // Fallback with debug information
                echo '<div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4" role="alert">';
                echo '<p class="font-bold">Debug Information</p>';
                echo '<p>Operator type not recognized: "' . htmlspecialchars($op) . '"</p>';
                echo '<p>Please contact support with this information.</p>';
                echo '</div>';

                // Default to tour operator as fallback
                include 'tour_operator.php';
            }
            ?>
        </main>
    </div>

    <footer class="bg-blue-600 text-white p-6 mt-8 shadow-inner">
        <div class="container mx-auto text-center">
            <p class="text-sm">Copyright © 2025 Municipality of Vinzons, Camarines Norte | All Rights Reserved.</p>
            <p class="text-xs mt-2">Municipal Tourism and Heritage Operations</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
    <script src="js/upload-preview.js"></script>


    <?php if (isset($_SESSION['error'])): ?>
        <script>
            Swal.fire({
                icon: 'error',
                title: 'Registration Error',
                text: '<?php echo htmlspecialchars($_SESSION['error'], ENT_QUOTES, 'UTF-8'); ?>',
                confirmButtonColor: '#3b82f6',
                confirmButtonText: 'Try Again'
            });
        </script>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
        <script>
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: '<?php echo htmlspecialchars($_SESSION['success'], ENT_QUOTES, 'UTF-8'); ?>',
                confirmButtonColor: '#3b82f6'
            });
        </script>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <script>
        // JavaScript to toggle between English and Tagalog
        const switchElement = document.getElementById('language-switch');
        const reminderTitle = document.getElementById('reminder-title');
        const reminderContent = document.getElementById('reminder-content');

        const translations = {
            en: {
                title: 'Important Reminder',
                content: `
                <ol class="list-decimal ml-6 space-y-3">
                    <li>This is the final step in completing your registration. Please provide the required information below to proceed.</li>
                    <li>If you close this window, you can log in to your account later to continue from where you left off.</li>
                     <li>For the <span class="font-medium text-blue-600">Resort and Tour operators</span>, please enter the name of your registered business name.</li>
                    <li>For the <span class="font-medium text-blue-600">Upload ID</span> section, please upload a valid identification document to verify your identity.</li>
                    <li>For the <span class="font-medium text-blue-600">Upload Permit</span>, kindly upload your Mayor's Permit or Operational Permit issued to your business.</li>
                    <li>Please make sure the uploaded pictures are clear and not blurred or distorted.</li>
                    <li>We will notify you via email once your account has been verified and activated.</li>
                </ol>
            `
            },
            tl: {
                title: 'Mahalagang Paalala',
                content: `
                <ol class="list-decimal ml-6 space-y-3">
                    <li>Ito ang huling hakbang para sa iyong pagpaparehistro. Mangyaring ibigay ang kinakailangang impormasyon sa ibaba upang magpatuloy.</li>
                    <li>Kung isasara mo ang window na ito, maaari kang mag-log in sa iyong account upang bumalik dito.</li>
                    <li>Para sa <span class="font-medium text-blue-600">Resort at Tour operators</span>, mangyaring ilagay ang pangalan ng iyong rehistradong negosyo.</li>
                    <li>Para sa <span class="font-medium text-blue-600">Upload ID</span>, mag-upload ng wastong pagkakakilanlan upang mapatunayan ang iyong pagkakakilanlan.</li>
                    <li>Para sa <span class="font-medium text-blue-600">Upload Permit</span>, mangyaring mag-upload ng Mayor's Permit o Operational Permit na inisyu sa iyong negosyo.</li>
                    <li>Tiyaking malinaw ang mga larawang ia-upload at hindi malabo o distorted.</li>
                    <li>Ipapaalam namin sa iyo sa pamamagitan ng email kapag na-verify at na-activate na ang iyong account.</li>
                </ol>
            `
            }
        };

        switchElement.addEventListener('change', (event) => {
            const language = event.target.checked ? 'tl' : 'en';
            reminderTitle.textContent = translations[language].title;
            reminderContent.innerHTML = translations[language].content;
        });
    </script>
</body>

</html>