<?php
ob_start();
require '_header.php';

$getBookingId = isset($_GET['id']) ? (int) $_GET['id'] : 0;
$statusBooking = "pending";
$statusPayment = "unpaid";

$otherOperators = "Approved";

if (empty($getBookingId)) {
    header("Location: transaction-draft.php");
    exit;
}

$bookingDetails = getBookingDetails($pdo, $getBookingId);
$disContactPassenger = getDistinctTourist($pdo, "contact_number", $getBookingId);
$disAddressPassenger = getDistinctTourist($pdo, "address", $getBookingId);


if ($bookingDetails['resort'] != $otherOperators || $bookingDetails['boat'] != $otherOperators) {
    header("Location: transaction-pending.php");
    exit;
}

if ($bookingDetails['booking_status'] != $statusBooking || $bookingDetails['payment_status'] != $statusPayment) {
    header("Location: transaction-draft.php");
    exit;
} else {

    if ($bookingDetails !== null) {
        $extname = $bookingDetails['extname'] ?? ''; // Use null coalescing operator for defaults
        $middlename = $bookingDetails['middlename'] ?? '';
        $operatorName = trim($bookingDetails['firstname'] . ' ' . $middlename . ' ' . $bookingDetails['lastname'] . ' ' . $extname);
        $designationTour = $bookingDetails['tour_operator_designation'];
        $resortName = $bookingDetails['resort_operator_designation'];
        $referenceNumber = $bookingDetails['referenceNum'];
        $boatName = $bookingDetails['boatName'];
        $portName = $bookingDetails['portName'];
        $portFee = $bookingDetails['port_fee'];
        $checkIn = $bookingDetails['check_in_date'];
        $checkout = $bookingDetails['check_out_date'];
        $adultCount = $bookingDetails['total_adults'];
        $childrenCount = $bookingDetails['total_children'];
    }

    if ($name !== $operatorName) {
        header("Location: transaction-draft.php");
        exit;
    }
}

// Get Count from inserted tourist info
$counts = getTouristAndCrewCounts($pdo, $getBookingId);
$actual_adultCount = $counts['adults'];
$actual_childrenCount = $counts['children'];

$pax_count = $actual_adultCount + $actual_childrenCount;
$total_amount = $actual_adultCount * $portFee;

ob_end_flush();;
?>

<div class="p-4 sm:ml-64">
    <div class="border p-6 rounded-xl shadow-lg bg-white mt-14 transition-all duration-300 hover:shadow-xl">
        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-2">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-semibold text-gray-800 hover:text-blue-600 transition-colors duration-200">
                        <i class="fas fa-home text-blue-500 mr-2"></i>
                        Home
                    </a>
                </li>
                <li aria-current="page" class="flex items-center">
                    <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <a href="transaction-draft.php" class="text-sm font-medium text-gray-600 hover:text-blue-600 transition-colors duration-200">Draft</a>
                </li>
                <li aria-current="page" class="flex items-center">
                    <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none"
                        viewBox="0 0 6 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="m1 9 4-4-4-4" />
                    </svg>
                    <span class="text-sm font-medium text-blue-600">Payment</span>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-blue-100 p-3 rounded-lg mr-4">
                <i class="fas fa-credit-card text-blue-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Booking Payment</h2>
                <p class="text-md text-gray-600">Complete your transaction with secure payment options</p>
            </div>
        </div>

        <!-- Payment Options Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
            <!-- Over the Counter Option -->
            <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center">
                    <div class="bg-white p-2 rounded-full mr-3 shadow-sm">
                        <i class="fas fa-cash-register text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="text-md font-semibold text-blue-800">Over the Counter</h3>
                        <p class="text-sm text-blue-700 mt-1">Pay directly at the municipal office</p>
                    </div>
                </div>
            </div>

            <!-- Bank Transfer Option -->
            <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200 p-4 shadow-sm transition-all duration-300 hover:shadow-md">
                <div class="flex items-center">
                    <div class="bg-white p-2 rounded-full mr-3 shadow-sm">
                        <i class="fas fa-university text-green-600"></i>
                    </div>
                    <div>
                        <h3 class="text-md font-semibold text-green-800">Bank Transfer</h3>
                        <p class="text-sm text-green-700 mt-1">Transfer payment via Landbank</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert -->
        <div class="flex items-center p-4 my-4 text-yellow-800 rounded-lg bg-yellow-50 border border-yellow-200 shadow-sm" role="alert">
            <div class="flex-shrink-0 bg-yellow-100 p-1 rounded-full">
                <i class="fas fa-exclamation-circle text-yellow-600"></i>
            </div>
            <div class="ml-3">
                <span class="font-medium">Important Reminder:</span> For Bank Transfer payments, please take a screenshot of the receipt provided by Landbank as proof of payment.
            </div>
        </div>

        <div class=" mt-6">
            <!-- Invoice Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-6 mb-6">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-3 rounded-lg mr-4 hidden sm:block">
                        <i class="fas fa-file-invoice text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-xl sm:text-2xl font-bold text-gray-800">
                            Invoice
                        </h1>
                        <p class="text-blue-600 font-semibold mt-1"><?= $referenceNumber; ?></p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="inline-flex items-center justify-center bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full">
                        <i class="fas fa-clock mr-1"></i> Pending Payment
                    </div>
                    <p class="text-base font-semibold text-gray-600 mt-2">Calaguas TDF</p>
                </div>
            </div>

            <!-- Bill From / Bill To & Dates -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 text-sm sm:text-base mb-6">
                <!-- Left Column: Bill From -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-user-tie text-blue-500 mr-2"></i>
                        <p class="font-semibold text-gray-700">Bill From:</p>
                    </div>
                    <p class="text-gray-800 ml-6">
                        <?= $operatorName ?> <br>
                        <span class="text-blue-600"><?= $designationTour ?></span>
                    </p>
                    <div class="mt-4 border-t border-gray-200 pt-3">
                        <div class="flex items-center mb-1">
                            <i class="fas fa-calendar-alt text-blue-500 mr-2"></i>
                            <p class="font-semibold text-gray-700">Issued On:</p>
                        </div>
                        <p class="text-gray-800 ml-6"><?= (new DateTime())->format('F d, Y'); ?></p>
                    </div>
                </div>
                <!-- Right Column: Bill To -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-building text-blue-500 mr-2"></i>
                        <p class="font-semibold text-gray-700">Bill To:</p>
                    </div>
                    <p class="text-gray-800 ml-6">
                        LGU VINZONS<br>
                        Treasurer Office
                    </p>
                    <div class="mt-4 border-t border-gray-200 pt-3">
                        <div class="flex items-center mb-1">
                            <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                            <p class="font-semibold text-gray-700">Destination:</p>
                        </div>
                        <p class="text-gray-800 ml-6"><?= $resortName ?></p>
                    </div>
                </div>
            </div>

            <!-- Travel Details Summary -->
            <div class="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-6">
                <div class="flex items-center mb-3">
                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                    <h3 class="font-semibold text-blue-800">Travel Details</h3>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                    <div class="bg-white p-3 rounded-lg border border-blue-100 shadow-sm">
                        <p class="text-xs font-medium text-gray-500 uppercase">Travel Dates</p>
                        <p class="text-sm font-semibold text-gray-800 mt-1">
                            <i class="fas fa-calendar-day text-blue-500 mr-1"></i>
                            <?= (new DateTime(htmlspecialchars($checkIn, ENT_QUOTES, 'UTF-8')))->format('M d') ?> - <?= (new DateTime(htmlspecialchars($checkout, ENT_QUOTES, 'UTF-8')))->format('M d, Y') ?>
                        </p>
                    </div>
                    <div class="bg-white p-3 rounded-lg border border-blue-100 shadow-sm">
                        <p class="text-xs font-medium text-gray-500 uppercase">Port</p>
                        <p class="text-sm font-semibold text-gray-800 mt-1">
                            <i class="fas fa-anchor text-blue-500 mr-1"></i>
                            <?= $portName ?>
                        </p>
                    </div>
                    <div class="bg-white p-3 rounded-lg border border-blue-100 shadow-sm">
                        <p class="text-xs font-medium text-gray-500 uppercase">Total Passengers</p>
                        <p class="text-sm font-semibold text-gray-800 mt-1">
                            <i class="fas fa-users text-blue-500 mr-1"></i>
                            <?= $pax_count ?> people
                        </p>
                    </div>
                </div>
            </div>

            <!-- Invoice Detail Table -->
            <div class="mb-6">
                <h2 class="text-lg font-bold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-receipt text-blue-500 mr-2"></i>
                    Invoice Detail
                </h2>
                <div class="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
                    <table class="w-full border-collapse">
                        <thead class="bg-gray-50 text-gray-600">
                            <tr>
                                <th class="px-4 py-3 text-left font-medium">Type</th>
                                <th class="px-4 py-3 text-center font-medium">Count</th>
                                <th class="px-4 py-3 text-center font-medium">Price</th>
                                <th class="px-4 py-3 text-right font-medium">Total</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-700 divide-y divide-gray-200">
                            <!-- Adults Row -->
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-4 py-3 font-medium">
                                    <div class="flex items-center">
                                        <div class="bg-blue-100 p-1.5 rounded-full mr-2 flex items-center justify-center w-6 h-6">
                                            <i class="fas fa-user text-blue-600 text-xs"></i>
                                        </div>
                                        Adults
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-center"><?= $actual_adultCount ?></td>
                                <td class="px-4 py-3 text-center">₱<?= $portFee ?>.00</td>
                                <td class="px-4 py-3 text-right font-medium">₱<?= $actual_adultCount * $portFee ?>.00</td>
                            </tr>
                            <!-- Children Row -->
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-4 py-3 font-medium">
                                    <div class="flex items-center">
                                        <div class="bg-green-100 p-1.5 rounded-full mr-2 flex items-center justify-center w-6 h-6">
                                            <i class="fas fa-child text-green-600 text-xs"></i>
                                        </div>
                                        Children
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-center"><?= $actual_childrenCount; ?></td>
                                <td class="px-4 py-3 text-center">₱0</td>
                                <td class="px-4 py-3 text-right font-medium">₱0</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- Grand Total - Separated from table -->
                <div class="mt-4 bg-white rounded-lg border border-gray-200 shadow-sm p-4">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold text-gray-700">Grand Total</span>
                        <span class="text-xl font-extrabold text-blue-700">₱<?= $total_amount; ?>.00</span>
                    </div>
                </div>
            </div>

            <!-- Payment Instructions -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mt-6">
                <div class="flex items-center mb-2">
                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                    <h3 class="font-semibold text-gray-800">Payment Instructions</h3>
                </div>
                <p class="text-sm text-gray-600 ml-6">
                    Thank you for your booking. Please complete your payment to confirm your reservation. For any questions regarding payment, please contact the municipal office.
                </p>
            </div>

            <div class="flex flex-col md:flex-row justify-center md:justify-end mt-6 space-y-4 md:space-y-0 md:space-x-4">
                <button data-modal-target="proceed-payment-modal" data-modal-toggle="proceed-payment-modal" type="button" class="inline-flex items-center justify-center w-full md:w-auto bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg text-sm px-6 py-3 shadow-sm hover:shadow-md transition-all duration-300">
                    <i class="fas fa-credit-card mr-2"></i> Proceed to Payment
                </button>

                <!--Proceed Payment Modal -->
                <div id="proceed-payment-modal" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                    <div class="relative p-4 w-full max-w-md max-h-full">
                        <div class="relative bg-white rounded-lg shadow-lg border border-gray-200">
                            <!-- Modal header -->
                            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t border-gray-200 bg-gradient-to-r from-green-600 to-green-700">
                                <div class="flex items-center">
                                    <i class="fas fa-credit-card text-white mr-2"></i>
                                    <h3 class="text-xl font-semibold text-white">
                                        Payment Confirmation
                                    </h3>
                                </div>
                                <button type="button" class="text-white bg-green-800 hover:bg-green-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="proceed-payment-modal">
                                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                    </svg>
                                    <span class="sr-only">Close modal</span>
                                </button>
                            </div>

                            <div class="p-4 md:p-5">
                                <form action="inc/inc.payment.php" method="POST">
                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                    <input type="hidden" name="booking_id" value="<?= $getBookingId; ?>">
                                    <input type="hidden" name="referenceNumber" value="<?= $referenceNumber; ?>">

                                    <!-- Payment Summary -->
                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                                        <div class="flex justify-between items-center mb-2">
                                            <span class="text-sm font-medium text-gray-600">Reference Number:</span>
                                            <span class="text-sm font-bold text-blue-600"><?= $referenceNumber; ?></span>
                                        </div>
                                        <div class="flex justify-between items-center mb-2">
                                            <span class="text-sm font-medium text-gray-600">Total Amount:</span>
                                            <span class="text-sm font-bold text-green-600">₱<?= $total_amount; ?>.00</span>
                                        </div>
                                        <div class="flex justify-between items-center">
                                            <span class="text-sm font-medium text-gray-600">Total Passengers:</span>
                                            <span class="text-sm font-bold text-gray-800"><?= $pax_count; ?> people</span>
                                        </div>
                                    </div>

                                    <!-- Warning Message -->
                                    <div class="flex p-4 mb-4 text-red-800 rounded-lg bg-red-50 border border-red-200" role="alert">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                                        </div>
                                        <div class="ml-3 text-sm font-medium text-red-800">
                                            This action is irreversible. Once you proceed, the booking status will be updated.
                                        </div>
                                    </div>

                                    <!-- Payment Method Selection -->
                                    <div class="mb-4">
                                        <label class="block mb-2 text-sm font-medium text-gray-700">
                                            Payment Method <span class="text-red-600">*</span>
                                        </label>
                                        <div class="grid grid-cols-2 gap-3">
                                            <div class="relative">
                                                <input type="radio" name="paymentMethod" id="counter" value="Over_the_Counter" class="hidden peer" required>
                                                <label for="counter" class="flex flex-col items-center justify-center p-3 text-gray-500 bg-white border border-gray-200 rounded-lg cursor-pointer peer-checked:border-green-600 peer-checked:bg-green-50 hover:text-gray-600 hover:bg-gray-100">
                                                    <i class="fas fa-cash-register text-2xl mb-2 text-gray-400 peer-checked:text-green-600"></i>
                                                    <div class="w-full text-sm font-semibold text-center">Over the Counter</div>
                                                </label>
                                            </div>
                                            <div class="relative">
                                                <input type="radio" name="paymentMethod" id="bank" value="Bank_Transfer" class="hidden peer">
                                                <label for="bank" class="flex flex-col items-center justify-center p-3 text-gray-500 bg-white border border-gray-200 rounded-lg cursor-pointer peer-checked:border-green-600 peer-checked:bg-green-50 hover:text-gray-600 hover:bg-gray-100">
                                                    <i class="fas fa-university text-2xl mb-2 text-gray-400 peer-checked:text-green-600"></i>
                                                    <div class="w-full text-sm font-semibold text-center">Bank Transfer</div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex justify-end space-x-3 mt-6">
                                        <button data-modal-hide="proceed-payment-modal" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 shadow-sm">
                                            <i class="fas fa-times mr-1"></i> Cancel
                                        </button>
                                        <button type="submit" name="proceedBtn" class="text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center shadow-sm hover:shadow-md transition-all duration-300">
                                            <i class="fas fa-check mr-1"></i> Confirm Payment
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
require '_footer.php';
?>
