# Configuration System

This directory contains the simplified configuration system for the Calaguas booking application.

## Configuration Structure

The configuration follows a simple chain: `.env` → `config.php` → `dbconnect.php`

1. **`.env`** - Environment variables (not committed to version control)
2. **`env.php`** - Environment variable loader
3. **`config.php`** - Main configuration file that loads from .env
4. **`dbconnect.php`** - Database connection using config.php
5. **`email.php`** - Email functions using config.php
6. **`security.php`** - Security functions using config.php

## Setup Instructions

1. **Copy the example environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the .env file** with your actual configuration values

3. **Set proper file permissions** (especially for production):
   ```bash
   chmod 0600 .env
   chmod 0644 config.php
   ```

## Security Recommendations

1. **Environment Variables**: Store sensitive information in the .env file
2. **File Permissions**: Protect the .env file with restrictive permissions (0600)
3. **Directory Location**: In production, consider moving .env outside the web root
4. **SSL/TLS**: Enable SSL/TLS for database connections if supported
5. **Version Control**: Never commit .env files to version control

## Environment Variables

The following environment variables are supported:

### Database Configuration
- `DB_HOST` - Database host (default: localhost)
- `DB_PORT` - Database port (default: 3306)
- `DB_NAME` - Database name (default: prod_calaguas)
- `DB_USER` - Database username (default: root)
- `DB_PASSWORD` - Database password (default: empty)

### Application Configuration
- `APP_ENV` - Application environment (default: development)
- `APP_DEBUG` - Debug mode (default: true)

### Email Configuration
- `EMAIL_FROM_ADDRESS` - From email address
- `EMAIL_FROM_NAME` - From name for emails
- `SMTP_HOST` - SMTP server host
- `SMTP_PORT` - SMTP server port
- `SMTP_USERNAME` - SMTP username
- `SMTP_PASSWORD` - SMTP password
- `SMTP_ENCRYPTION` - SMTP encryption (ssl/tls)

### Security Configuration
- `CSRF_TOKEN_EXPIRY` - CSRF token expiry time in seconds

## Deployment Checklist

- [ ] Copied .env.example to .env
- [ ] Updated .env with production values
- [ ] Set proper file permissions on .env
- [ ] Tested database connection
- [ ] Verified email configuration
- [ ] Set APP_ENV to 'production'
- [ ] Set APP_DEBUG to 'false'
