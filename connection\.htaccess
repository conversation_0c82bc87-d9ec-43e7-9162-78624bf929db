# Deny access to all files in this directory
<FilesMatch ".*">
    Order Allow,<PERSON><PERSON> from all
</FilesMatch>

# Specifically deny access to database connection files
<FilesMatch "^(dbconnect\.php)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Disable PHP execution if this directory is somehow accessed
<FilesMatch "\.php$">
    SetHandler default-handler
</FilesMatch>

# Additional security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
