<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'dompdf/dompdf' => array(
            'pretty_version' => 'v3.1.0',
            'version' => '3.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/dompdf',
            'aliases' => array(),
            'reference' => 'a51bd7a063a65499446919286fb18b518177155a',
            'dev_requirement' => false,
        ),
        'dompdf/php-font-lib' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/php-font-lib',
            'aliases' => array(),
            'reference' => '6137b7d4232b7f16c882c75e4ca3991dbcf6fe2d',
            'dev_requirement' => false,
        ),
        'dompdf/php-svg-lib' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/php-svg-lib',
            'aliases' => array(),
            'reference' => 'eb045e518185298eb6ff8d80d0d0c6b17aecd9af',
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'dev_requirement' => false,
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => 'v8.7.0',
            'version' => '8.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'reference' => 'f414ff953002a9b18e3a116f5e462c56f21237cf',
            'dev_requirement' => false,
        ),
    ),
);
