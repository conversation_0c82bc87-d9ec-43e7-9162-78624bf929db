<?php
session_start();
include '../../../../connection/dbconnect.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['addPersonnelBtn'])) {

        try {
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                throw new Exception("Invalid CSRF token.");
            }

            $firstname = strtoupper(trim($_POST['firstname']));
            $middlename = strtoupper(trim($_POST['middlename']));
            $lastname = strtoupper(trim($_POST['lastname']));
            $extname = strtoupper(trim($_POST['extname']));
            $accountType = trim($_POST['accountType']);
            $username = trim($_POST['username']);
            $email = trim($_POST['email']);
            $adminUsername = $_POST['adminUsername'];

            $pdo->beginTransaction();

            // Check if username already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM operator_account WHERE username = :username");
            $stmt->execute([':username' => $username]);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("Username already exists.");
            }

            // Check if email already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM operator_account WHERE email = :email");
            $stmt->execute([':email' => $email]);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception("email already exists.");
            }

            // Generate rememberToken
            $randomBytes = bin2hex(random_bytes(18));
            $csrfToken = $_POST['csrf_token'];
            $rememberToken = hash('sha256', $randomBytes . $csrfToken);
            $accountCreated = date("Y-m-d");

            // Insert into operator_account
            $hashed_password = password_hash($username, PASSWORD_BCRYPT);
            $stmt = $pdo->prepare("INSERT INTO operator_account (username, password, email, accountCreated, accountExpired, rememberToken, accType,accountStatus) VALUES (:username, :password, :email, :accountCreated, :accountExpired, :rememberToken, :accType,:accountStatus)");
            $stmt->execute([
                ':username' => $username,
                ':password' => $hashed_password,
                ':email' => $email,
                ':accountCreated' => $accountCreated,
                ':accountExpired' => null,
                ':rememberToken' => $rememberToken,
                ':accType' => $accountType,
                ':accountStatus' => 'Activated'
            ]);

            $user_id = $pdo->lastInsertId();

            // Insert into operator_info
            $stmt = $pdo->prepare("INSERT INTO operator_info (user_id, firstname, middlename, lastname, extname, address, contact, operatorType, designation) VALUES (:user_id, :firstname, :middlename, :lastname, :extname, :address, :contact, :operatorType, :designation)");
            $stmt->execute([
                ':user_id' => $user_id,
                ':firstname' => $firstname,
                ':middlename' => $middlename,
                ':lastname' => $lastname,
                ':extname' => $extname,
                ':address' => null,
                ':contact' => null,
                ':operatorType' => null,
                ':designation' => null
            ]);

            // For Logs
            $type = "Created - Account";
            $description = "Admin: " . $adminUsername . " created new account for: " . $username . ". Account Type: " . $accountType;
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => $type,
                ':description' => $description
            ]);


            $pdo->commit();

            $_SESSION['success'] = "Personnel account has been created";
            header("Location: ../personnel.php"); // Redirect back to the form
            exit();
        } catch (Exception $e) {
            $pdo->rollBack();
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../personnel.php"); // Redirect back to the form
            exit();
        }
    }
}

// Change Status
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['changeStatusBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['accountType'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $id = $_POST['id'];
            $getStatus = $_POST['rowStatus'];
            $adminUsername = $_POST['adminUsername'];
            $username = $_POST['username'];

            if ($getStatus === "Deactivated") {
                $newStatus = "Activated";
            } else {
                $newStatus = "Deactivated";
            }

            // Start transaction
            $pdo->beginTransaction();

            // Update the empty designation
            $stmt = $pdo->prepare("UPDATE operator_account SET accountStatus = :accountStatus WHERE id = :id");
            $stmt->execute(['accountStatus' => $newStatus, 'id' => $id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update.');
            }

            // For Logs
            $type = "Created - Account";
            $description = "Admin: " . $adminUsername . " " . $newStatus . " the account of employee: " . $username;
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => $type,
                ':description' => $description
            ]);


            // Commit the transaction
            $pdo->commit();

            // Redirect on success
            $_SESSION['success'] = "Account Status changed successfully";
            header("Location: ../personnel.php"); // Redirect back to the form
            exit();
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../personnel.php");
            exit;
        }
    }
}


// Reset Password
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['resetPasswordBtn'])) {
        try {
            // Validate session and CSRF token
            if (!isset($_SESSION['csrf_token'], $_SESSION['accountType'], $_SESSION['id'])) {
                throw new Exception('Session data is missing. Please log in again.');
            }
            if (!hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'] ?? '')) {
                throw new Exception('Invalid CSRF token.');
            }

            $id = $_POST['id'];
            $resetPassword = $_POST['rowUsername'];
            $adminUsername = $_POST['adminUsername'];

            // Start transaction
            $pdo->beginTransaction();

            $hashed_password = password_hash($resetPassword, PASSWORD_BCRYPT);
            // Update the empty designation
            $stmt = $pdo->prepare("UPDATE operator_account SET password = :newPassword WHERE id = :id");
            $stmt->execute(['newPassword' => $hashed_password, 'id' => $id]);

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to update.');
            }

            // For Logs
            $type = "Created - Account";
            $description = "Admin: " . $adminUsername . " reset the password of employee: " . $resetPassword;
            $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
            $stmt->execute([
                ':type' => $type,
                ':description' => $description
            ]);

            // Commit the transaction
            $pdo->commit();

            // Redirect on success
            $_SESSION['success'] = "Reset password successfully";
            header("Location: ../personnel.php"); // Redirect back to the form
            exit();
        } catch (Exception $e) {
            // Rollback on error
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $_SESSION['error'] = $e->getMessage();
            header("Location: ../personnel.php");
            exit;
        }
    }
}
