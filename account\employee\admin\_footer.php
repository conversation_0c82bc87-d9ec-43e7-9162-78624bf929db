<script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.2/dist/flowbite.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/simple-datatables@9.0.3"></script>

<?php if (isset($_SESSION['error'])): ?>
    <script>
        Swal.fire({
            icon: 'error',
            title: 'Oops...',
            text: '<?php echo htmlspecialchars($_SESSION['error'], ENT_QUOTES, 'UTF-8'); ?>',
            confirmButtonText: 'OK',
            confirmButtonColor: '#3085d6', // Set a visible button color
        });
    </script>
    <?php unset($_SESSION['error']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['success'])): ?>
    <script>
        Swal.fire({
            icon: 'success',
            title: 'Done!',
            text: '<?php echo htmlspecialchars($_SESSION['success'], ENT_QUOTES, 'UTF-8'); ?>',
            confirmButtonText: 'OK',
            confirmButtonColor: '#3085d6', // Set a visible button color
        });
    </script>
    <?php unset($_SESSION['success']); ?>
<?php endif; ?>


<script>
    if (document.getElementById("search-table") && typeof simpleDatatables.DataTable !== 'undefined') {
        // Initialize DataTable with built-in UI elements
        const dataTable = new simpleDatatables.DataTable("#search-table", {
            searchable: true,
            sortable: true,
            fixedHeight: false,
            perPage: 10,
            perPageSelect: [10, 25, 50, 100], // Enable built-in per page select with options
            labels: {
                placeholder: "Search...",
                perPage: "{select} entries per page",
                noRows: "No entries found",
                info: "Showing {start} to {end} of {rows} entries"
            }
        });

        // Apply custom styling to DataTable elements
        const dataTableTop = document.querySelector(".dataTable-top");
        if (dataTableTop) {
            dataTableTop.classList.add("mb-4");
        }

        const dataTableSearch = document.querySelector(".dataTable-search");
        if (dataTableSearch) {
            dataTableSearch.classList.add("w-full", "md:w-64");
        }

        const dataTableInput = document.querySelector(".dataTable-input");
        if (dataTableInput) {
            dataTableInput.classList.add("block", "w-full", "p-2", "text-sm", "text-gray-900", "border", "border-gray-300", "rounded-lg", "bg-gray-50", "focus:ring-blue-500", "focus:border-blue-500");
        }

        const dataTableSelector = document.querySelector(".dataTable-selector");
        if (dataTableSelector) {
            dataTableSelector.classList.add("bg-gray-50", "border", "border-gray-300", "text-gray-900", "text-sm", "rounded-lg", "focus:ring-blue-500", "focus:border-blue-500", "block", "p-2", "mr-2");
        }

        const dataTableInfo = document.querySelector(".dataTable-info");
        if (dataTableInfo) {
            dataTableInfo.classList.add("text-sm", "text-gray-500", "mb-4", "md:mb-0");
        }

        const paginationList = document.querySelector(".dataTable-pagination");
        if (paginationList) {
            paginationList.classList.add("flex", "space-x-1");

            // Style pagination buttons
            const paginationItems = paginationList.querySelectorAll("li");
            paginationItems.forEach(item => {
                item.classList.add("px-1");

                const link = item.querySelector("a");
                if (link) {
                    link.classList.add("px-3", "py-1", "rounded-md", "text-sm", "font-medium");

                    if (link.classList.contains("active")) {
                        link.classList.add("bg-blue-600", "text-white");
                    } else {
                        link.classList.add("border", "border-gray-300", "text-gray-700", "hover:bg-gray-50");
                    }
                }
            });
        }
    }

    if (document.getElementById("unregistered-table") && typeof simpleDatatables.DataTable !== 'undefined') {
        const dataTable = new simpleDatatables.DataTable("#unregistered-table", {
            searchable: true,
            sortable: false
        });
    }

    if (document.getElementById("registered-table") && typeof simpleDatatables.DataTable !== 'undefined') {
        const dataTable = new simpleDatatables.DataTable("#registered-table", {
            searchable: true,
            sortable: false
        });
    }




    if (document.getElementById("filter-logs") && typeof simpleDatatables.DataTable !== 'undefined') {
        const dataTable = new simpleDatatables.DataTable("#filter-logs", {
            tableRender: (_data, table, type) => {
                if (type === "print") {
                    return table
                }
                const tHead = table.childNodes[0]
                const filterHeaders = {
                    nodeName: "TR",
                    attributes: {
                        class: "search-filtering-row"
                    },
                    childNodes: tHead.childNodes[0].childNodes.map(
                        (_th, index) => ({
                            nodeName: "TH",
                            childNodes: [{
                                nodeName: "INPUT",
                                attributes: {
                                    class: "datatable-input",
                                    type: "search",
                                    "data-columns": "[" + index + "]"
                                }
                            }]
                        })
                    )
                }
                tHead.childNodes.push(filterHeaders)
                return table
            }
        });
    }
</script>

    <!-- Footer -->
    <footer class="mt-auto p-4 bg-white md:p-6 md:flex md:items-center md:justify-between border-t border-gray-200">
        <span class="text-sm text-gray-500 sm:text-center">© <?= date('Y') ?> <a href="#" class="hover:underline">Calaguas TDF</a>. All Rights Reserved.
        </span>
        <div class="flex mt-4 space-x-6 sm:justify-center md:mt-0">
            <a href="#" class="text-gray-400 hover:text-blue-600">
                <i class="fab fa-facebook-f"></i>
                <span class="sr-only">Facebook page</span>
            </a>
            <a href="#" class="text-gray-400 hover:text-blue-600">
                <i class="fab fa-instagram"></i>
                <span class="sr-only">Instagram page</span>
            </a>
            <a href="#" class="text-gray-400 hover:text-blue-600">
                <i class="fab fa-twitter"></i>
                <span class="sr-only">Twitter page</span>
            </a>
        </div>
    </footer>
</div>
    <!-- Back to top button -->
    <button id="back-to-top" class="fixed bottom-5 right-5 z-50 p-2 bg-blue-600 text-white rounded-full shadow-lg opacity-0 transition-opacity duration-300">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Scripts -->
    <script>
        // Back to top button
        document.addEventListener('DOMContentLoaded', function() {
            const backToTopButton = document.getElementById('back-to-top');

            if (backToTopButton) {
                window.addEventListener('scroll', function() {
                    if (window.scrollY > 300) {
                        backToTopButton.classList.remove('opacity-0');
                        backToTopButton.classList.add('opacity-100');
                    } else {
                        backToTopButton.classList.remove('opacity-100');
                        backToTopButton.classList.add('opacity-0');
                    }
                });

                backToTopButton.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }
        });
    </script>
</body>

</html>