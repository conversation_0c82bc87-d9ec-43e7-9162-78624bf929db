<?php
// Start the session with secure cookie parameters
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);

include 'connection/dbconnect.php';

// Basic rate limiting: track login attempts in the session
if (!isset($_SESSION['login_attempts'])) {
    $_SESSION['login_attempts'] = 0;
} 
$maxAttempts = 5;  // Maximum allowed attempts before temporary block

if ($_SESSION['login_attempts'] >= $maxAttempts) {
    $_SESSION['error'] = "Too many login attempts. Please try again later.";
    header("Location: login.php");
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Clear any previous error message
    unset($_SESSION['error']);

    // Sanitize input; note: password is not filtered to preserve special characters
    $username = trim(filter_input(INPUT_POST, 'username', FILTER_SANITIZE_SPECIAL_CHARS));
    $password = trim($_POST['password']);

    try {
        // Ensure CSRF tokens exist and compare them in constant time
        if (
            empty($_SESSION['csrf_token']) || empty($_POST['csrf_token']) ||
            !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])
        ) {
            throw new Exception("Invalid request. Please try again.");
        }

        // Prepare and execute statement to fetch user info
        $stmt = $pdo->prepare("SELECT a.id, a.password, a.accountStatus, a.accountExpired, a.accType, i.operatorType 
                               FROM operator_account a 
                               LEFT JOIN operator_info i ON a.id = i.user_id 
                               WHERE a.username = :username");
        $stmt->execute([':username' => $username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            $_SESSION['login_attempts']++; // Increment failed attempts
            throw new Exception("Invalid credentials. Please try again.");
        }

        // Verify the password
        if (!password_verify($password, $user['password'])) {
            $_SESSION['login_attempts']++; // Increment failed attempts
            throw new Exception("Invalid credentials. Please try again.");
        }

        // Reset login attempts and regenerate the session ID on successful login
        $_SESSION['login_attempts'] = 0;
        session_regenerate_id(true);

        // Check system maintenance status
        $stmt = $pdo->prepare('SELECT status_maintenance FROM system');
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$row) {
            throw new Exception("Something went wrong. Please try again later.");
        }

        // Maintenance logic: Only admins and mtho staff can bypass maintenance
        if ($row['status_maintenance'] === 1) {
            if (($user['accType'] === "admin" || $user['accType'] === "mtho staff") &&
                $user['accountStatus'] === "Activated"
            ) {
                $_SESSION['id'] = $user['id'];
                $_SESSION['accountType'] = $user['accType'];
                header("Location: account/employee/admin/home.php");
                exit;
            } else {
                throw new Exception("System under maintenance. Please try again later.");
            }
        } else {
            // Process login based on the user's account status
            if ($user['accountStatus'] === "Incomplete") {
                $_SESSION['id']            = $user['id'];
                $_SESSION['operator']      = $user['operatorType'];
                $_SESSION['accountStatus'] = $user['accountStatus'];
                header("Location: auth/register-other-details.php");
                exit();
            }

            if (in_array($user['accountStatus'], ['pending', 'ready', 'declined'])) {
                $_SESSION['id']            = $user['id'];
                $_SESSION['operator']      = $user['operatorType'];
                $_SESSION['accountStatus'] = $user['accountStatus'];
                header("Location: auth/registration-complete.php");
                exit();
            }

            if ($user['accountStatus'] === "Deactivated") {
                throw new Exception("Account is deactivated.");
            }

            if ($user['accountStatus'] === "Activated") {
                $loginStatus = "isLogin";
                $stmt = $pdo->prepare("UPDATE operator_account SET login_status = :loginStatus WHERE id = :id");
                $stmt->execute([
                    ':loginStatus' => $loginStatus,
                    ':id' => $user['id']
                ]);

                $_SESSION['id']         = $user['id'];
                $_SESSION['accountType'] = $user['accType'];
                $_SESSION['username']    = $username;
                $_SESSION['loginStatus'] = $loginStatus;

                if ($user['accType'] === "user") {
                    $operator = $user['operatorType'];
                    if ($operator === "Tour operator") {
                        header("Location: account/operator/tour/home.php");
                        exit;
                    } elseif ($operator === "Resort operator") {
                        header("Location: account/operator/resort/home.php");
                        exit;
                    } else {
                        header("Location: account/operator/boat/home.php");
                        exit;
                    }
                } else {
                    if ($user['accType'] === "admin" || $user['accType'] === "mtho staff") {
                        header("Location: account/employee/admin/home.php");
                        exit;
                    } elseif ($user['accType'] === "treasurer") {
                        header("Location: account/employee/treasurer/home.php");
                        exit;
                    }
                }
            }
        }
    } catch (Exception $e) {
        // Log the error details server-side if needed, but only show a generic error to the user.
        $_SESSION['error'] = $e->getMessage();
        header("Location: login.php");
        exit();
    }
}
