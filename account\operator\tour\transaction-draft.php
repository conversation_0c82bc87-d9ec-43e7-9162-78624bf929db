<?php
require '_header.php';
?>

<div class="p-4 sm:ml-64">
    <div class="mt-16"> <!-- Adjusted for top navbar -->
        <!-- Main Content Section -->
        <div class="border p-4 rounded-lg shadow-lg bg-white mb-6">
            <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-gray-900">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Transactions</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2">Draft & Voucher</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header Section with Icon -->
        <div class="flex items-center mt-6">
            <div class="bg-gray-100 p-3 rounded-lg mr-4">
                 <i class="fas fa-file-alt text-gray-600 text-xl"></i>
            </div>
            <div>
                <h2 class="text-2xl font-bold text-gray-900">Draft & Voucher Bookings</h2>
                <p class="text-sm text-gray-600 mt-1">Bookings awaiting for user's inputs and voucher bookings.</p>
            </div>
        </div>

        <p class="text-sm text-gray-600 mt-2"></p>
        <!-- Table -->
        <div class="mt-4 overflow-hidden">
            <div class="overflow-x-auto rounded-lg">
                <table id="search-table" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-responsive">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Reference</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Resort</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">Boat</th>
                            <th class="px-4 sm:px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                        </tr>
                    </thead>
                <tbody class="divide-y divide-gray-200">
                    <?php
                    try {
                        // SQL query to fetch booking details for both draft and voucher statuses
                        $sql = "SELECT bb.booking_id AS booking_id, bb.referenceNum, bb.booking_status, oi.designation, bob.boatName
                                FROM cb_bookings bb
                                JOIN operator_info oi ON bb.resort_operator_id = oi.user_id
                                JOIN boat_operator_boatlist bob ON bb.boat_id = bob.id
                                WHERE bb.booking_status IN ('draft', 'voucher')";

                        // Prepare and execute the statement
                        $stmt = $pdo->prepare($sql);
                        $stmt->execute();

                        // Fetch the results
                        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                        if ($rows) {
                            foreach ($rows as $row) {
                    ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-blue-600">
                                        <?= htmlspecialchars($row['referenceNum'] ?? 'N/A'); ?>
                                        <?php if ($row['booking_status'] === 'voucher'): ?>
                                            <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                                <svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M2 6a2 2 0 012-2h12a2 2 0 012 2v2a2 2 0 100 4v2a2 2 0 01-2 2H4a2 2 0 01-2-2v-2a2 2 0 100-4V6z"></path>
                                                </svg>
                                                Voucher
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm font-medium text-gray-900"><?= htmlspecialchars($row['designation']); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-gray-500 text-center"><?= htmlspecialchars($row['boatName']); ?></td>
                                    <td class="px-4 sm:px-6 py-3 sm:py-4 text-sm text-center">
                                        <div class="flex flex-col sm:flex-row justify-center sm:space-x-2 space-y-2 sm:space-y-0">
                                            <a href="add-passenger-info.php?id=<?= $row['booking_id']; ?>"
                                                class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium rounded-md text-xs px-2 sm:px-3 py-1.5 w-full sm:w-auto">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
                                                </svg>
                                                <span class="whitespace-nowrap">Edit</span>
                                            </a>
                                            <button type="button" data-modal-target="deleteDraft-modal-<?= $row['booking_id']; ?>" data-modal-toggle="deleteDraft-modal-<?= $row['booking_id']; ?>"
                                                class="inline-flex items-center justify-center bg-red-600 hover:bg-red-700 text-white font-medium rounded-md text-xs px-2 sm:px-3 py-1.5 w-full sm:w-auto">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                                                </svg>
                                                <span class="whitespace-nowrap">Delete</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Delete Modal -->
                                <div id="deleteDraft-modal-<?= $row['booking_id']; ?>" tabindex="-1" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                    <div class="relative p-4 w-full max-w-md max-h-full">
                                        <div class="relative bg-white rounded-lg shadow">
                                            <button type="button" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="deleteDraft-modal-<?= $row['booking_id']; ?>">
                                                <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                </svg>
                                                <span class="sr-only">Close modal</span>
                                            </button>

                                            <div class="p-4 md:p-5 text-center">
                                                <form action="inc/inc.booking.php" method="POST">
                                                    <svg class="mx-auto mb-4 text-gray-400 w-12 h-12" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                    </svg>
                                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                    <input type="hidden" value="<?= $row['booking_id']; ?>" name="booking_id">
                                                    <h3 class="mb-5 text-lg font-normal text-gray-700">
                                                        Are you sure you want to <span class="text-red-600 font-bold">delete</span> booking?
                                                    </h3>
                                                    <button type="submit" name="deleteDraft" class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
                                                        Yes, I'm sure
                                                    </button>
                                                </form>
                                                <button data-modal-hide="deleteDraft-modal-<?= $row['booking_id']; ?>" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200">
                                                    No, cancel
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                    } catch (PDOException $e) {
                        ?>
                        <tr>
                            <td colspan="4" class="px-4 sm:px-6 py-4 text-center">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-3 inline-block mx-auto">
                                    <p class="text-sm text-red-600 font-medium">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </p>
                                </div>
                            </td>
                        </tr>
                    <?php
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>


<?php
require '_footer.php';
?>