<?php
require '_header.php';
?>

<div class="p-4 sm:ml-64">
    <div class="border p-4 rounded-lg shadow-lg bg-white mt-14">
        <!-- Breadcrumb -->
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="home.php" class="inline-flex items-center text-sm font-medium text-gray-700 dark:text-gray-400">
                        <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                        </svg>
                        Home
                    </a>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">User Account</span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">Operator</span>
                    </div>
                </li>
            </ol>
        </nav>

        <h2 class="text-xl font-bold text-gray-900 mt-6">Operator Accounts</h2>
        <p class="text-sm text-gray-600 mt-2">Oversee and manage operator information and roles efficiently</p>
        <!-- Table -->

        <div class="mb-4 border-b border-gray-200 dark:border-gray-700 mt-2">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="default-styled-tab" data-tabs-toggle="#default-styled-tab-content" data-tabs-active-classes="text-purple-600 hover:text-purple-600 dark:text-purple-500 dark:hover:text-purple-500 border-purple-600 dark:border-purple-500" data-tabs-inactive-classes="dark:border-transparent text-gray-500 hover:text-gray-600 dark:text-gray-400 border-gray-100 hover:border-gray-300 dark:border-gray-700 dark:hover:text-gray-300" role="tablist">
                <li class="me-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 rounded-t-lg" id="profile-styled-tab" data-tabs-target="#styled-profile" type="button" role="tab" aria-controls="profile" aria-selected="false">Unregistered</button>
                </li>
                <li class="me-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300" id="dashboard-styled-tab" data-tabs-target="#styled-dashboard" type="button" role="tab" aria-controls="dashboard" aria-selected="false">Registered</button>
                </li>
            </ul>
        </div>
        <div id="default-styled-tab-content">
            <div class="hidden p-4 rounded-lg" id="styled-profile" role="tabpanel" aria-labelledby="profile-tab">
                <div class="mx-auto mt-4">
                    <table id="unregistered-table" class="w-full border-collapse">
                        <thead class="bg-gray-200 text-gray-700">
                            <tr>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Username
                                    </span>
                                </th>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Full name
                                    </span>
                                </th>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Email
                                    </span>
                                </th>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Operator Type
                                    </span>
                                </th>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Status
                                    </span>
                                </th>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Action
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                $fetchAccountType = "user";
                                $fetchAccountStatus = "pending";
                                // SQL query to fetch data
                                $sql = "SELECT 
                                        i.firstname, i.middlename, i.lastname, i.extname, i.operatorType,
                                        a.accType, a.accountStatus, a.email, a.username, a.id, a.rememberToken
                                    FROM 
                                        operator_info i
                                    INNER JOIN 
                                        operator_account a
                                    ON 
                                        i.user_id = a.id
                                    WHERE 
                                        a.accType = :accType
                                    AND
                                        a.accountStatus = :accStatus";

                                $stmt = $pdo->prepare($sql);
                                $stmt = $pdo->prepare($sql);
                                $stmt->bindParam(':accType', $fetchAccountType, PDO::PARAM_STR);
                                $stmt->bindParam(':accStatus', $fetchAccountStatus, PDO::PARAM_STR);
                                $stmt->execute();


                                // Fetch the data
                                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                // Check if there are rows
                                if ($rows) {
                                    foreach ($rows as $row) {
                                        $fullname = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);
                            ?>
                                        <tr class="odd:bg-white even:bg-gray-200">
                                            <td class="px-4 py-2 font-medium text-black"><?= htmlspecialchars($row['username']); ?></td>
                                            <td class="px-4 py-2"><?= htmlspecialchars($fullname); ?></td>
                                            <td class="px-4 py-2"><?= htmlspecialchars($row['email']); ?></td>
                                            <td class="px-4 py-2"><?= htmlspecialchars($row['operatorType']); ?></td>
                                            <td class="px-4 py-2"><span class="bg-yellow-200 text-yellow-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded">Pending</span></td>
                                            <td class="px-4 py-2">
                                                <a href="pending-operator.php?token=<?= $row['rememberToken']; ?>" class="bg-green-700 hover:bg-green-800 text-white font-medium rounded-md text-xs px-3 py-1.5" type="button">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 0 0-1.883 2.542l.857 6a2.25 2.25 0 0 0 2.227 1.932H19.05a2.25 2.25 0 0 0 2.227-1.932l.857-6a2.25 2.25 0 0 0-1.883-2.542m-16.5 0V6A2.25 2.25 0 0 1 6 3.75h3.879a1.5 1.5 0 0 1 1.06.44l2.122 2.12a1.5 1.5 0 0 0 1.06.44H18A2.25 2.25 0 0 1 20.25 9v.776" />
                                                    </svg>
                                                </a>
                                            </td>
                                        </tr>
                                <?php
                                    }
                                }
                            } catch (PDOException $e) {
                                ?>
                                <tr class="odd:bg-white even:bg-gray-50">
                                    <td colspan="5" class="px-6 py-3 text-center">
                                        Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </td>
                                </tr>
                            <?php
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="hidden p-4 rounded-lg" id="styled-dashboard" role="tabpanel" aria-labelledby="dashboard-tab">
                <div class="mx-auto mt-4">
                    <table id="registered-table" class="w-full border-collapse">
                        <thead class="bg-gray-200 text-gray-700">
                            <tr>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Username
                                    </span>
                                </th>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Full name
                                    </span>
                                </th>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Email
                                    </span>
                                </th>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Operator Type
                                    </span>
                                </th>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Status
                                    </span>
                                </th>
                                <th class="px-4 py-2">
                                    <span class="flex items-center">
                                        Action
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                $fetchAccountType = "user";
                                $fetchAccountStatus = "Activated";
                                $fetAccountStatus2 = "Deactivated";
                                $fetAccountStatus3 = "ready";
                                // SQL query to fetch data
                                $sql = "SELECT 
                                        i.firstname, i.middlename, i.lastname, i.extname, i.operatorType,
                                        a.accType, a.accountStatus, a.email, a.username, a.id, a.rememberToken
                                    FROM 
                                        operator_info i
                                    INNER JOIN 
                                        operator_account a
                                    ON 
                                        i.user_id = a.id
                                    WHERE 
                                        a.accType = :accType
                                    AND
                                        (a.accountStatus = :accStatus OR a.accountStatus = :accStatus2 OR a.accountStatus = :accStatus3)";

                                $stmt = $pdo->prepare($sql);
                                $stmt->bindParam(':accType', $fetchAccountType, PDO::PARAM_STR);
                                $stmt->bindParam(':accStatus', $fetchAccountStatus, PDO::PARAM_STR);
                                $stmt->bindParam(':accStatus2', $fetAccountStatus2, PDO::PARAM_STR);
                                $stmt->bindParam(':accStatus3', $fetAccountStatus3, PDO::PARAM_STR);
                                $stmt->execute();


                                // Fetch the data
                                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                // Check if there are rows
                                if ($rows) {
                                    foreach ($rows as $row) {
                                        $fullname = trim($row['firstname'] . ' ' . $row['middlename'] . ' ' . $row['lastname'] . ' ' . $row['extname']);

                                        // For Badges
                                        if ($row['accountStatus'] === "Activated") {
                                            $rowStatus = "Deactivated";
                                            $badgeStatus = '<span class="bg-green-200 text-green-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded">Active</span>';
                                        } elseif ($row['accountStatus'] === "Deactivated") {
                                            $rowStatus = "Activated";
                                            $badgeStatus = '<span class="bg-red-200 text-red-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded">Deactivated</span>';
                                        } else {
                                            $rowStatus = "Deactivated";
                                            $badgeStatus = '<span class="bg-yellow-200 text-yellow-800 text-xs font-medium me-2 px-2.5 py-0.5 rounded">Ready</span>';
                                        }
                            ?>
                                        <tr class="odd:bg-white even:bg-gray-200">
                                            <td class="px-4 py-2 font-medium text-black"><?= htmlspecialchars($row['username']); ?></td>
                                            <td class="px-4 py-2"><?= htmlspecialchars($fullname); ?></td>
                                            <td class="px-4 py-2"><?= htmlspecialchars($row['email']); ?></td>
                                            <td class="px-4 py-2"><?= htmlspecialchars($row['operatorType']); ?></td>
                                            <td class="px-4 py-2"><?= $badgeStatus; ?></td>
                                            <td class="px-4 py-2">
                                                <button data-modal-target="change-status-modal-<?= $row['id']; ?>" data-modal-toggle="change-status-modal-<?= $row['id']; ?>"
                                                    class="bg-blue-700 hover:bg-blue-800 text-white font-medium rounded-md text-xs px-3 py-1.5"
                                                    type="button">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
                                                    </svg>
                                                </button>
                                                <button data-modal-target="reset-password-modal-<?= $row['id']; ?>" data-modal-toggle="reset-password-modal-<?= $row['id']; ?>"
                                                    class="bg-gray-700 hover:bg-gray-800 text-white font-medium rounded-md text-xs px-3 py-1.5"
                                                    type="button">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5V6.75a4.5 4.5 0 1 1 9 0v3.75M3.75 21.75h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H3.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z" />
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                        <div id="change-status-modal-<?= $row['id']; ?>" tabindex="-1"
                                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                            <div class="relative p-4 w-full max-w-md max-h-full">
                                                <div class="relative bg-white rounded-lg shadow">
                                                    <button type="button"
                                                        class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center"
                                                        data-modal-hide="change-status-modal-<?= $row['id']; ?>">
                                                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                        </svg>
                                                        <span class="sr-only">Close modal</span>
                                                    </button>

                                                    <div class="p-4 md:p-5 text-center">
                                                        <form action="inc/inc.personnel.php" method="POST">
                                                            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                            </svg>
                                                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                            <input type="hidden" value="<?= $row['id']; ?>" name="rowId">
                                                            <input type="hidden" value="<?= $rowStatus; ?>" name="rowStatus">
                                                            <h3 class="mb-5 text-lg font-normal text-gray-700">
                                                                Are you sure you want to <span class="text-red-600 text-bold">change the current status</span> of this account?
                                                            </h3>
                                                            <input type="hidden" name="id" value="<?= $row['id']; ?>">
                                                            <button type="submit" name="changeStatusBtn" class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
                                                                Yes, I'm sure
                                                            </button>
                                                        </form>
                                                        <button data-modal-hide="change-status-modal-<?= $row['id']; ?>" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200">
                                                            No, cancel
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="reset-password-modal-<?= $row['id']; ?>" tabindex="-1"
                                            class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
                                            <div class="relative p-4 w-full max-w-md max-h-full">
                                                <div class="relative bg-white rounded-lg shadow">
                                                    <button type="button"
                                                        class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center"
                                                        data-modal-hide="reset-password-modal-<?= $row['id']; ?>">
                                                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
                                                        </svg>
                                                        <span class="sr-only">Close modal</span>
                                                    </button>

                                                    <div class="p-4 md:p-5 text-center">
                                                        <form action="inc/inc.personnel.php" method="POST">
                                                            <svg class="mx-auto mb-4 text-gray-400 w-12 h-12" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                            </svg>
                                                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
                                                            <input type="hidden" value="<?= $row['id']; ?>" name="rowId">
                                                            <input type="hidden" value="<?= $row['username']; ?>" name="rowUsername">
                                                            <h3 class="mb-5 text-lg font-normal text-gray-700">
                                                                Are you sure you want to <span class="text-red-600 text-bold">reset the password</span> of this account?
                                                            </h3>
                                                            <input type="hidden" name="id" value="<?= $row['id']; ?>">
                                                            <button type="submit" name="resetPasswordBtn" class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
                                                                Yes, I'm sure
                                                            </button>
                                                        </form>
                                                        <button data-modal-hide="reset-password-modal-<?= $row['id']; ?>" type="button" class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-300 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200">
                                                            No, cancel
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                <?php
                                    }
                                }
                            } catch (PDOException $e) {
                                ?>
                                <tr class="odd:bg-white even:bg-gray-50">
                                    <td colspan="5" class="px-6 py-3 text-center">
                                        Error: <?= htmlspecialchars($e->getMessage()); ?>
                                    </td>
                                </tr>
                            <?php
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


<?php
require '_footer.php';
?>