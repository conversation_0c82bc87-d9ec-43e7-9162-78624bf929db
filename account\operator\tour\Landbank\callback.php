<?php
/**
 * Simplified Landbank Callback Handler
 */

// Security check
define('LANDBANK_ACCESS', true);

// Include required files
require_once '../../../connection/dbconnect.php';
require_once 'config.php';

// Set response headers
header('Content-Type: text/plain');
header('Cache-Control: no-cache');

// Get callback parameters
$params = [
    'LBPRefNum' => $_GET['LBPRefNum'] ?? '',
    'MerchantRefNum' => $_GET['MerchantRefNum'] ?? '',
    'TrxnAmount' => $_GET['TrxnAmount'] ?? '',
    'LBPConfNum' => $_GET['LBPConfNum'] ?? '',
    'LBPConfDate' => $_GET['LBPConfDate'] ?? '',
    'Checksum' => $_GET['Checksum'] ?? ''
];

// Validate required parameters
$required = ['LBPRefNum', 'MerchantRefNum', 'TrxnAmount', 'LBPConfNum', 'LBPConfDate', 'Checksum'];
foreach ($required as $param) {
    if (empty($params[$param])) {
        echo "ERROR: Missing $param";
        exit;
    }
}

// Verify checksum
$calculatedChecksum = generateCallbackChecksum($params, LANDBANK_SECRET_KEY);
if (strtoupper($calculatedChecksum) !== strtoupper($params['Checksum'])) {
    echo "INVALID_CHECKSUM";
    exit;
}

try {
    // Find booking by reference number
    $referenceNum = $params['MerchantRefNum'];
    $booking = getBookingForPayment($pdo, null); // We'll modify this function

    // Alternative: Find by reference number directly
    $stmt = $pdo->prepare("SELECT booking_id FROM cb_bookings WHERE referenceNum = :ref");
    $stmt->execute([':ref' => $referenceNum]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$result) {
        echo "ERROR: Booking not found";
        exit;
    }

    $bookingId = $result['booking_id'];

    // Update payment status
    updatePaymentStatus($pdo, $bookingId, 'paid');

    // Log successful callback
    logTransaction($pdo, 'CALLBACK-' . $params['LBPRefNum'], $bookingId, 'completed', $params);

    // Update booking approval if needed
    $stmt = $pdo->prepare("UPDATE cb_booking_approvals SET mtho = 'Pending' WHERE booking_id = :booking_id");
    $stmt->execute([':booking_id' => $bookingId]);

    echo "OK";

} catch (Exception $e) {
    error_log("Callback error: " . $e->getMessage());
    echo "ERROR";
}
