<?php
/**
 * Simplified Landbank Payment Processor
 */

// Security check
define('LANDBANK_ACCESS', true);

// Start session
session_start([
    'cookie_secure'   => true,
    'cookie_httponly' => true,
    'cookie_samesite' => 'Strict'
]);

// Include required files
require_once '../../../connection/dbconnect.php';
require_once 'config.php';

/**
 * Send payment request to Landbank
 */
function sendPaymentRequest($params, $pdo, $bookingId) {
    try {
        // Generate transaction ID
        $transactionId = 'TXN-' . date('YmdHis') . '-' . uniqid();

        // Generate checksum
        $params['checksum'] = generatePaymentChecksum($params, LANDBANK_SECRET_KEY);

        // Log transaction
        logTransaction($pdo, $transactionId, $bookingId, 'initiated', $params);

        // Send to Landbank
        $ch = curl_init(LANDBANK_API_URL . '/postpayment');
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($params),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            logTransaction($pdo, $transactionId, $bookingId, 'failed', null, $error);
            throw new Exception("Connection failed: $error");
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            logTransaction($pdo, $transactionId, $bookingId, 'failed', null, "HTTP $httpCode");
            throw new Exception("HTTP Error: $httpCode");
        }

        // Log response
        logTransaction($pdo, $transactionId, $bookingId, 'pending', $response);

        return [
            'success' => true,
            'response' => $response,
            'transaction_id' => $transactionId
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Process payment from booking
 */
function processPayment($bookingId, $pdo) {
    try {
        // Get booking details
        $booking = getBookingForPayment($pdo, $bookingId);
        if (!$booking) {
            throw new Exception("Booking not found");
        }

        if ($booking['payment_status'] === 'paid') {
            throw new Exception("Payment already completed");
        }

        // Prepare payment data
        $params = [
            'trxnamt' => number_format($booking['total_amount'], 2, '.', ''),
            'merchantcode' => LANDBANK_MERCHANT_CODE,
            'bankcode' => 'B000',
            'trxndetails' => 'Tourism Development Fee - ' . $booking['referenceNum'],
            'trandetail1' => $booking['referenceNum'],
            'trandetail2' => $booking['customer_name'],
            'trandetail3' => $booking['contact_number'] ?? '',
            'trandetail4' => $booking['email'] ?? '',
            'trandetail5' => $booking['total_adults'],
            'trandetail6' => $booking['portName'],
            'trandetail7' => $booking['check_in_date'],
            'trandetail8' => $booking['check_out_date'],
            'trandetail9' => $bookingId,
            'trandetail10' => '', 'trandetail11' => '', 'trandetail12' => '',
            'trandetail13' => '', 'trandetail14' => '', 'trandetail15' => '',
            'trandetail16' => '', 'trandetail17' => '', 'trandetail18' => '',
            'trandetail19' => '', 'trandetail20' => '',
            'callbackurl' => LANDBANK_CALLBACK_URL,
            'username' => LANDBANK_USERNAME,
            'password' => LANDBANK_PASSWORD
        ];

        // Send to Landbank
        $result = sendPaymentRequest($params, $pdo, $bookingId);

        if ($result['success'] && str_starts_with($result['response'], '00|')) {
            // Update payment status
            updatePaymentStatus($pdo, $bookingId, 'pending_landbank');

            return [
                'success' => true,
                'redirect_url' => substr($result['response'], 3),
                'transaction_id' => $result['transaction_id']
            ];
        }

        throw new Exception($result['error'] ?? 'Invalid payment response');

    } catch (Exception $e) {
        error_log("Payment error for booking $bookingId: " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Handle payment requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['booking_id'])) {
    // CSRF protection
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'] ?? '', $_POST['csrf_token'])) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Invalid CSRF token']);
        exit;
    }

    $bookingId = filter_input(INPUT_POST, 'booking_id', FILTER_VALIDATE_INT);
    if (!$bookingId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid booking ID']);
        exit;
    }

    $result = processPayment($bookingId, $pdo);
    header('Content-Type: application/json');
    echo json_encode($result);
    exit;
}
